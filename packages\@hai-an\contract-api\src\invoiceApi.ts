import type { RequestClient } from '@vben/request';

import type { ResponseResult } from './types/commont';
import type { InvoiceSearch, InvoiceViewModel } from './types/invoice';

export enum InvoiceType {
  专票,
  普票,
}

export interface InvoiceSubmit {
  amount: number;
  applicant: string;
  applicationDate: string;
  contractCode: string;
  createBy: string;
  createTime: string;
  id: number;
  invoiceDate: string;
  invoiceNo: string;
  invoiceType: InvoiceType;
  isDeleted: boolean;
  orderNo: string;
  payAmount: number;
  payContents: string;
  payDate: string;
  taxRate: number;
  updateBy: string;
  updateTime: string;
  voucherNo: string;
}

export const createInvoiceApi = (request: RequestClient, path: string) => {
  return {
    delete(id: number): Promise<ResponseResult> {
      return request.delete(`${path}/Invoice/${id}`);
    },

    /**
     * Get invoice by ID
     */
    getById(id: number): Promise<InvoiceViewModel> {
      return request.get(`${path}/Invoice/${id}`);
    },

    save(submi: InvoiceSubmit): Promise<any> {
      return request.post(`${path}/Invoice/save`, submi);
    },

    /**
     * Get invoice list
     */
    list(params: InvoiceSearch): Promise<InvoiceViewModel[]> {
      return request.get(`${path}/Invoice/list`, { params });
    },
  };
};
