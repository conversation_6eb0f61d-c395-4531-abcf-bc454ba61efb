<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Designer } from '@coder/vdesigner-form-designer';
import { Button as AButton } from 'ant-design-vue';

const data = reactive({});
const designerRef = ref();
const renderConfig = reactive({});
// designer setting;
const templateSave = ref(false);
const isDev = ref(false);

const onSaveCheck = () => {
  const json = designerRef.value.getDesignData();
  // eslint-disable-next-line no-alert
  alert(JSON.stringify(json));
};
</script>

<template>
  <Page
    description="支持多语言，主题功能集成切换等"
    title="Ant Design Vue组件使用演示"
  >
    <Designer
      ref="designerRef"
      :dev="isDev"
      :form-data="data"
      :render-config="renderConfig"
      :template-save="templateSave"
    >
      <template #menuBar>
        <AButton @click="onSaveCheck">保存（扩展按钮）</AButton>
      </template>
    </Designer>
  </Page>
</template>
