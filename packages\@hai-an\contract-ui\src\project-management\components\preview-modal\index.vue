<script setup lang="ts">
import type { PropType } from 'vue';

import { computed, ref } from 'vue';

import { CheckOutlined } from '@ant-design/icons-vue';
import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON> as ACard,
  Col as <PERSON><PERSON>,
  <PERSON>dal as AModal,
  Row as ARow,
  TabPane as ATabPane,
  Tabs as ATabs,
} from 'ant-design-vue';

import DiaryList from '../../../contracts/components/modal-info/diary/diary-list.vue';
// @ts-ignore - Vue SFC module resolution
import LinkContracts from '../../../contracts/index.vue';
import JournalList from '../../../project-archive-history/index.vue';
// @ts-ignore - Vue SFC module resolution
import BaseInfo from './base-info.vue';

const props = defineProps({
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  itemId: {
    type: Number as PropType<number>,
    default: 0,
  },
  name: {
    type: String as PropType<string>,
    default: '',
  },
  code: {
    type: String as PropType<string>,
    default: '',
  },
});
const emit = defineEmits(['update:visible']);
const isVisible = computed(() => props.visible);
const activeKey = ref('1');
const diaryType = ref('1');

const canceled = () => {
  emit('update:visible', false);
};

const onPreview = (path: string) => {
  console.error('path:', path);
  // props.previewFile && props.previewFile(path)
};

const handleTabChange = (key: number | string) => {
  activeKey.value = String(key);
};
</script>

<template>
  <AModal
    title="项目详细信息"
    :width="1200"
    :visible="isVisible"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <div>
      <ATabs :active-key="activeKey" @update:active-key="handleTabChange">
        <ATabPane key="1" tab="基础信息">
          <BaseInfo :item-id="itemId" @do-cancel="canceled" />
        </ATabPane>
        <ATabPane key="2" tab="合同">
          <ACard>
            <ATabs>
              <ATabPane key="2-1" tab="收款合同">
                <LinkContracts
                  :is-edit="false"
                  :project-archive-code="code"
                  :contract-type="0"
                />
              </ATabPane>
              <ATabPane key="2-2" tab="付款合同">
                <LinkContracts
                  :is-edit="false"
                  :project-archive-code="code"
                  :contract-type="1"
                />
              </ATabPane>
            </ATabs>
          </ACard>
        </ATabPane>
        <ATabPane key="3" tab="日记">
          <DiaryList
            :diary-type="diaryType"
            :business-id="itemId"
            :business-name="name"
            @preview-file="onPreview"
          />
        </ATabPane>
        <ATabPane key="4" tab="文档">
          <div>文档</div>
        </ATabPane>
        <ATabPane key="5" tab="日志">
          <JournalList :is-edit="false" :project-archive-code="code" />
        </ATabPane>
      </ATabs>
      <div style="padding: 20px; margin-top: 20px; background-color: #ececec">
        <ARow type="flex" justify="center">
          <ACol :span="3">
            <AButton type="primary" @click="canceled">
              <CheckOutlined />确定
            </AButton>
          </ACol>
        </ARow>
      </div>
    </div>
  </AModal>
</template>

<style scoped></style>
