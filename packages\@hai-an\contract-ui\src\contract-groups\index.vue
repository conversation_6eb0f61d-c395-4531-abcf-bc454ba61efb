<!-- 供应商管理 -->
<script setup lang="ts">
import type {
  ContractGroupSearch,
  ContractGroupViewModel,
} from '@hai-an/contract-api';

import { computed, createVNode, onMounted, reactive, ref } from 'vue';

import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import {
  createGroupApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  Card as ACard,
  Modal as AModal,
  message,
  Table,
} from 'ant-design-vue';

import Editor from './components/edit.vue';
import ContractForm from './components/form.vue';
import ContractInfo from './components/info.vue';
import { makeColumns } from './components/tableColumn';

const editTitle = ref('');
const infoTitle = ref('');
const isInfo = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const editId = ref(0);
const infoId = ref(0);
const api = createGroupApi(options.request, options.path);
const searchForm = reactive<ContractGroupSearch>({
  page: 1,
  pageSize: 10,
});

const total = ref(0);

const datas = reactive<ContractGroupViewModel[]>([]);

const pagination = computed(() => {
  return {
    pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
    current: searchForm.page,
    pageSize: searchForm.pageSize,
    showQuickJumper: true,
    showSizeChanger: true,
    total: total.value,
    showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
  };
});

const filter = () => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    datas.splice(0);
    datas.push(...res);
    loading.value = false;
  });
  api.count(searchForm).then((res) => {
    total.value = res;
  });
};
const search = () => {
  searchForm.page = 1;
  filter();
};
const onAdd = () => {
  editTitle.value = '新增合同组信息';
  isEdit.value = true;
  editId.value = 0;
};
const onEdit = (val: ContractGroupViewModel) => {
  editTitle.value = '编辑合同组信息';
  isEdit.value = true;
  editId.value = val.id;
};
const onInfo = (val: ContractGroupViewModel) => {
  infoTitle.value = '合同组详细信息';
  isInfo.value = true;
  infoId.value = val.id;
};
const canceled = () => {
  isEdit.value = false;
  isInfo.value = false;
};

const saved = () => {
  canceled();
  filter();
};

const onSearch = (val: any) => {
  Object.assign(searchForm, val); // 赋值到searchForm
  search();
};

// 分页按钮调用
const handleTableChange = (val: any) => {
  searchForm.page = val.current;
  searchForm.pageSize = val.pageSize;
  filter();
};

const onDel = (val: any) => {
  AModal.confirm({
    cancelText: '取消',
    content: () =>
      createVNode(
        'div',
        { style: 'color:red;' },
        '是否确定删除选定信息?删除后将无法恢复！',
      ),
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: '确认删除',
    onCancel() {
      message.info('取消删除！');
    },
    onOk() {
      api.delete(val.id).then((res) => {
        if (res.success) {
          message.success(res.message);
          filter();
        } else {
          message.error(res.message);
        }
      });
    },
    title: () => '删除确认',
  });
};
const columns = reactive(makeColumns(onDel, onEdit, onInfo));
onMounted(() => {
  search();
});
</script>
<template>
  <ACard>
    <ContractForm @on-search="onSearch" />
  </ACard>
  <ACard>
    <AButton type="primary" shape="round" @click="onAdd" size="small">
      <PlusOutlined />新增
    </AButton>

    <Table
      :data-source="datas"
      :pagination="pagination"
      :loading="loading"
      :columns="columns"
      @change="handleTableChange"
    />
  </ACard>
  <AModal
    :title="editTitle"
    :width="1000"
    :open="isEdit"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <Editor :id="editId" @do-save="saved" @do-cancel="canceled" />
  </AModal>

  <AModal
    :title="infoTitle"
    :width="1200"
    :open="isInfo"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    :columns="columns"
    footer=""
  >
    <ContractInfo :id="infoId" @do-cancel="canceled" />
  </AModal>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
