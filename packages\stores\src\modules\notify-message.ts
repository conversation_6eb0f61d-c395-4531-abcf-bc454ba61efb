import { toRaw } from 'vue';

import {
  getMessageType<PERSON>pi,
  getUserMessageApi,
  ReadStatus,
} from '@coder/notify-api';
import { acceptHMRUpdate, defineStore } from 'pinia';

interface NotificationItem {
  avatar: string;
  date: string;
  id: number;
  isRead?: boolean;
  message: string;
  title: string;
}

/**
 * @zh_CN 访问权限相关
 */
export const useNotifyStore = defineStore('coder-notify', {
  actions: {
    async markAllRead() {
      await getUserMessageApi().markAllRead();
      return this.reload();
    },
    async reload() {
      if (this.types.length === 0) {
        await this.reloadType();
      }
      await this.reloadMessage();
    },
    async reloadMessage() {
      const api = getUserMessageApi();

      const searcher = {
        page: 1,
        pageSize: 40,
        status: ReadStatus.unread,
        type: toRaw(this.types),
      };
      return api.list(searcher).then((resp) => {
        const notify = resp.map((t) => {
          return {
            id: t.id,
            avatar: t.avatar,
            date: t.date,
            isRead: t.isRead,
            message: t.message,
            title: t.title,
          };
        });
        this.message.splice(0);
        this.message.push(...notify);
      });
    },
    async reloadType() {
      const api = getMessageTypeApi();
      return api.list({ group: 'vben', page: 1, pageSize: 10 }).then((data) => {
        const typesName = data.map((t) => t.name);
        this.types.splice(0);
        this.types.push(...typesName);
      });
    },
    async makeRead(id: number) {
      const api = getUserMessageApi();
      const message = this.$state.message.find((a) => a.id === id);
      const data = await api.markRead(id, ReadStatus.read);
      if (data.success && message) {
        message.isRead = true;
      }
    },
  },

  state: () => ({
    message: new Array<NotificationItem>(),
    types: new Array<string>(),
  }),
});

// 解决热更新问题
const hot = import.meta.hot;
if (hot) {
  hot.accept(acceptHMRUpdate(useNotifyStore, hot));
}
