<script setup lang="ts">
import type { SupplierViewModel } from '@hai-an/contract-api/src/types/supplier';

import { onMounted, reactive } from 'vue';

import {
  createSupplierApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: 0, type: Number },
});

const api = createSupplierApi(options.request, options.path);
const submitForm = reactive<SupplierViewModel>({
  address: '',
  id: 0,
  name: '',
  owner: '',
  ownerPhone: '',
  remark: '',
});
const dayF = (val: any) => {
  return dayjs(val).format('YYYY-MM-DD HH:mm');
};

const realod = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};
onMounted(() => {
  realod();
});
</script>
<template>
  <div style="padding: 10px; background-color: #ececec">
    <ADescriptions bordered size="small">
      <ADescriptionsItem label="公司全称">
        {{ submitForm.name }}
      </ADescriptionsItem>
      <ADescriptionsItem label="纳税人识别号">
        {{ submitForm.code }}
      </ADescriptionsItem>
      <ADescriptionsItem label="开户银行名称">
        {{ submitForm.bankName }}
      </ADescriptionsItem>
      <ADescriptionsItem label="银行账号">
        {{ submitForm.num }}
      </ADescriptionsItem>
      <ADescriptionsItem label="公司地址">
        {{ submitForm.address }}
      </ADescriptionsItem>
      <ADescriptionsItem label="公司电话">
        {{ submitForm.phone }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建人">
        {{ submitForm.createBy }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建日期">
        {{ dayF(submitForm.createTime) }}
      </ADescriptionsItem>
    </ADescriptions>
  </div>
</template>
