<!-- 一级仓table -->
<script setup lang="ts">
import { reactive } from 'vue';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON>vider as <PERSON><PERSON><PERSON>,
  Table as ATable,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import columnDefined from './tableColumn';

const props = defineProps({
  datas: { default: () => [], type: Array },
  isEidtList: { type: Boolean },
  loading: { type: Boolean },
  pagination: {
    default: () => {
      return {};
    },
    type: Object,
  },
});
const emit = defineEmits([
  'toEdit',
  'toDel',
  'handleTableChange',
  'LinkContract',
  'toInfo',
]);
const datas = computed(() => props.datas);
const dayF = (val) => {
  return dayjs(val).format('YYYY-MM-DD');
};

const columns = reactive(columnDefined);
const toEdit = (val) => {
  emit('toEdit', val);
};
const toDel = (val) => {
  emit('toDel', val);
};
const toInfo = (val) => {
  emit('toInfo', val);
};

const handleTableChange = (pagination) => {
  emit('handleTableChange', pagination);
};
</script>
<template>
  <ATable
    class="ant-table-striped"
    size="middle"
    :row-class-name="
      (_record, index) => (index % 2 === 1 ? 'table-striped' : null)
    "
    bordered
    :row-key="(data) => data.id"
    :pagination="pagination"
    :columns="columns"
    :loading="loading"
    @change="handleTableChange"
    :data-source="datas"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'contractCode'">
        <a @click="toInfo({ record })">{{ record.contractCode }}</a>
      </template>
      <template v-if="column.dataIndex === 'action'">
        <span v-if="!record.isDeleted">
          <AButton type="link" @click="toEdit({ record })">
            <EditOutlined />修改
          </AButton>
          <ADivider type="vertical" />
          <AButton type="link" @click="toDel({ record })">
            <DeleteOutlined />
            删除
          </AButton>
        </span>
        <span v-else>--无操作--</span>
      </template>
      <template v-if="column.dataIndex === 'startDate'">
        {{ record.startDate ? dayF(record.startDate) : '' }}
      </template>
      <template v-if="column.dataIndex === 'endDate'">
        {{ record.endDate ? dayF(record.endDate) : '' }}
      </template>
    </template>
  </ATable>
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
