import type { MessageAction, MessageUserSearcher } from '@coder/notify-api';

import { computed, reactive, ref, toRaw } from 'vue';

import { getMessageTypeApi, getUserMessageApi } from '@coder/notify-api';
import { Modal } from 'ant-design-vue';

export const useMessageList = (type: string) => {
  const api = getUserMessageApi();
  const typeApi = getMessageTypeApi();
  const loading = ref(false);
  const dataSource = reactive<any[]>([]);
  const total = ref(0);
  const columns = reactive<any>([]);
  const searcher = reactive({
    page: 1,
    pageSize: 50,
  } as MessageUserSearcher);

  const commands = new Array<MessageAction>();

  const getTypeSetting = () => {
    if (!type) return;

    typeApi.getByName(type).then((resp) => {
      columns.splice(0);
      columns.push({
        dataIndex: 'action',
        key: 'action',
        title: '',
      });

      if (resp.listColumnDefined) {
        const typeColumns = JSON.parse(resp.listColumnDefined) as any[];
        columns.push(...typeColumns);
      } else {
        Modal.error({ content: '未定义列表列' });
      }

      columns.push({
        dataIndex: 'createTime',
        key: 'createTime',
        title: '创建时间',
      });

      // 解决 命令行
      commands.splice(0);
      commands.push(...resp.commands);
    });
  };

  const pagination = computed(() => {
    return {
      current: toRaw(searcher.page),
      pageSize: toRaw(searcher.pageSize),
      total: total.value,
    };
  });

  const load = () => {
    loading.value = true;
    searcher.type = [type ?? ''];
    api
      .list(searcher)
      .then((resp) => {
        dataSource.splice(0);
        dataSource.push(...resp);
      })
      .then(() => (loading.value = false));

    api.count(searcher).then((resp) => {
      total.value = resp;
    });
  };

  const onSearch = () => {
    searcher.page = 1;
    load();
  };

  getTypeSetting();

  return {
    columns,
    commands,
    dataSource,
    load,
    loading,
    onSearch,
    pagination,
    searcher,
  };
};
