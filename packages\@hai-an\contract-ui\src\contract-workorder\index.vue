<!-- 合同流程工单管理 -->
<script setup lang="ts">
import type { ContractWorkOrderViewModel } from '@hai-an/contract-api';
import type { PaginationProps } from 'ant-design-vue';

import { onMounted, reactive, ref, watch } from 'vue';

import { ProcessInstanceViews } from '@coder/swf-render';
import {
  createWorkOrderApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import { Card as ACard, Modal as AModal } from 'ant-design-vue';

import ContractForm from './components/form.vue';
import ContractTable from './components/table.vue';

const props = withDefaults(defineProps<Props>(), {
  isEdit: true,
  contractCode: '',
});

const api = createWorkOrderApi(options.request, options.path);

const infoTitle = ref('');
const isInfo = ref(false);

const loading = ref(false);

const infoId = ref(0);

interface Props {
  contractCode?: string;
  isEdit?: boolean;
}
const searchForm = reactive({
  page: 0,
  pageSize: 10,
  contractCode: props.contractCode?.trim() || '',
});

const datas = reactive<ContractWorkOrderViewModel[]>([]);
const pagination = reactive({
  pageSize: 10, // 每页中显示10条数据
  pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total: number) => `共有 ${total} 条数据`, // 分页中显示总的数据
  total: 0,
} as PaginationProps);
const filter = () => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    datas.splice(0);
    datas.push(...res);
    loading.value = false;
  });
  api.count(searchForm).then((res) => {
    pagination.total = res;
  });
};

const search = () => {
  searchForm.page = 1;
  filter();
};

const onInfo = (val: any) => {
  infoTitle.value = '供应商详细信息';
  isInfo.value = true;
  infoId.value = val.record.id;
};

const onSearch = (val: any) => {
  Object.assign(searchForm, val); // 赋值到searchForm
  search();
};

// 分页按钮调用
const handleTableChange = (val: any) => {
  pagination.current = val.current;
  pagination.pageSize = val.pageSize;
  searchForm.page = val.current;
  searchForm.pageSize = val.pageSize;
  filter();
};
const currentOrderId = ref<number | string>('');
const isDetailVisible = ref(false);
const onDetail = (workOrderId: number | string) => {
  isDetailVisible.value = true;
  currentOrderId.value = workOrderId;
};

watch(
  () => props.contractCode,
  (newVal) => {
    if (newVal !== undefined) {
      searchForm.contractCode = newVal.trim();
      search();
    }
  },
);

onMounted(() => {
  search();
});
</script>
<template>
  <ACard v-if="props.isEdit">
    <ContractForm @on-search="onSearch" />
  </ACard>
  <ACard>
    <ContractTable
      :datas="datas"
      :pagination="pagination"
      :loading="loading"
      @to-info="onInfo"
      @handle-table-change="handleTableChange"
      @detail="onDetail"
    />
  </ACard>
  <AModal
    :open="isDetailVisible"
    title="工单详细情信息"
    :width="1300"
    @cancel="() => (isDetailVisible = false)"
    footer=""
    :confirm-loading="loading"
    cancel-text="取消"
  >
    <ProcessInstanceViews
      :is-manage="false"
      :id="currentOrderId"
      :show-flow="true"
    />
    <!-- <SwfProcessInstanceDetail v-if="previewUrl" :url="previewUrl" :headers="headers" />-->
  </AModal>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
