import type { ProjectArchiveHistoryViewModel } from '@hai-an/contract-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import { dayF } from '../../util';

export const makeColumns = (emits: any) => {
  return [
    {
      dataIndex: 'projectArchiveCode',
      title: '项目编码',
      width: 200,
    },
    {
      dataIndex: 'projectArchiveName',
      title: '项目名称',
      customRender: ({ record }) => {
        const historyViewModel = record as ProjectArchiveHistoryViewModel;

        return (
          <a onClick={() => emits('toInfo', historyViewModel)}>
            {historyViewModel.projectArchiveName}
          </a>
        );
      },
    },

    {
      dataIndex: 'changeUserName',
      title: '处理账号',
    },
    {
      dataIndex: 'changeComment',
      title: '操作内容',
    },
    {
      customRender: ({ text }) => {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'changeTime',
      title: '处理时间',
    },
    {
      customRender: ({ record }) => {
        const historyViewModel = record as ProjectArchiveHistoryViewModel;
        return (
          <ul>
            {historyViewModel.diff.map((item: any) => {
              return <li>{item}</li>;
            })}
          </ul>
        );
      },
      dataIndex: 'diff',
      title: '改变',
    },
  ] as ColumnType[];
};
