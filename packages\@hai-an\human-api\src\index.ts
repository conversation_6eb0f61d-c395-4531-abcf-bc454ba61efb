import type { RequestClient } from '@vben/request';

export * from './services/humanResourceApi';
export * from './types';
export interface IHaianHumanOption {
  // getToken: { (): string };
  path: string;
  previewHost?: string;
  request: RequestClient;
}

export const haianHumanOption = {} as IHaianHumanOption;

export const api = {
  install: (_app: any, options: IHaianHumanOption) => {
    if (options.path.endsWith('/')) {
      options.path = options.path.slice(
        0,
        Math.max(0, options.path.length - 1),
      );
    }

    Object.assign(haianHumanOption, options);
  },
};

export default api;
