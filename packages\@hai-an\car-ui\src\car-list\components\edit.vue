<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import { CreateCarApi } from '@hai-an/car-api';
import {
  <PERSON>ton as <PERSON>utton,
  Card as ACard,
  Col as ACol,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Row as ARow,
  message,
} from 'ant-design-vue';

import { haianCarOption } from '../../haianCarOption';

const props = defineProps({
  id: { default: () => 0, type: Number },
});
const emit = defineEmits(['doSave', 'doCancel']);
const api = CreateCarApi(haianCarOption.carPath, haianCarOption.request);

const submitForm = reactive({
  carNo: '',
  driverName: '',
  driverPhone: '',
  id: 0,
});

const submitFormRef = ref();
const rules = ref({
  carNo: [{ message: '请输入车牌号码!', required: true, trigger: 'change' }],
  driverName: [{ message: '请输入联系人!', required: true, trigger: 'change' }],
  driverPhone: [
    { message: '请输入电话号码!', required: true, trigger: 'change' },
    {
      message: '请输入数字!',
      pattern: /^(\d*)$/,
      required: true,
      trigger: 'change',
    },
  ],
});
const toSave = () => {
  submitFormRef.value.validate().then(() => {
    api.save(submitForm).then((res) => {
      if (res.success) {
        message.success(res.message);
        emit('doSave');
      } else {
        message.error(res.message);
      }
    });
  });
};
const reload = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};
const doReset = () => {
  if (submitForm.id === 0) {
    const tempForm = {
      name: '',
      sequence: 0,
    };
    Object.assign(submitForm, tempForm);
  } else {
    reload();
  }
};
const doCancel = () => {
  emit('doCancel');
};

onMounted(() => {
  reload();
});
</script>
<template>
  <ACard>
    <AForm
      ref="submitFormRef"
      :label-col="{ span: 4 }"
      :model="submitForm"
      :rules="rules as any"
      :wrapper-col="{ span: 14 }"
      layout="horizontal"
    >
      <AFormItem has-feedback label="车牌号" name="carNo">
        <AInput v-model:value="submitForm.carNo" autocomplete="off" />
      </AFormItem>
      <AFormItem has-feedback label="联系人" name="driverName" validate-first>
        <AInput v-model:value="submitForm.driverName" autocomplete="off" />
      </AFormItem>
      <AFormItem
        has-feedback
        label="电话号码"
        name="driverPhone"
        validate-first
      >
        <AInput v-model:value="submitForm.driverPhone" autocomplete="off" />
      </AFormItem>
    </AForm>
    <ARow justify="center" type="flex">
      <ACol :span="4">
        <AButton @click="doCancel"> <CloseOutlined />取消 </AButton>
      </ACol>
      <ACol :span="4">
        <AButton @click="doReset"> <RedoOutlined />重置 </AButton>
      </ACol>
      <ACol :span="4">
        <AButton type="primary" @click="toSave">
          <CheckOutlined />保存
        </AButton>
      </ACol>
    </ARow>
  </ACard>
</template>
