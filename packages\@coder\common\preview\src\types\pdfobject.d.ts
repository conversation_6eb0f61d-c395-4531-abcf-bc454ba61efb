declare module 'pdfobject' {
  interface PDFObjectOptions {
    assumptionMode?: boolean;
    fallbackLink?: string;
    fastWebView?: boolean;
    forceIframe?: boolean;
    height?: string;
    id?: string;
    omitInlineStyles?: boolean;
    page?: number | string;
    PDFJS_URL?: string;
    pdfOpenParams?: Record<string, number | string>;
    supportRedirect?: boolean;
    suppressConsole?: boolean;
    targetIframe?: string;
    width?: string;
  }

  interface PDFObjectStatic {
    /**
     * Embeds a PDF into a specified HTML element
     * @param url - The URL of the PDF file
     * @param target - The target HTML element selector or HTMLElement
     * @param options - Optional configuration options
     */
    embed(
      url: string,
      target?: HTMLElement | string,
      options?: PDFObjectOptions,
    ): HTMLElement | null;

    /**
     * Detects PDF support
     */
    isSupported(): boolean;

    /**
     * Gets information about PDF support in the current browser
     */
    pdfobjectversion: string;

    /**
     * Checks if the browser supports PDF embedding
     */
    supportsPDFs: boolean;
  }

  const PDFObject: PDFObjectStatic;
  export default PDFObject;
}
