import type { PaginationProps } from 'ant-design-vue';

import { computed, reactive, ref } from 'vue';

import { message, Modal } from 'ant-design-vue';

interface Options {
  callback?: any;
  countApi?: any;
  deleteApi?: any;
  listApi: any;
  searchForm?: any;
}
export const useLists = (options: Options) => {
  const { searchForm, listApi, countApi, deleteApi } = options;
  const formData = reactive({
    page: 1,
    pageSize: 10,
  });
  const loading = ref<boolean>(false);
  const total = ref<number>(0);
  const tableData = ref<any[]>([]);
  const pagination = computed(() => {
    return {
      current: formData.page,
      pageSize: formData.pageSize,
      showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
      total: total.value,
      showQuickJumper: true,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
    };
  }) as PaginationProps;

  const getList = async () => {
    if (!listApi || !countApi)
      return new Error('listApi or countApi is undefined');
    loading.value = true;
    try {
      const param = {
        ...formData,
        ...searchForm,
      };
      const listTask = listApi(param);
      const countTask = countApi(param);
      const [res, count] = await Promise.all([listTask, countTask]);
      loading.value = false;
      tableData.value = res;
      total.value = count;
    } catch {
      loading.value = false;
    }
  };
  /**
   * total(count) 和 列表数据分开使用连个接口返回， 使用 getList 方法
   * total(count) 与 列表数据一起返回 且采用一个接口 使用loadList 方法
   */
  const loadList = async () => {
    loading.value = true;
    try {
      if (!listApi) throw new Error('listApi is undefined');
      const param = {
        ...formData,
        ...searchForm,
      };
      const res = await listApi(param);
      loading.value = false;
      tableData.value = res.rows;
      total.value = res.total;
    } catch {
      loading.value = false;
    }
  };
  const reload = () => {
    formData.page = 1;
    if (listApi && !countApi) {
      return loadList();
    }
    getList();
  };
  const onDelete = async (
    id: number | string,
    tip: string | undefined = '警告-你正在删除',
  ) => {
    if (deleteApi) return new Error('deleteApi is undefined');
    loading.value = true;
    try {
      await deleteApi(id);
      loading.value = false;
      message.success('删除成功');
      reload();
    } catch {
      loading.value = false;
    }

    return;
    Modal.confirm({
      title: `警告-` + `你正在启动流程${tip}`,
      content: '工单一旦删除，将不能够恢复。',
      okText: '删除',
      cancelText: '取消',
      onOk: () => {
        message.success('删除成功');
      },
    });
  };

  const changeTable = (val: any) => {
    formData.page = val.current;
    formData.pageSize = val.pageSize;
    if (listApi && !countApi) {
      return loadList();
    }
    getList();
  };

  return {
    formData,
    loading,
    total,
    tableData,
    getList,
    loadList,
    reload,
    onDelete,
    pagination,
    changeTable,
  };
};

export default useLists;
