import type { IHaianHumanOption } from '@hai-an/human-api';

import humanApi from '@hai-an/human-api';

import { requestClient } from '#/api/request';

const install = (app: any) => {
  const option = {
    path: '/humanResource',
    request: requestClient,
    // getToken() {
    //   const store = useAccessStore();
    //   return `Bearer ${store.accessToken}`;
    // },
  } as IHaianHumanOption;
  app.use(humanApi, option);
};

export default install;
