<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';

import { onMounted, ref } from 'vue';

import { useMitter, useRenderStore, useWidget } from '@coder/vdesigner-core';
import { ContainerContent, FormOptions } from '@coder/vdesigner-form-render';
import { Form } from 'ant-design-vue';

const props = defineProps<WidgetPropsType>();
const { childWidgets, formData, isDesign } = useWidget(
  props.widget,
  props.renderId,
);

const store = useRenderStore(props.renderId);

const formRef = ref();

onMounted(() => {
  /**
   * 对外公开函数。
   */
  useRenderStore(props.renderId).functions.form = {
    // v-designer-form-render clearValidate/resetFields/validate 这些方法. 但是实际执行是由antdv,van的form提供方法.这里就是
    // 把这些方法与实际实现的地方绑定起来.
    clearValidate: () => {
      formRef.value.clearValidate();
    },

    resetFields: () => {
      formRef.value.resetFields();
    },

    validate: () => {
      return formRef.value.validate();
    },
  };
});

const formConfig = props.widget.options as FormOptions;
const renderMitter = useMitter(props.renderId);

renderMitter.emitAddContainer(formRef, childWidgets, props.parentWidget);

onMounted(() => {
  store.containerElements.push(formRef.value);
  renderMitter.emitAddWidget(
    formRef.value.$el,
    props.widget,
    props.parentWidget,
  );
});

const style = ref<{ 'min-height': string }>({
  'min-height': isDesign.value ? `300px` : 'unset',
});
</script>

<template>
  <Form
    v-widget-menu="{ widgetProps: props }"
    ref="formRef"
    :class="{ 'drop-area': isDesign, 'form-design-overlay': isDesign }"
    :colon="formConfig.colon"
    :label-align="formConfig.labelAlign ?? 'left'"
    :label-col="formConfig.labelCol"
    :label-wrap="formConfig.labelWrap"
    :layout="formConfig.layout"
    :model="formData"
    :name="formConfig.name"
    :style="style"
    style="overflow: visible"
  >
    <ContainerContent :render-id="props.renderId" :widget="props.widget" />
  </Form>
</template>
<style lang="scss" scoped>
.form-design-overlay {
  padding: 20px;
  background-color: #eaeaea;
}
</style>
