<script setup lang="ts">
import type { ContractHistoryViewModel } from '@hai-an/contract-api';
import type { PaginationProps } from 'ant-design-vue';

import type { PropType } from 'vue';

import { onMounted, reactive, ref } from 'vue';

import {
  createHistoryApi,
  haianContractOption as options,
} from '@hai-an/contract-api';

import ContractTable from '../../../../contract-history/components/table.vue';

const props = defineProps({
  contractNumber: {
    type: String as PropType<string>,
    default: '',
  },
});

const API = createHistoryApi(options.request, options.path);

const infoTitle = ref('');
const isInfo = ref(false);

const loading = ref(false);

const infoId = ref(0);

const searchForm = reactive({
  page: 1,
  pageSize: 10,
  contractNumber: props.contractNumber,
});
const datas = reactive<ContractHistoryViewModel[]>([]);
const pagination = reactive({
  pageSize: 10, // 每页中显示10条数据
  pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total: number) => `共有 ${total} 条数据`, // 分页中显示总的数据
  total: 0,
} as PaginationProps);

// 分页按钮调用
const handleTableChange = (val: any) => {
  pagination.current = val.current;
  pagination.pageSize = val.pageSize;
  searchForm.page = val.current;
  searchForm.pageSize = val.pageSize;
  loadList();
};

const onInfo = (val: any) => {
  infoTitle.value = '合同日志详细信息';
  infoId.value = val.id;
  isInfo.value = true;
};

const loadList = async () => {
  loading.value = true;
  try {
    const [res, count] = await Promise.all([
      API.list(searchForm),
      API.count(searchForm),
    ]);
    datas.splice(0);
    datas.push(...res);
    pagination.total = count;
    loading.value = false;
  } catch {
    loading.value = false;
  }
};

onMounted(() => {
  loadList();
});
</script>

<template>
  <ContractTable
    :datas="datas"
    :pagination="pagination"
    :loading="loading"
    @to-info="onInfo"
    @handle-table-change="handleTableChange"
  />
</template>

<style scoped></style>
