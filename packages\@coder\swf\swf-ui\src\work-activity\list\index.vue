<script setup lang="ts">
import type {
  WorkActivityListItemViewModel,
  WorkActivitySearcher,
} from '@coder/swf-api';
import type { PaginationProps } from 'ant-design-vue';
import type { Dayjs } from 'dayjs';

import type { PropType } from 'vue';

import { computed, onMounted, reactive, ref, toRaw, watchEffect } from 'vue';

import { useTable } from '@vben/hooks';

import { FormOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { ClipButton } from '@coder/clip-button';
import { createWorkflowApi, WorkActivityStatus } from '@coder/swf-api';
import { WorkProcessNameSelector } from '@coder/swf-designer';
import { swfOption } from '@coder/swf-render';
import { showSubmitResult } from '@coder/swf-render/src/utility';
import {
  Button as AButton,
  Card as ACard,
  Col as ACol,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  RangePicker as ARangePicker,
  Row as ARow,
  Space as ASpace,
  Table as ATable,
  Tag as ATag,
} from 'ant-design-vue';
// ToDO 相关类型
import dayjs from 'dayjs';

import status from './_workActivityStatus.vue';
// TODO:导入column
import workActivityColumns from './columns';
import { canDispose } from './helper';

const props = defineProps({
  tags: { default: () => [], type: Array as PropType<Array<string>> },
  workProcessName: { default: '', type: String },
  workTaskName: { default: '', type: String },
});

const emits = defineEmits(['dispose', 'onDetail']);

const dayF = (val: any) => {
  return dayjs(val).format('YYYY-MM-DD');
};

const showWpNameSearcher = computed(() => !props.workProcessName);

const columns = reactive(workActivityColumns);

const searcherModel = reactive({
  number: '',
  orgs: [],
  page: 1,
  pageSize: 50,
  roles: [],
  statuses: [WorkActivityStatus.UnAssign, WorkActivityStatus.Processing],
  tags: Array.from({ length: 0 }),
  workProcessName: [],
} as WorkActivitySearcher & { tags: Array<any> });

const { dataSource, loading, onSearch, loadData, pagination } = useTable({
  getPageInfo: () => {
    return {
      page: searcherModel.page,
      pageSize: searcherModel.pageSize,
    };
  },
  fetchData: async () => {
    const api = createWorkflowApi(swfOption.request, swfOption.host);
    const data = await api.listWorkActivity(toRaw(searcherModel));
    const count = await api.countWorkActivity(toRaw(searcherModel));
    return {
      data,
      total: count,
    };
  },
});

const pager = computed(() => {
  return {
    current: toRaw(searcherModel.page),
    pageSize: toRaw(searcherModel.pageSize),
    showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
    total: pagination.value.total,
  } as any;
});

const onPagerChanged = (pagination: PaginationProps) => {
  if (pagination.pageSize) searcherModel.pageSize = pagination.pageSize;
  if (pagination.current) searcherModel.page = pagination.current;
  loadData();
};

type RangeValue = [Dayjs, Dayjs];
const createTimetemp = ref<RangeValue>();

const createTimeChange = (val: any) => {
  searcherModel.createTimeStart = '';
  searcherModel.createTimeEnd = '';
  if (val) {
    searcherModel.createTimeStart = dayF(val[0]);
    searcherModel.createTimeEnd = dayF(val[1]);
  }
};

onMounted(() => {
  loadData();
});

const onDispose = (wa: WorkActivityListItemViewModel) => {
  if (wa.status === WorkActivityStatus.UnAssign) {
    const workflowApi = createWorkflowApi(swfOption.request, swfOption.host);
    workflowApi.acceptWorkActivity(wa.id).then((resp) => {
      showSubmitResult(resp);
      if (resp.success) {
        emits('dispose', wa);
      }
    });
  } else {
    emits('dispose', wa);
  }
};

const onDetail = (wa: WorkActivityListItemViewModel) => {
  emits('onDetail', wa);
};
// const onGiveup = (wa: WorkActivityListItemViewModel) => {
//   if (wa.status !== WorkActivityStatus.UnAssign) {
//     const workflowApi = createWorkflowApi(swfOption.request, swfOption.host);
//     workflowApi.giveUpWorkActivity(wa.id).then((resp) => {
//       showSubmitResult(resp);
//       if (resp.success) {
//         loadData();
//       }
//     });
//   }
// };

watchEffect(() => {
  if (props.workProcessName) {
    searcherModel.workProcessName = [];
    searcherModel.workProcessName.push(props.workProcessName);
  }
});

const getTagColor = (type: number) => {
  switch (type) {
    case 0: {
      return 'green';
    }
    case 1: {
      return 'cyan';
    }
    case 2: {
      return 'blue';
    }
  }
};

defineExpose({
  reload: () => loadData(),
});
</script>

<template>
  <ACard :bordered="false">
    <AForm :label-col="{ span: 6 }">
      <ARow :gutter="16">
        <ACol v-if="showWpNameSearcher" :span="6">
          <AFormItem label="流程名称">
            <WorkProcessNameSelector v-model="searcherModel.workProcessName" />
          </AFormItem>
        </ACol>
        <ACol :span="6">
          <AFormItem label="工单号">
            <AInput v-model:value="searcherModel.number" placeholder="工单号" />
          </AFormItem>
        </ACol>
        <ACol :span="6">
          <AFormItem label="状态">
            <status v-model="searcherModel.statuses" />
          </AFormItem>
        </ACol>
        <ACol :span="6">
          <AFormItem label="申请人">
            <AInput
              v-model:value="searcherModel.creator"
              placeholder="申请人"
            />
          </AFormItem>
        </ACol>
        <ACol :span="6">
          <AFormItem label="主题">
            <AInput v-model:value="searcherModel.subject" placeholder="主题" />
          </AFormItem>
        </ACol>
        <ACol :span="6">
          <AFormItem label="创建时间">
            <ARangePicker
              v-model:value="createTimetemp"
              @change="createTimeChange"
            />
          </AFormItem>
        </ACol>

        <slot :search-model="searcherModel" name="filter"></slot>
      </ARow>
      <ARow>
        <ACol :span="24">
          <AFormItem>
            <ASpace>
              <AButton type="primary" @click="onSearch">
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </AButton>
            </ASpace>
          </AFormItem>
        </ACol>
      </ARow>
    </AForm>
  </ACard>
  <ACard class="mt-5">
    <ATable
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pager"
      row-key="id"
      @change="onPagerChanged"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'action'">
          <ASpace>
            <AButton
              v-if="canDispose(record as any)"
              type="primary"
              @click="onDispose(record as any)"
            >
              <template #icon>
                <FormOutlined />
              </template>
              处理
            </AButton>

            <!--<AButton
              v-if="canGiveup(record as any)"
              danger
              type="primary"
              @click="onGiveup(record as any)"
            >
              <template #icon>
                <CloseCircleOutlined />
              </template>
              放弃
            </AButton>-->
          </ASpace>
        </template>

        <template v-if="column.key === 'assignPerformer'">
          <ATag
            v-for="el in record.assignPerformer"
            :key="el.key"
            :color="getTagColor(el.type)"
          >
            {{ el.name || el.key }}
          </ATag>
        </template>
        <template v-if="column.key === 'subject'">
          <ClipButton :value="record.subject" size="small" />
          <AButton type="link" @click="onDetail(record as any)">
            {{ record.subject }}
          </AButton>
        </template>
        <template v-if="column.key === 'number'">
          <ClipButton :value="record.number" size="small" />
          <AButton type="link" @click="onDetail(record as any)">
            {{ record.number }}
          </AButton>
        </template>
      </template>
    </ATable>
  </ACard>
</template>
