<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import {
  createInvoiceApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as <PERSON>utton,
  Card as ACard,
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Row as ARow,
  message,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: () => 0, type: Number },
});

const emit = defineEmits(['doSave', 'doCancel']);

const dateFormat = 'YYYY-MM-DD';

const api = createInvoiceApi(options.request, options.path);

const submitForm = reactive({
  amount: 0,
  applicant: '',
  applicationDate: undefined,
  id: 0,
  invoiceDate: undefined,
  invoiceNo: '',
  orderNo: '',
  payDate: undefined,
  taxRate: 0,
  voucherNo: '',
});

const submitFormRef = ref();
const rules = ref({
  applicant: [{ max: 50, message: '字符串长度最大为50', trigger: 'blur' }],
  applicationDate: [{ message: '请输入发票时间!', required: true }],
  invoiceDate: [{ message: '请输入发票时间!', required: true }],
  invoiceNo: [
    { max: 100, message: '字符串长度最大为100', trigger: 'blur' },
    { message: '请输入发票号码!', required: true, trigger: 'blur' },
  ],

  orderNo: [{ max: 100, message: '字符串长度最大为100', trigger: 'blur' }],
  payDate: [{ message: '请输入发票时间!', required: true }],
  voucherNo: [{ max: 100, message: '字符串长度最大为100', trigger: 'blur' }],
});

const toSave = () => {
  submitFormRef.value.validate().then(() => {
    api.save(submitForm).then((res) => {
      if (res.data.success) {
        message.success(res.data.message);
        emit('doSave');
      } else {
        message.error(res.data.message);
      }
    });
  });
};
const realod = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      submitForm.id = res.data.id;
      Object.assign(submitForm, res.data);
    });
  }
};

const payDateForm = computed({
  get: () => {
    return dayjs(submitForm.payDate);
  },
  set: (v) => {
    submitForm.payDate = v.format(dateFormat);
  },
});

const applicationDateForm = computed({
  get: () => {
    return dayjs(submitForm.applicationDate);
  },
  set: (v) => {
    submitForm.applicationDate = v.format(dateFormat);
  },
});

const invoiceDateForm = computed({
  get: () => {
    return dayjs(submitForm.invoiceDate);
  },
  set: (v) => {
    submitForm.invoiceDate = v.format(dateFormat);
  },
});

const doReset = () => {
  if (submitForm.id === 0) {
    const tempForm = {
      amount: 0,
      applicant: '',
      applicationDate: undefined,
      id: 0,
      invoiceDate: undefined,
      invoiceNo: '',
      orderNo: '',
      payDate: undefined,
      taxRate: 0,
      voucherNo: '',
    };
    Object.assign(submitForm, tempForm);
  } else {
    realod();
  }
};
const doCancel = () => {
  emit('doCancel');
};

onMounted(() => {
  realod();
});

const labelCol = { style: { width: '150px' } };
</script>
<template>
  <ACard>
    <AForm
      ref="submitFormRef"
      layout="horizontal"
      :model="submitForm"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 14 }"
    >
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="发票号码"
            name="invoiceNo"
          >
            <AInput v-model:value="submitForm.invoiceNo" autocomplete="off" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="发票时间"
            name="invoiceDate"
            :format="dateFormat"
          >
            <ADatePicker
              style="width: 265px"
              v-model:value="invoiceDateForm"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="开票申请人"
            name="applicant"
          >
            <AInput v-model:value="submitForm.applicant" autocomplete="off" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="开票申请日期"
            name="applicationDate"
          >
            <ADatePicker
              style="width: 265px"
              v-model:value="applicationDateForm"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="开票税率"
            name="taxRate"
          >
            <AInputNumber
              v-model:value="submitForm.taxRate"
              style="width: 100%"
              :min="0"
              :max="100"
              :step="0.01"
              :formatter="(value) => `${value}%`"
              :parser="(value) => value.replace('%', '')"
            />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="凭证号"
            name="voucherNo"
          >
            <AInput v-model:value="submitForm.voucherNo" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="金额"
            name="amount"
          >
            <AInputNumber
              v-model:value="submitForm.amount"
              style="width: 100%"
              :min="0"
              :step="0.01"
              :formatter="
                (value) => `￥${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              "
              :parser="
                (value) => value.replace(/\$\s?|(,*)/g, '').replace(/￥/g, '')
              "
            />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="支付日期"
            name="payDate"
          >
            <ADatePicker
              style="width: 265px"
              v-model:value="payDateForm"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="所属工单"
            name="orderNo"
          >
            <AInput v-model:value="submitForm.orderNo" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
    </AForm>
    <ARow type="flex" justify="center">
      <ACol :span="4">
        <AButton @click="doCancel"><CloseOutlined />取消 </AButton>
      </ACol>
      <ACol :span="4">
        <AButton @click="doReset"><RedoOutlined />重置 </AButton>
      </ACol>
      <ACol :span="4">
        <AButton type="primary" @click="toSave">
          <CheckOutlined />保存
        </AButton>
      </ACol>
    </ARow>
  </ACard>
</template>
