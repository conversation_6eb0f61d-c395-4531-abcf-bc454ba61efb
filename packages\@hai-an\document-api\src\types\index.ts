export interface PackageMultiFiles {
  fileId: number[];
  name: null | string | undefined;
}
export declare enum PerformerType {
  org = 2,
  role = 1,
  /**
   * 用户
   */
  user = 0,
}
export enum PermissionType {
  管理权限,
  使用权限,
}
export interface FileListItem {
  createTime: string;
  createUser: string;
  deleteFlag: boolean;
  deletePermissions: boolean;
  fileClientId: string;
  fileLength: string;
  folderId: number;
  folderName: string;
  folderPath: string;
  id: number;
  isCommunal: boolean;
  name: string;
  suffixName: string;
  type: string;
  updateTime: string;
  updateUser: string;
}

export interface FileSearcher {
  createTimeEnd?: string;
  createTimeStart?: string;
  createUser?: string;
  deleteFlag?: boolean;
  folderId?: number;
  isManager?: boolean;
  name?: string;
  page?: number;
  pageSize?: number;
}

export interface FolderListItem {
  // 附加信息
  children: FolderListItem[];
  copyFolderId?: number;
  createTime: string;

  createUser: string;

  deleteFlag: boolean;
  deleteTime?: string;
  deleteUser?: string;
  id: number;

  idPath: string;
  isAdmin: boolean;
  isCommunal: boolean;
  isFolderManager: boolean;
  isLeaf: boolean;
  isManager: boolean;
  isMyFolder: boolean;
  leafFlag: boolean;
  managePerformers: PermissionViewModel[];
  name: string;
  namePath: string;
  parent: FolderListItem | undefined;
  parentFolderId: number;
  parentId: number;
  performers: PermissionViewModel[];
  selectable: boolean;
  sequence: number;
  updateTime: string;

  updateUser: string;
}

export interface PermissionViewModel {
  id: number;
  name: string;
  performerType: PerformerType;
  permissionType: PermissionType;
}

export interface FolderSearcher {
  createUser?: string;
  deleteFlag?: boolean;
  isManager?: boolean;
  name?: string;
  page?: number;
  pageSize?: number;
  parentFolderId?: number;
  parentId?: number;
}

export interface FolderSubmit {
  copyFolderId?: number;
  id?: number;
  /** 是否继承父节点的管理权限 */
  inheritPermissionM?: boolean;
  /** 是否继承父节点的查看权限 */
  inheritPermissionQ?: boolean;
  name: string;
  parentFolderId: number;
  sequence?: number;
}
export interface Exchange {
  aId: number;
  bId: number;
}

export interface ResponseMessage {
  message: string;
  success: boolean;
}

export interface UpdateMessage {
  id: number;
  message: string;
  success: boolean;
}

export interface FolderViewModel {
  children?: FolderViewModel[];
  createTime: string;
  createUser: string;
  deleteFlag: boolean;
  deleteTime?: string;
  deleteUser?: string;
  id: number;
  isCommunal: boolean;
  name: string;
  parentId: number;
  updateTime: string;
  updateUser: string;
}
