import type { SupplierViewModel } from '@hai-an/contract-api/src/types/supplier';
import type { TablePaginationConfig } from 'ant-design-vue';

import { computed, reactive, ref } from 'vue';

import {
  createSupplierApi,
  haianContractOption as options,
} from '@hai-an/contract-api';

export const useSupplierList = () => {
  const api = createSupplierApi(options.request, options.path);
  const searchForm = reactive({
    address: '',
    bankName: '',
    code: '',
    isDeleted: '',
    name: '',
    page: 1,
    pageSize: 10,
    phone: '',
    userName: '',
  });
  const loading = ref(false);
  const total = ref(0);
  const dataSource = reactive<SupplierViewModel[]>([]);

  const filter = () => {
    loading.value = true;
    api.list(searchForm).then((res) => {
      dataSource.splice(0);
      dataSource.push(...res);
      loading.value = false;
    });
    api.count(searchForm).then((res) => {
      pagination.value.total = res;
      total.value = res;
    });
  };

  const search = () => {
    searchForm.page = 1;
    filter();
  };

  const pagination = computed(() => {
    return {
      pageSize: searchForm.pageSize,
      pageSizeOptions: ['10', '20', '50', '100'],
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total: number) => `共有 ${total} 条数据`,
      total: total.value,
    } as TablePaginationConfig;
  });

  return {
    searchForm,
    pagination,
    dataSource,
    search,
    filter,
    loading,
    pageChange: (page: TablePaginationConfig) => {
      searchForm.page = page.current as number;
      searchForm.pageSize = page.pageSize as number;
      filter();
    },
  };
};
