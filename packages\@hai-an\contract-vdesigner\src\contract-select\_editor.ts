import type { WidgetOptionEditorSetting } from '@coder/vdesigner-core';

import {
  booleanEditor,
  formItemName,
  javascriptEditor,
  stringEditor,
} from '@coder/vdesigner-core';

import intellisense from './intelisense.d.ts?raw';

export const ContractSelectEditor = () => {
  return {
    name: formItemName('contractSelect'),
    hidden: booleanEditor('是否隐藏'),
    changeEvent: javascriptEditor('changeEvent', intellisense),
    bookType: stringEditor('合同类型'),
    placeholder: stringEditor('请输入合同类型'),
    contractType: stringEditor('合同类型'),
    orgPath: stringEditor('组织路径'),
    isLock: booleanEditor('是否锁定'),
    isMaster: booleanEditor('是否主合同'),
    isWorkloadLock: booleanEditor('是否工作量锁定'),
    label: stringEditor('标签'),
    labelHidden: booleanEditor('标签隐藏'),
  } as Record<string, WidgetOptionEditorSetting>;
};
