<script lang="ts" setup>
import type { CascaderProps } from 'ant-design-vue';

import { onMounted, ref, watch } from 'vue';

import { createOrgApi } from '@coder/system-api';
import { Cascader } from 'ant-design-vue';

import { coderSystemOption } from '../../coderMemberOption';

const props = defineProps({
  value: { default: () => null, type: String },
  placeholder: { type: String, default: 'Please select' },
});
const emits = defineEmits<{
  (e: 'update:value', value: string): void;
  (e: 'change', value: string): void;
}>();

const options = ref<any[]>([]);
const api = createOrgApi(coderSystemOption.request, coderSystemOption.orgPath);
const loadData: CascaderProps['loadData'] = (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1] as any;
  targetOption.loading = true;

  api.getByParentId(targetOption.id).then((res) => {
    const data = res.map((_) => {
      return {
        id: _.id,
        isLeaf: false,
        label: _.name,
        value: _.name,
        children: [],
      };
    });
    targetOption.loading = false;
    targetOption.children = data;
    options.value = [...options.value];
  });
};

onMounted(() => {
  api.getByParentId(0).then((res) => {
    const data = res.map((_) => {
      return {
        id: _.id,
        isLeaf: false,
        label: _.name,
        value: _.name,
        children: [],
      };
    });
    options.value?.push(...data);
  });
});
const selectValue = ref<string[]>([]);
watch(
  () => props.value,
  () => {
    selectValue.value = props.value ? props.value.split('/') : [];
  },
  {
    immediate: true,
  },
);
const onChange = () => {
  const val = selectValue.value.join('/');
  emits('update:value', val);
  emits('change', val);
};
</script>
<template>
  <Cascader
    v-model:value="selectValue"
    :options="options"
    :load-data="loadData"
    :placeholder="placeholder"
    change-on-select
    @change="onChange"
  />
</template>
