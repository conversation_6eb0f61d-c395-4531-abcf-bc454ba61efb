<script setup lang="ts">
import type {
  MessageSettingSubmit,
  MessageTypeSubmit,
} from '@coder/notify-api';

import { onMounted, ref } from 'vue';

import { PubMessageIcon } from '@vben/icons';

import { getMessageManagerApi, getMessageTypeApi } from '@coder/notify-api';
import { PerformerButton } from '@coder/system-ui';
import { FormRender } from '@coder/vdesigner-form-render';
import { Button, Card, Divider, Form, FormItem, message } from 'ant-design-vue';

import Attachment from '../components/attachment/attachment.vue';

const props = defineProps<{ id?: number; typeName: string }>();
const emits = defineEmits(['complete']);

const managerApi = getMessageManagerApi();
const messageTypeApi = getMessageTypeApi();
const form = ref<MessageSettingSubmit>({
  content: '{}',
  fileIds: [],
  id: 0,
  performers: [],
  type: 0,
  typeName: '',
} as MessageSettingSubmit);
const messageType = ref<MessageTypeSubmit>();
const vFormRef = ref();
const formSetting = ref({});
const loadType = () => {
  messageTypeApi.getByName(props.typeName).then((res) => {
    messageType.value = res;
    form.value.typeName = res.name;
    form.value.type = res.id;
    const json = messageType.value?.inputFormDesign || '{}';
    formSetting.value = JSON.parse(json);
    vFormRef.value.setRenderConfig(JSON.parse(json));
  });
};
const load = () => {
  if (props.id === undefined) return;
  managerApi.get(props.id).then((res) => {
    const content = JSON.parse(res.content ?? '{}');
    vFormRef.value.setFormData(content);

    form.value.id = res.id;
    form.value.fileIds = res.fileIds;
    form.value.type = res.type;
    form.value.performers = res.performers;
  });
};

const onPub = () => {
  form.value.content = JSON.stringify(vFormRef.value.getFormData());

  managerApi.save(form.value).then((res) => {
    message.info({ content: '保存成功' });
    form.value.id = res.id;
    if (res.success) emits('complete');
  });
};

onMounted(() => {
  loadType();
  load();
});
</script>

<template>
  <Card>
    <Button type="primary" @click="onPub"> <PubMessageIcon />保存 </Button>
    <Divider />

    <Form>
      <FormItem label="接收用户" name="title">
        <PerformerButton :performers="form.performers" />
      </FormItem>
    </Form>

    <FormRender ref="vFormRef" :render-config="formSetting" />
    <Attachment
      :file-ids="form.fileIds"
      :is-edit="true"
      :message-id="props.id"
    />
  </Card>
</template>
