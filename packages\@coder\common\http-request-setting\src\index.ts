import type { AxiosRequestConfig, AxiosResponseTransformer } from 'axios';

import type { AxiosReqConfig } from './types/index';

import { isArray, isString } from 'lodash-es';

export { default as CoderHttpRequestSetting } from './components/request-setting.vue';

export * from './types/index';

const _templateReplace = function (
  str?: string,
  replaceData?: Record<string, any>,
) {
  if (!str || !replaceData) return;

  const keys = Object.keys(replaceData);
  const dataList = keys.map((key) => {
    return replaceData[key];
  });
  for (const [i, key] of keys.entries()) {
    str = str.replaceAll(
      new RegExp(String.raw`\$\{` + key + String.raw`\}`, 'gm'),
      dataList[i],
    );
  }
  return str;
};

const _replaceObjectString = function (
  obj?: Record<string, any>,
  replaceData?: Record<string, any>,
) {
  if (!obj || !replaceData) return;
  for (const key in obj) {
    if (isString(obj[key])) {
      obj[key] = _templateReplace(obj[key], replaceData);
    }
  }
};

export const converToAxiosCofnig = (
  config: AxiosReqConfig,
  replaceData: Record<string, any> = {},
  extend: AxiosRequestConfig = {},
): AxiosRequestConfig => {
  const setting = {
    data: _replaceObjectString(config.data, replaceData),
    headers: _replaceObjectString(config.headers, replaceData),
    method: config.method,
    params: _replaceObjectString(config.params, replaceData),
    url: _templateReplace(config.url, replaceData),
  } as AxiosRequestConfig;

  const result = Object.assign(setting, extend) as any;

  if (config.transformResponseData) {
    const transforms = new Array<AxiosResponseTransformer>();
    const caller = (data: any) => {
      if (config.transformResponseData) config.transformResponseData(data);
    };
    transforms.push(caller);

    if (result.transformResponse) {
      if (isArray(result.transformResponse)) {
        transforms.push(...result.transformResponse);
      } else {
        transforms.push(result.transformResponse);
      }
    }

    result.transformResponse = transforms;
  }
  return result;
};

export default {
  install: (_: any) => {},
};
