{"name": "@hai-an/car-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "main": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/system-api": "workspace:*", "@hai-an/car-api": "workspace:*", "@hai-an/contract-api": "workspace:*", "@morev/vue-transitions": "^3.0.2", "@vben/locales": "workspace:*", "@vben/request": "workspace:*", "@vben/utils": "workspace:^", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash-es": "catalog:coder", "lru-cache": "catalog:coder", "vue": "catalog:", "vue-clipboard3": "catalog:coder", "vue-draggable-plus": "catalog:coder"}, "devDependencies": {"@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}