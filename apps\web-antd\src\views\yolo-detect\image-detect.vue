<script setup lang="ts">
// YOLO 图像检测页面
import { ref } from 'vue';

const loading = ref(false);

// 图像检测功能
const detectImage = () => {
  // TODO: 实现图像检测功能
};
</script>

<template>
  <div class="image-detect">
    <div class="page-header">
      <h2>图像检测</h2>
      <p>基于 YOLO 算法的图像目标检测</p>
    </div>

    <div class="detect-container">
      <div class="upload-area">
        <p>请上传图像文件进行检测</p>
        <button @click="detectImage" :disabled="loading">
          {{ loading ? '检测中...' : '开始检测' }}
        </button>
      </div>

      <div class="result-area">
        <p>检测结果将在此显示</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.image-detect {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.detect-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.upload-area,
.result-area {
  padding: 30px;
  text-align: center;
  border: 2px dashed #ddd;
  border-radius: 8px;
}

.upload-area button {
  padding: 10px 20px;
  margin-top: 20px;
  color: white;
  cursor: pointer;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.upload-area button:hover:not(:disabled) {
  background-color: #0056b3;
}

.upload-area button:disabled {
  cursor: not-allowed;
  background-color: #ccc;
}
</style>
