<script setup lang="ts">
import type { MessageSettingSubmit } from '@coder/notify-api';
import type { TablePaginationConfig } from 'ant-design-vue';

import { onMounted, ref } from 'vue';

import {
  DeleteIcon as DeleteOutlined,
  EditIcon,
  PreviewIcon,
  RefreshIcon,
  UsersIcon,
} from '@vben/icons';

import { FormRender } from '@coder/vdesigner-form-render';
import {
  Button,
  Card,
  Form,
  FormItem,
  Modal,
  Popconfirm,
  Space,
  Table,
} from 'ant-design-vue';

import { NotifyTypeList } from '../..';
import UserMessageList from '../user-message/user-message.vue';
import { columns } from './column';
import { useMessageList } from './useList';

const emits = defineEmits<{
  (e: 'edit', item: MessageSettingSubmit): void;
}>();

const { dataSource, events, getType, loading, pagination, searcher } =
  useMessageList();
const detailVisible = ref(false);
const formData = ref({});
const formJson = ref({});
const vFormRef = ref();
const search = () => {
  events.onSearch();
};

const onDelete = (record: MessageSettingSubmit) => {
  events.onDelete(record);
};

/**
 * 分页
 * @param paginationInfo
 */
const handelChangePage = (paginationInfo: TablePaginationConfig) => {
  events.onPageChange(
    paginationInfo.current ?? 1,
    paginationInfo.pageSize ?? 50,
  );
};

onMounted(() => {
  search();
});

const onShowDetail = (record: MessageSettingSubmit) => {
  getType(record.type).then((resp) => {
    formData.value = JSON.parse(record.content);
    formJson.value = JSON.parse(resp.detailFormDesign);
    detailVisible.value = true;
  });
};

const handleOk = () => {
  detailVisible.value = false;
};

const onEdit = (record: MessageSettingSubmit) => {
  emits('edit', record);
};

const UserMessageListVisible = ref(false);
const handlerUserMessageList = () => {
  UserMessageListVisible.value = false;
};
const messageId = ref(0);
const onShowUserMessageList = (record: MessageSettingSubmit) => {
  UserMessageListVisible.value = true;
  messageId.value = record.id;
};
</script>

<template>
  <Card :bordered="false">
    <Form layout="inline">
      <FormItem label="类型">
        <NotifyTypeList v-model:value="searcher.type" />
      </FormItem>
      <FormItem>
        <Space>
          <Button type="primary" @click="search"><RefreshIcon /></Button>
        </Space>
      </FormItem>
    </Form>
  </Card>

  <Card :bordered="false" style="margin-top: 10px">
    <Table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @change="handelChangePage"
    >
      <template #bodyCell="{ column, /*text,*/ record }">
        <template v-if="column.key === 'id'">
          <Space>
            <Popconfirm
              cancel-text="取消"
              ok-text="删除"
              title="是否删除本消息?"
              @confirm="onDelete(record as MessageSettingSubmit)"
            >
              <Button danger type="primary">
                <template #icon>
                  <DeleteOutlined />
                </template>
              </Button>
            </Popconfirm>

            <Button @click="onShowDetail(record as MessageSettingSubmit)">
              <template #icon>
                <PreviewIcon />
              </template>
            </Button>

            <Button
              @click="
                () => {
                  onShowUserMessageList(record as MessageSettingSubmit);
                }
              "
            >
              <template #icon>
                <UsersIcon />
              </template>
            </Button>

            <Button
              title="编辑"
              @click="onEdit(record as MessageSettingSubmit)"
            >
              <template #icon>
                <EditIcon />
              </template>
            </Button>
          </Space>
        </template>
      </template>
    </Table>
  </Card>

  <Modal v-model:open="detailVisible" title="详细" @ok="handleOk">
    <FormRender
      ref="vFormRef"
      :form-data="formData"
      :render-config="formJson"
    />
  </Modal>

  <Modal
    v-model:open="UserMessageListVisible"
    title="详细"
    @ok="handlerUserMessageList"
    width="800px"
  >
    <UserMessageList :id="messageId" />
  </Modal>
</template>
