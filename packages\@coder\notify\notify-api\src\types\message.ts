import type { Performer } from '@coder/system-api';

import type { ReadStatus } from '.';

export interface MessageSettingSubmit {
  content: string;
  creator?: string;
  fileIds: string[];
  id: number;
  performers: Performer[];
  refId?: string;
  type: number;
  typeName: string;
}

export interface SimpleSFileViewModel {
  comment: string;
  createTime: string;
  createUser: string;
  fileSize: number;
  id: string;
  name: string;
  refId: string;
}

export interface MessageSearcher {
  page: number;
  pageSize: number;
  type?: string;
  user?: string;
}

export interface MessageViewModel {
  content: string;
  createTime: string;
  creator: string;
  detailFormDesign: string;
  id: number;
  messageTypeId: null | number;
  messageTypeName: string;
  performers: Performer[];
  refId: string;
  status?: ReadStatus;

  type: number;
}
