<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { DownloadOutlined } from '@ant-design/icons-vue';
import { getSizeInfo, IsUniApp, useDownload } from '@coder/common-api';
import {
  Col as ACol,
  DropdownButton as ADropdownButton,
  <PERSON>u as AMenu,
  Menu<PERSON>tem as AMenuItem,
  Popover as APopover,
  Progress as AProgress,
  Row as ARow,
  message,
} from 'ant-design-vue';

type PropType = {
  btnType?: 'link' | 'primary' | undefined;
  headers?: Record<string, any> | undefined;
  size?: 'large' | 'small' | undefined;
  url: string | { (): Promise<string> } | { (): string };
  useLink?: boolean;
};
const props = withDefaults(defineProps<PropType>(), {
  btnType: 'link',
  headers: undefined,
  size: undefined,
  useLink: false,
});

const downloadUrl = ref<string | undefined>(undefined);

const progressBarVisible = ref(false);
const isUniApp = computed(() => IsUniApp() || props.useLink);

const btnType = computed(() => props.btnType || 'link');
const btnSize = computed(() => props.size);

// 控制ajax下载，可以终端下载。
let downloadController: AbortController | undefined;
const fileInfo = {
  blob: undefined,
  fileName: 'file.txt',
} as {
  blob: Blob | undefined;
  fileName: string;
};
const downloadStatus = reactive({
  done: false,
  downloading: false,
  progress: 0,
});
const showContextMenu = computed(() => {
  return downloadStatus.done ? '' : 'coder-download-file-hide';
});
const text = ref('准备下载');

function createA() {
  if (!fileInfo.blob) return;
  const link = document.createElement('a');
  link.href = window.URL.createObjectURL(fileInfo.blob);
  link.download = fileInfo.fileName; // 替换为你想要的文件名
  document.body.append(link);
  link.click();
  link.remove();
}
const OnProgressShow = (visible: boolean) => {
  if (visible) {
    if (downloadStatus.done || isUniApp.value) progressBarVisible.value = false;
  } else {
    if (downloadStatus.downloading) progressBarVisible.value = true;
  }
};
/**
 * 执行下载文件
 */
const executeDownload = () => {
  downloadUrl.value = undefined;
  downloadStatus.done = false;
  downloadStatus.downloading = true;
  downloadStatus.progress = 0;
  progressBarVisible.value = true;
  text.value = '准备下载';
  fileInfo.blob = undefined;

  // 创建新的 AbortController
  downloadController = new AbortController();

  useDownload({
    method: 'get',
    url: props.url,
  })
    .addHeaders(props.headers ?? {})
    .addProgressEvent((e) => {
      const totalSizeInfo = getSizeInfo(e.total);
      const loadSizeInfo = getSizeInfo(e.loaded);

      downloadStatus.progress = Math.round((e.loaded / e.total) * 100);
      text.value = `${loadSizeInfo.size.toFixed(2)}/${totalSizeInfo.size.toFixed(2)} ${totalSizeInfo.unit}`;
    })
    .addSuccessEvent((response, fileName) => {
      progressBarVisible.value = false;
      downloadStatus.done = true;
      downloadStatus.downloading = false;
      fileInfo.fileName = fileName;
      response.blob().then((blob) => {
        fileInfo.blob = blob;
        createA();
      });
    })
    .addErrorEvent(() => {
      downloadStatus.downloading = false;
      progressBarVisible.value = false;
    })
    .start();
};

const download = () => {
  // 已经下载过的，就不再下载
  if (fileInfo.blob) {
    createA();
    return;
  }
  if (downloadStatus.downloading) {
    message.info('已经取消下载。');
    downloadController?.abort();
    downloadStatus.downloading = false;
    progressBarVisible.value = false;
    return;
  }
  executeDownload();
};
</script>

<template>
  <APopover
    :open="progressBarVisible"
    overlay-class-name="progressStyle"
    trigger="click"
    @open-change="OnProgressShow"
    @update:open="(value) => (progressBarVisible = value)"
  >
    <ADropdownButton
      :class="showContextMenu"
      :size="btnSize"
      :type="btnType"
      @click="download"
    >
      <template #overlay>
        <AMenu v-if="!isUniApp" @click="executeDownload">
          <AMenuItem key="1">重新下载</AMenuItem>
        </AMenu>
      </template>
      <DownloadOutlined />
      <span style="white-space: pre-line"><slot>下载</slot></span>
    </ADropdownButton>
    <template #content>
      <ARow justify="start">
        <ACol :offset="2" :span="13">
          <AProgress :format="() => text" :percent="downloadStatus.progress" />
        </ACol>
      </ARow>
    </template>
  </APopover>
</template>

<style>
.coder-download-file-hide .ant-dropdown-trigger {
  display: none !important;
}

.progressStyle {
  width: 330px;
}
</style>
