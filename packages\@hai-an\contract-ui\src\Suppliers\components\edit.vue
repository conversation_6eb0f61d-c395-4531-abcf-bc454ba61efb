<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import {
  createSupplierApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Card as ACard,
  Col as ACol,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Row as ARow,
  message,
} from 'ant-design-vue';

const props = defineProps({
  id: { default: 0, type: Number },
});

const api = createSupplierApi(options.request, options.path);

const submitForm = reactive({
  address: '',
  bankName: '',
  code: '',
  id: 0,
  name: '',
  num: '',
  phone: '',
  userName: '',
});

const submitFormRef = ref();
const rules = ref({
  address: [{ max: 100, message: '字符串长度最大为200', trigger: 'blur' }],
  bankName: [{ max: 100, message: '字符串长度最大为200', trigger: 'blur' }],
  code: [
    { max: 100, message: '字符串长度最大为200', trigger: 'blur' },
    { message: '请输入纳税人识别号!', required: true, trigger: 'blur' },
  ],
  name: [
    { max: 200, message: '字符串长度最大为200', trigger: 'blur' },
    { message: '请输入公司全称!', required: true, trigger: 'blur' },
  ],
  num: [{ max: 50, message: '字符串长度最大为200', trigger: 'blur' }],
  phone: [{ max: 50, message: '字符串长度最大为200', trigger: 'blur' }],
  userName: [{ max: 50, message: '字符串长度最大为200', trigger: 'blur' }],
});

const reload = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};

// 暴露给父组件的方法
defineExpose({
  save: async () => {
    try {
      await submitFormRef.value.validate();
      const res = await api.save(submitForm);
      if (res.success) {
        message.success(res.message);
        return submitForm;
      } else {
        message.error(res.message);
        return false;
      }
    } catch {
      return false;
    }
  },
  reset: () => {
    if (submitForm.id === 0) {
      const tempForm = {
        address: '',
        bankName: '',
        code: '',
        name: '',
        num: '',
        phone: '',
        userName: '',
      };
      Object.assign(submitForm, tempForm);
    } else {
      reload();
    }
  },
});

onMounted(() => {
  reload();
});

const labelCol = { style: { width: '150px' } };
</script>
<template>
  <ACard>
    <AForm
      ref="submitFormRef"
      layout="horizontal"
      :model="submitForm"
      :rules="rules as any"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 14 }"
    >
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="公司全称"
            name="name"
          >
            <AInput v-model:value="submitForm.name" autocomplete="off" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="纳税人识别号"
            name="code"
          >
            <AInput v-model:value="submitForm.code" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="开户银行名称"
            name="bankName"
          >
            <AInput v-model:value="submitForm.bankName" autocomplete="off" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="银行账号"
            name="num"
          >
            <AInput v-model:value="submitForm.num" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="联系人"
            name="userName"
          >
            <AInput v-model:value="submitForm.userName" autocomplete="off" />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="电话"
            name="phone"
          >
            <AInput v-model:value="submitForm.phone" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="24">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="公司地址"
            name="address"
          >
            <AInput v-model:value="submitForm.address" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
    </AForm>
  </ACard>
</template>
