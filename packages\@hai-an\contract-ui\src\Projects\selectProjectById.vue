<script setup lang="ts">
import type {
  ProjectSearch,
  ProjectViewModel,
} from '@hai-an/contract-api/src/types/project';

import { computed, onMounted, reactive, ref } from 'vue';

import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { createProjectApi, haianContractOption } from '@hai-an/contract-api';
import { Button, InputGroup, Select, Tooltip } from 'ant-design-vue';

import { showAdd } from './list/useModal';

type ProjectSelectOption = {
  key: number;
  label: string;
  project: ProjectViewModel;
  value: number;
};
const props = withDefaults(defineProps<{ modelValue?: number | string }>(), {
  modelValue: '',
});
const emit = defineEmits(['update:modelValue', 'change']);

const api = createProjectApi(
  haianContractOption.request,
  haianContractOption.path,
);

const loading = ref(false);

const searchForm = reactive({
  isDeleted: false,
  page: 1,
  pageSize: 50,
} as ProjectSearch);

const projectInfo = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});
const projectResult = ref<ProjectSelectOption[]>([]);

const search = () => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    projectResult.value.splice(0);
    let matchModelValue = false;
    res.forEach((element) => {
      if (!matchModelValue) {
        matchModelValue = element.id === props.modelValue;
      }
      projectResult.value.push({
        key: element.id,
        label: `${element.name}(${element.code})`,
        project: element,
        value: element.id,
      });

      if (matchModelValue && props.modelValue) {
        api.getById(props.modelValue).then((resp) => {
          projectResult.value.push({
            key: resp.id,
            label: `${resp.name}(${resp.code})`,
            project: resp,
            value: resp.id,
          });
        });
      }
    });

    loading.value = false;
  });
};
const handleSearch = (val: any) => {
  searchForm.name = val;
  search();
};
const handleChange = (val: any) => {
  for (let i = 0; i < projectResult.value.length; i++) {
    const data = projectResult.value[i];
    if (data?.value === val) {
      emit('change', data?.project);
      break;
    }
  }
};

const toNew = () => {
  showAdd(() => {
    search();
  });
};

onMounted(() => {
  projectInfo.value = props.modelValue;
  search();
});
</script>
<template>
  <InputGroup compact>
    <Select
      v-model:value="projectInfo"
      :default-active-first-option="false"
      :filter-option="false"
      :loading="loading"
      :not-found-content="null"
      :options="projectResult"
      placeholder="请输入项目名称"
      show-search
      style="width: 210px"
      @change="handleChange"
      @search="handleSearch"
      allow-clear
    >
      <template #suffixIcon>
        <SearchOutlined />
      </template>
    </Select>
    <Tooltip title="新增项目">
      <Button type="link" @click="toNew">
        <PlusOutlined />
      </Button>
    </Tooltip>
  </InputGroup>
</template>
