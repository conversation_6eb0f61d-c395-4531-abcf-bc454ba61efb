import type { RequestClient } from '@vben/request';

import type { PageSearcher, ResponseMessage } from './common';

export interface CarSearcher extends PageSearcher {
  carNo?: string;
  deleteFlag?: boolean;
  driverName?: string;
  driverPhone?: string;
}

export interface CarSubmit {
  carNo?: string;
  deleteFlag?: boolean;
  driverName?: string;
  driverPhone?: string;
  id?: number;
}

export const CreateCarApi = (host: string, request: RequestClient) => {
  const path = `${host}/Car`;
  return {
    count(searcher: CarSearcher) {
      return request.get<number>(`${path}/count`, { params: searcher });
    },

    delete(id: number) {
      return request.delete<ResponseMessage>(`${path}/${id}`);
    },

    getById(id: number) {
      return request.get<CarSubmit>(`${path}/${id}`);
    },

    save(data: CarSubmit) {
      return request.post<ResponseMessage>(`${path}/save`, data);
    },

    list(searcher: CarSearcher) {
      return request.get<Array<CarSubmit>>(`${path}/list`, {
        params: searcher,
      });
    },
  };
};
