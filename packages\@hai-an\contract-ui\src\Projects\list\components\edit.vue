<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import {
  createProjectApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Col as ACol,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Row as ARow,
  message,
  Modal,
} from 'ant-design-vue';

const props = defineProps({
  id: { default: 0, type: Number },
});

const api = createProjectApi(options.request, options.path);

const submitForm = reactive({
  code: '',
  id: 0,
  manager: '',
  name: '',
  phone: '',
});

const submitFormRef = ref();
const rules = ref({
  code: [
    { max: 100, message: '字符串长度最大为100', trigger: 'blur' },
    {
      asyncValidator(_, value: any, callback: any) {
        const existForm = {
          code: value,
          id: submitForm.id,
        };
        api.exist(existForm).then((res) => {
          if (res.success) {
            callback();
          } else {
            callback(new Error(res.message));
          }
        });
      },
      trigger: 'blur',
    },
  ],
  manager: [{ max: 100, message: '字符串长度最大为50', trigger: 'blur' }],
  name: [
    { max: 100, message: '字符串长度最大为200', trigger: 'blur' },
    { message: '请输入项目名称!', required: true, trigger: 'blur' },
  ],
  phone: [{ max: 100, message: '字符串长度最大为50', trigger: 'blur' }],
} as any);
const reload = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};

onMounted(() => {
  reload();
});

defineExpose({
  save: () => {
    return new Promise((resolve) => {
      submitFormRef.value.validate().then(() => {
        api.save(submitForm).then((res) => {
          if (res.success) {
            message.success(res.message);
            resolve(0);
          } else {
            Modal.info({ content: res.message });
          }
        });
      });
    });
  },
  reset: () => {
    if (submitForm.id === 0) {
      const tempForm = {
        code: '',
        manager: '',
        name: '',
        phone: '',
      };
      Object.assign(submitForm, tempForm);
    } else {
      reload();
    }
  },
});

const labelCol = { style: { width: '130px' } };
</script>
<template>
  <AForm
    ref="submitFormRef"
    layout="horizontal"
    :model="submitForm"
    :rules="rules as any"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 14 }"
  >
    <ARow>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同项目编号"
          name="code"
        >
          <AInput v-model:value="submitForm.code" autocomplete="off" />
        </AFormItem>
      </ACol>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同项目名称"
          name="name"
        >
          <AInput v-model:value="submitForm.name" autocomplete="off" />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="负责人"
          name="manager"
        >
          <AInput v-model:value="submitForm.manager" autocomplete="off" />
        </AFormItem>
      </ACol>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="负责人电话"
          name="phone"
        >
          <AInput v-model:value="submitForm.phone" autocomplete="off" />
        </AFormItem>
      </ACol>
    </ARow>
  </AForm>
</template>
