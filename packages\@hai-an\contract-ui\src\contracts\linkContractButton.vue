<script setup lang="ts">
import { computed, ref } from 'vue';

import { LinkOutlined } from '@ant-design/icons-vue';
import { Button as AButton, Modal as AModal } from 'ant-design-vue';

import LinkContract from './linkContract.vue';

const props = defineProps({
  linkCode: { default: '', type: String },
});

const linkCode = computed(() => {
  return props.linkCode;
});
const linkContractTitle = ref('关联合同列表');
const isLinkContract = ref(false);

const linkContract = () => {
  isLinkContract.value = true;
};
const canceled = () => {
  isLinkContract.value = false;
};
</script>
<template>
  <AButton type="link" v-if="!!linkCode" @click="linkContract">
    <LinkOutlined />
    {{ linkCode }}
  </AButton>
  <AModal
    :title="linkContractTitle"
    :width="1200"
    v-model:open="isLinkContract"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <LinkContract v-if="linkCode" :code="linkCode" />
  </AModal>
</template>
