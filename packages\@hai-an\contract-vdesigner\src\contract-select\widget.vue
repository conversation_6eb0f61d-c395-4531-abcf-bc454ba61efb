<script lang="ts" setup>
import type { WidgetPropsType } from '@coder/vdesigner-core';

import type { ContractListItem } from '../../../contract-api/src';
import type { ContractSelectOptions } from './_options';

import { computed } from 'vue';

import {
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { ContractSelect } from '@hai-an/contract-ui';

const props = defineProps<WidgetPropsType>();
const { getComponent } = useWidgetRegistry();
const { widget, value, isDesign, callJsCode } = useWidget(
  props.widget,
  props.renderId,
);
const renderStore = useRenderStore(props.renderId);
const options = widget.value.options as ContractSelectOptions;
const CoderVDesignFormItem = getComponent(
  'CoderVDesignFormItem',
  renderStore.implement,
);
const style = computed(() => {
  return {
    display: options.hidden ? 'none' : 'block',
  };
});

const onChange = (contract: ContractListItem | undefined) => {
  if (!isDesign && options.changeEvent) {
    callJsCode(options.changeEvent, { contract });
  }
};
</script>
<template>
  <CoderVDesignFormItem v-bind="props">
    <ContractSelect
      v-model="value"
      :is-workload-lock="options.isWorkloadLock"
      :is-master="options.isMaster"
      :is-lock="options.isLock"
      :org-path="options.orgPath"
      :contract-type="options.contractType"
      :book-type="options.bookType"
      :style="style"
      @change="onChange"
    />
  </CoderVDesignFormItem>
</template>
