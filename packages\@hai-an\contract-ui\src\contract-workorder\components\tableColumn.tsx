import type { ContractWorkOrderViewModel } from '@hai-an/contract-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import { dayF } from '../../util';

export const makeColumns = (emits: any) => {
  return [
    {
      dataIndex: 'workOrderNo',
      title: '工单编码',
      customRender: ({ record }) => {
        const historyViewModel = record as ContractWorkOrderViewModel;

        return (
          <a onClick={() => emits('detail', historyViewModel.workOrderId)}>
            {historyViewModel.workOrderNo}
          </a>
        );
      },
    },
    {
      dataIndex: 'workOrderSubject',
      title: '工单主题',
    },
    {
      dataIndex: 'workOrderWorkProcessName',
      title: '流程名称',
    },
    {
      dataIndex: 'workOrderStatus',
      title: '流程状态',
    },
    {
      dataIndex: 'createBy',
      title: '流程创建人',
    },
    {
      customRender: ({ text }) => {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'createTime',
      title: '流程处理时间',
    },
  ] as ColumnType[];
};
