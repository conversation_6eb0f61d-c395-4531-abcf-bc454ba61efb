import type { FileListItem } from '@coder/document-api';

import { reactive, ref } from 'vue';

import { useFormData } from '@coder/common-api';
import { createFileApi } from '@coder/document-api';

import { documentOptions } from '../../..';

type UploadStatus = FileListItem & {
  current: number;
  message: string;
  total: number;
};

export type FileItem = FileListItem & {
  uploadStatus: UploadStatus;
};

export const useFileTable = (props: { folderId: number }) => {
  const fileApi = createFileApi(documentOptions.request, documentOptions.path);

  const headers = {
    Authorization: () =>
      documentOptions.getToken ? documentOptions.getToken() : '',
    // 'Content-Type': 'multipart/form-data',
  };

  /**
   * 添加了 上传需要的
   */
  const tableData = reactive<FileItem[]>([]);
  const tablePagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tableLoading = ref(false);

  const loadTableData = async () => {
    tableLoading.value = true;
    try {
      const [files, total] = await Promise.all([
        fileApi.list({
          deleteFlag: false,
          folderId: props.folderId,
          page: tablePagination.value.current,
          pageSize: tablePagination.value.pageSize,
        }),
        fileApi.count({
          deleteFlag: false,
          folderId: props.folderId,
        }),
      ]);
      tableData.splice(0);
      files.forEach((item) => {
        tableData.push(item as any);
      });
      tablePagination.value.total = total;
    } finally {
      tableLoading.value = false;
    }
  };

  const handleTableChange = (pagination: any) => {
    if (!props.folderId) return;
    tablePagination.value.current = pagination.current;
    tablePagination.value.pageSize = pagination.pageSize;
    loadTableData();
  };

  const uploadFile = (file: File) => {
    const position = tableData.push({
      fileLength: file.size,
      name: file.name,
      uploadStatus: {
        current: 0,
        message: '上传中',
        total: file.size,
      },
    } as unknown as FileItem);
    const doc = tableData[position - 1];
    if (!doc) throw new Error('文件不存在');
    doc.id = -1 * position;

    useFormData({
      method: 'POST',
      url: () => {
        const fileApi = createFileApi(
          documentOptions.request,
          `api${documentOptions.path}`,
        );

        return fileApi.getUploadUrl(props.folderId);
      },
    })
      .addHeaders(headers)
      .addFile('file', file)
      .addProgressEvent((opt) => {
        if (doc) {
          doc.uploadStatus.current = opt.loaded;
          doc.uploadStatus.total = opt.total;
        }
      })
      .addSuccessEvent((opt) => {
        opt.json().then((_resp: any) => {
          // Handle upload success
        });
      })
      .submit();
  };

  return {
    handleTableChange,
    loadTableData,

    tableData,
    tableLoading,
    tablePagination,

    uploadFile,
  };
};
