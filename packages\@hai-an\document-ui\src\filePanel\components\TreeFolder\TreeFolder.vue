<script lang="ts" setup>
import type { FolderSearcher } from '@hai-an/document-api';
import type { TreeDropInfo, TreeOption } from 'naive-ui';

import { onMounted, ref, watch } from 'vue';

import { createFolderApi } from '@hai-an/document-api';
import { Button, message, Modal } from 'ant-design-vue';
import { NDropdown, NTree } from 'naive-ui';

import { documentOptions } from '../../..';
import FolderPermission from '../FolderPermission/index.vue';
import FolderProperty from '../FolderProperty/index.vue';
import { useContextMenu } from './useContextMenu';

const props = defineProps({
  selectedId: {
    type: [Number, String],
    default: null,
  },
  // ...其他props
});
const emit = defineEmits<{
  (e: 'select', id: number): void;
}>();
const selectedKeys = ref<any[]>([]);

watch(
  () => props.selectedId,
  (val) => {
    if (val) {
      selectedKeys.value = [val];
      emit('select', val as number);
    }
    // else selectedKeys.value = [];
  },
  { immediate: true },
);

const treeData = ref<TreeOption[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const folderPropertyRef = ref();
const processPermissionRef = ref();
const {
  dropdownOptions,
  handleClickoutside,
  handleContextMenu,
  menuX,
  menuY,
  selectedNode,
  showCreateModal,
  showCopyModal,
  showDropdown,
  showRenameModal,
} = useContextMenu();

// Handle lazy loading of child nodes
const loadChild = async (parenNode: TreeOption) => {
  try {
    const folderApi = createFolderApi(
      documentOptions.request!,
      documentOptions.path,
    );
    const children = await folderApi.list({
      parentFolderId: parenNode.id,
    } as FolderSearcher);

    parenNode.children = children.map((item) => ({
      ...item,
      isLeaf: false,
    }));
    parenNode.children = [...parenNode.children];
  } catch {
    message.error('Failed to load child nodes');
  }
};

// Load folder data
const loadRoot = async () => {
  treeData.value = [];
  try {
    const folderApi = createFolderApi(
      documentOptions.request!,
      documentOptions.path,
    );
    const data = await folderApi.list({ parentId: 0 });
    treeData.value = data.map((item) => ({
      ...item,
      isLeaf: false,
    }));
  } catch {
    message.error('Failed to load folder data');
  }
};

const handleDropdownSelect = async (key: string) => {
  showDropdown.value = false;
  if (key === 'properties' && selectedNode.value) {
    folderPropertyRef.value?.show();
  } else if (key === 'create') {
    showCreateModal(selectedNode.value, async (id: number) => {
      await loadChild(selectedNode.value as any);
      const createdNode =
        selectedNode.value &&
        selectedNode.value.children &&
        (selectedNode.value.children.find(
          (item: any) => item.id === id,
        ) as any);

      if (createdNode && createdNode.id) {
        selectedKeys.value = [createdNode.id];
        emit('select', createdNode.id);
      }
    });
  } else if (key === 'rename' && selectedNode.value) {
    showRenameModal(selectedNode.value, async () => {
      // 如果重命名的是根节点，刷新整个树
      if (selectedNode.value?.parentFolderId === 0) {
        await loadRoot();
      } else {
        // 否则刷新父节点
        const parentNode = findNodeById(
          treeData.value,
          selectedNode.value?.parentFolderId,
        );
        if (parentNode) {
          await loadChild(parentNode);
        }
      }
    });
  } else if (key === 'refresh' && selectedNode.value) {
    // 如果是根节点，刷新整个树
    // eslint-disable-next-line unicorn/prefer-ternary
    if (selectedNode.value.parentFolderId === 0) {
      await loadRoot();
    } else {
      // 否则刷新当前节点
      await loadChild(selectedNode.value);
    }
  } else if (key === 'copy' && selectedNode.value) {
    showCopyModal(selectedNode.value, async (id: number) => {
      // console.log('copy node:' ,selectedNode.value);
      // 如果重命名的是根节点，刷新整个树
      if (selectedNode.value?.parentFolderId === 0) {
        await loadRoot();
      } else {
        // 否则刷新父节点
        const parentNode = findNodeById(
          treeData.value,
          selectedNode.value?.parentFolderId,
        );

        if (parentNode) {
          await loadChild(parentNode);
        }
      }
      emit('select', id);
      //       const copiedNode =
      //         selectedNode.value &&
      //         selectedNode.value.children &&
      //         (selectedNode.value.children.find(
      //           (item: any) => item.id === id,
      //         ) as any);
      // console.log('copiedNode', copiedNode);
      //       if (copiedNode && copiedNode.id) {
      //         selectedKeys.value = [copiedNode.id];
      //         emit('select', copiedNode.id);
      //       }
    });
  } else if (key === 'delete' && selectedNode.value) {
    Modal.confirm({
      cancelText: '取消',
      okText: '确定',
      onOk: () => {
        const folderApi = createFolderApi(
          documentOptions.request!,
          documentOptions.path,
        );

        folderApi
          .deleteFolder(selectedNode.value.id)
          .then((msg) => {
            if (msg.success) {
              message.success({ content: '删除成功' });
              // 删除改节点
              removeNodeById(treeData.value, selectedNode.value.id);
              // selectedKeys.value = [];
              // emit('select', 0);
            } else {
              message.error({ content: msg.message });
            }
          })
          .catch((error) => {
            console.error(error);
            message.error({ content: error.message });
          });
      },
      title: `是否删除文件夹‘${selectedNode.value.name}’?`,
    });
  } else if (key === 'download' && selectedNode.value) {
    const folderApi = createFolderApi(
      documentOptions.request!,
      documentOptions.path,
    );
    folderApi
      .buildPack(selectedNode.value.id)
      .then((msg) => {
        if (msg.success) {
          message.success({ content: msg.message });
        } else {
          message.error({ content: msg.message });
        }
      })
      .catch((error) => {
        // console.error(msg);
        message.error({ content: error.message });
      });
  } else if (key === 'setpermission' && selectedNode.value) {
    processPermissionRef.value?.show();
  }
};

// Handle node selection
const handleSelect = (keys: number[]) => {
  const id = keys[0];
  if (id === undefined) {
    return;
  }
  emit('select', id);
};

const nodesProps = ({ option }: { option: TreeOption }) => {
  return {
    onContextmenu(e: MouseEvent): void {
      handleContextMenu(e, option);
    },
  };
};
const saveSuccess = (data: any = {}) => {
  const { performers, manager } = data;
  selectedNode.value.performers = [...performers];
  selectedNode.value.managePerformers = [...manager];
};
onMounted(() => {
  loadRoot();
});
function findNodeById(nodes: TreeOption[], id: number): TreeOption | undefined {
  for (const node of nodes) {
    if (node.id === id) return node;
    if (node.children && node.children.length > 0) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return undefined;
}
// 递归删除树节点
function removeNodeById(nodes: TreeOption[], id: number): boolean {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === id) {
      nodes.splice(i, 1);
      return true;
    }
    if (
      nodes[i].children &&
      nodes[i].children.length > 0 &&
      removeNodeById(nodes[i].children, id)
    ) {
      return true;
    }
  }
  return false;
}
const handleDrop = ({ dragNode, dropPosition, node }: TreeDropInfo) => {
  const folderApi = createFolderApi(
    documentOptions.request!,
    documentOptions.path,
  );

  const reOrder = () => {
    folderApi
      .exchange({
        aId: dragNode.id as any,
        bId: node.id as any,
      })
      .then(() => {
        if (dragNode.parentFolderId === 0) {
          loadRoot();
        } else {
          const aryPathes = (dragNode.namePath as string).split('/');
          aryPathes.pop();
          let parent: any;
          do {
            const path = aryPathes.shift();
            parent = treeData.value.find((item) => item.name === path);
          } while (aryPathes.length > 0);

          if (parent) loadChild(parent);
        }
      });
  };

  switch (dropPosition) {
    case 'after': {
      reOrder();
      break;
    }
    case 'before': {
      reOrder();
      break;
    }
    case 'inside': {
      if (dragNode.parentFolderId === node.parentFolderId) {
        reOrder();
      }
      break;
    }
    // No default
  }
};
</script>

<template>
  <NDropdown
    :options="dropdownOptions"
    :show="showDropdown"
    :x="menuX"
    :y="menuY"
    placement="bottom-start"
    trigger="manual"
    @clickoutside="handleClickoutside"
    @select="handleDropdownSelect"
  />
  <Button @click="loadRoot">刷新</Button>
  <NTree
    v-model:selected-keys="selectedKeys"
    :data="treeData"
    :default-expanded-keys="defaultExpandedKeys"
    :node-props="nodesProps"
    :on-load="loadChild"
    block-line
    draggable
    key-field="id"
    label-field="name"
    remote
    @drop="handleDrop"
    @update:selected-keys="handleSelect"
  />
  <FolderProperty ref="folderPropertyRef" :folder="selectedNode" />
  <FolderPermission
    ref="processPermissionRef"
    :folder="selectedNode"
    @save-success="(data) => saveSuccess(data)"
  />
</template>
