import dayjs from 'dayjs';

const kUnit = 1024 * 1024;
const mbUnit = kUnit * 1024;

const sizeShow = (size: number) => {
  if (size < 1024) {
    return `${size} byte`;
  } else if (size < mbUnit) {
    return `${Math.round(size / 1024)} KB`;
  } else {
    return `${Math.round(size / kUnit)} MB`;
  }
};
export default [
  {
    key: 'op',
    title: '操作',
  },
  {
    dataIndex: 'name',
    key: 'name',
    title: '文件名称',
  },
  {
    dataIndex: 'comment',
    key: 'comment',
    title: '备注',
  },
  {
    customRender: ({ text }: { index: number; text: string }) => {
      return sizeShow(Number.parseFloat(text));
    },
    dataIndex: 'fileSize',
    key: 'fileSize',
    title: '文件大小',
  },

  {
    customRender: ({ text }: { text: string }) => {
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
    dataIndex: 'createTime',
    key: 'createTime',
    title: '创建日期',
  },
];
