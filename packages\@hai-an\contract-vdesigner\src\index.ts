import type { App } from 'vue';

import { registerLabelCar } from './car-label/index';
import { registerCarSelector } from './car-select';
import { registerContractGroupSelectWidget } from './contract-group-select/index';
import { registerContractLinkWidget } from './contract-link/index';
import { registerContractSelectWidget } from './contract-select';
import { registerProjectSelectWidget } from './project-selector';
import { registerSupplierSelectWidget } from './supplier-selector';

const install = {
  install(_app: App) {
    registerContractLinkWidget();
    registerLabelCar();
    registerCarSelector();
    registerContractSelectWidget();
    registerContractGroupSelectWidget();
    registerSupplierSelectWidget();
    registerProjectSelectWidget();
  },
};
export default install;
