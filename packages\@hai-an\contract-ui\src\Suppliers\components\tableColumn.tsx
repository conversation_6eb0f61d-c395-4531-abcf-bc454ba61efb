import type { ColumnsType } from 'ant-design-vue/es/table';

export default [
  {
    align: 'center',
    dataIndex: 'action',
    title: '操作',
    width: 320,
    // slots: { customRender: "action" },
  },
  {
    dataIndex: 'name',
    title: '公司全称',
    width: 200,
    // slots: { customRender: "name" },
  },
  {
    dataIndex: 'code',
    title: '纳税人识别号',
    // slots: { customRender: "code" },
  },
  {
    dataIndex: 'bankName',
    title: '开户银行名称',
    // slots: { customRender: "bankName" },
  },
  {
    dataIndex: 'num',
    title: '银行账号',
    // slots: { customRender: "num" },
  },
  {
    dataIndex: 'userName',
    title: '联系人',
    // slots: { customRender: "userName" },
  },
  {
    dataIndex: 'phone',
    title: '电话',
    // slots: { customRender: "phone" },
  },
  {
    dataIndex: 'address',
    title: '公司地址',
    // slots: { customRender: "address" },
  },
  {
    dataIndex: 'isDeleted',
    title: '状态',
    // slots: { customRender: "isDeleted" },
  },
  {
    dataIndex: 'createBy',
    title: '创建人',
    // slots: { customRender: "createBy" },
  },
  {
    dataIndex: 'createTime',
    title: '创建日期',
    // slots: { customRender: "createTime" },
  },
] as ColumnsType;
