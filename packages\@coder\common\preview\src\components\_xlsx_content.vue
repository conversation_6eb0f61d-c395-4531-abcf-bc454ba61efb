<script lang="ts" setup>
import { ref, watch } from 'vue';

import VueOfficeExcel from '@vue-office/excel';

// 引入相关样式
import '@vue-office/excel/lib/index.css';

interface Props {
  headers?: Record<string, string>;
  src: Blob | string;
}

const props = withDefaults(defineProps<Props>(), {
  headers: () => ({}),
});

const options = ref({
  xls: false, // 预览xlsx文件设为false；预览xls文件设为true
  minColLength: 0, // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
  minRowLength: 0, // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
  widthOffset: 10, // 如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
  heightOffset: 10, // 在默认渲染的列表高度上再加 Npx高
});

const blobUrl = ref<string>('');
const loading = ref(false);
const error = ref<string>('');

const loadExcel = async (url: string) => {
  if (!url) return;

  loading.value = true;
  error.value = '';

  try {
    const response = await fetch(url, {
      headers: props.headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    blobUrl.value = URL.createObjectURL(blob);
  } catch (error_) {
    error.value =
      error_ instanceof Error ? error_.message : '加载Excel文件失败';
    console.error('Excel加载失败:', error_);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.src,
  (newUrl) => {
    if (newUrl && typeof newUrl === 'string') {
      loadExcel(newUrl as string);
    }
  },
  {
    immediate: true,
  },
);

// 清理 blob URL
watch(
  () => blobUrl.value,
  (_, oldUrl) => {
    if (oldUrl) {
      URL.revokeObjectURL(oldUrl);
    }
  },
);
</script>
<template>
  <div class="xlsx-preview-container">
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载Excel文件...</p>
    </div>
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
    </div>
    <div v-else-if="blobUrl" class="xlsx-content">
      <VueOfficeExcel :src="blobUrl" :options="options" />
    </div>
    <div v-else class="empty">
      <p>暂无Excel文件</p>
    </div>
  </div>
</template>

<style scoped>
.xlsx-preview-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 80vh;
}

.xlsx-content {
  width: 100%;
  height: 100%;
  overflow: auto; /* 提供滚动条 */
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.error,
.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}
</style>
