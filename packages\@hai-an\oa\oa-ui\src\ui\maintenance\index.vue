<script setup lang="ts">
import { ref } from 'vue';

import { EyeOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { createMaintenanceApi, haianOAOption as option } from '@hai-an/oa-api';
import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Space as ASpace,
  Textarea as ATextarea,
  message,
} from 'ant-design-vue';

const API = createMaintenanceApi(option.request, option.workflowPath);

const processInstanceNumber = ref('');
const processInstanceNumber1 = ref('');
const form = ref('');

const onModifyStatus = () => {
  API.GetProcessingBackFirst(processInstanceNumber.value).then((resp) => {
    if (resp.data.success) message.info(resp.data.message);
    else message.error(resp.data.message);
  });
};

const onBackToSubmitter = () => {
  API.GetProcessingBackLast(processInstanceNumber.value).then((resp) => {
    if (resp.data.success) message.info(resp.data.message);
    else message.error(resp.data.message);
  });
};

const onGetCompletedfirst = () => {
  API.GetCompletedfirst(processInstanceNumber.value).then((resp) => {
    if (resp.data.success) message.info(resp.data.message);
    else message.error(resp.data.message);
  });
};

const onGetFormSave = () => {
  API.GetFormByNumber(processInstanceNumber1.value).then((resp) => {
    if (resp.data.success) form.value = resp.data.message;
    else message.error(resp.data.message);
  });
};

const onFormSave1 = () => {
  API.FormSaveByNumber(
    processInstanceNumber1.value,
    JSON.parse(form.value),
  ).then((resp) => {
    if (resp.data.success) message.info(resp.data.message);
    else message.error(resp.data.message);
  });
};
</script>

<template>
  <ACard title="修改工单状态环节" :bordered="false">
    <AForm layout="inline" :label-col="{ span: 5 }">
      <AFormItem label="工单号" name="processInstanceNumber">
        <AInput v-model:value="processInstanceNumber" placeholder="工单号" />
      </AFormItem>

      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="onModifyStatus">
            <SearchOutlined />工单已闭单状态改为未完成，工作处理人退回到提单环节
          </AButton>
        </ASpace>
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="onBackToSubmitter">
            <EyeOutlined />工单已闭单状态改为未完成，工作处理人回退到最后一环节
          </AButton>
        </ASpace>
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="onGetCompletedfirst">
            <SearchOutlined />工单未完成状态改为已闭单，提单处理人直接完成（用于替换工单附件，在修复成已完成）
          </AButton>
        </ASpace>
      </AFormItem>
    </AForm>
  </ACard>

  <ACard title="修改工单Form内容" :bordered="false">
    <AForm layout="inline" :label-col="{ span: 12 }">
      <AFormItem label="工单号" name="processInstanceNumber1">
        <AInput v-model:value="processInstanceNumber1" placeholder="工单号" />
      </AFormItem>

      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="onGetFormSave">
            <SearchOutlined />获取保存的form数据
          </AButton>
        </ASpace>
      </AFormItem>
      <AFormItem name="form">
        <ATextarea
          v-model:value="form"
          placeholder="form数据"
          :rows="4"
          :cols="50"
        />
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="onFormSave1">
            <EyeOutlined />保存form的数据
          </AButton>
        </ASpace>
      </AFormItem>
    </AForm>
  </ACard>
</template>

<style scoped></style>
