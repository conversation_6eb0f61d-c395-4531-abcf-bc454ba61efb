const isNothing = (val: any): boolean => !val;

const GetEncode64 = (str: string) => {
  /* eslint-disable unicorn/prefer-code-point */
  return btoa(
    encodeURIComponent(str).replaceAll(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode(Number(`0x${p1}`));
    }),
  );
  /* eslint-enable unicorn/prefer-code-point */
};

export const makePreviewUrl = (_fileUri: string, _serviceAddress: string) => {
  _serviceAddress = tryAddEndSplit(_serviceAddress);

  if (!isNothing(_fileUri) && !isNothing(_serviceAddress)) {
    return `${_serviceAddress}onlinePreview?url=${encodeURIComponent(
      GetEncode64(_fileUri),
    )}&officePreviewType=pdf`;
  }
  return false;
};

export const tryAddEndSplit = (url: string) => {
  if (!url.endsWith('/')) return `${url}/`;
  return url;
};
