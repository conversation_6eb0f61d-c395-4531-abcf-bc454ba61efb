{
  "folders": [
    {
      "name": "@vben/backend-mock",
      "path": "apps/backend-mock",
    },
    {
      "name": "@vben/web-antd",
      "path": "apps/web-antd",
    },
    {
      "name": "@vben/web-ele",
      "path": "apps/web-ele",
    },
    {
      "name": "@vben/web-naive",
      "path": "apps/web-naive",
    },
    {
      "name": "@vben/docs",
      "path": "docs",
    },
    {
      "name": "@vben/commitlint-config",
      "path": "internal/lint-configs/commitlint-config",
    },
    {
      "name": "@vben/eslint-config",
      "path": "internal/lint-configs/eslint-config",
    },
    {
      "name": "@vben/prettier-config",
      "path": "internal/lint-configs/prettier-config",
    },
    {
      "name": "@vben/stylelint-config",
      "path": "internal/lint-configs/stylelint-config",
    },
    {
      "name": "@vben/node-utils",
      "path": "internal/node-utils",
    },
    {
      "name": "@vben/tailwind-config",
      "path": "internal/tailwind-config",
    },
    {
      "name": "@vben/tsconfig",
      "path": "internal/tsconfig",
    },
    {
      "name": "@vben/vite-config",
      "path": "internal/vite-config",
    },
    {
      "name": "@coder/common-api",
      "path": "packages/@coder/common-api",
    },
    {
      "name": "@coder/clip-button",
      "path": "packages/@coder/common/clip-button",
    },
    {
      "name": "@coder/code-editor",
      "path": "packages/@coder/common/code-editor",
    },
    {
      "name": "@coder/mermaid",
      "path": "packages/@coder/common/coder-mermaid",
    },
    {
      "name": "@coder/excel-sheet",
      "path": "packages/@coder/common/excel-sheet",
    },
    {
      "name": "@coder/file-download",
      "path": "packages/@coder/common/file-download",
    },
    {
      "name": "@coder/file-upload",
      "path": "packages/@coder/common/file-upload",
    },
    {
      "name": "@coder/string-format",
      "path": "packages/@coder/common/format-string",
    },
    {
      "name": "@coder/http-request-setting",
      "path": "packages/@coder/common/http-request-setting",
    },
    {
      "name": "@coder/kkfile-preview",
      "path": "packages/@coder/common/kkfile-preview",
    },
    {
      "name": "@coder/monaco-editor-builder",
      "path": "packages/@coder/common/monaco-builder",
    },
    {
      "name": "@coder/object-editor",
      "path": "packages/@coder/common/object-editor",
    },
    {
      "name": "@coder/preview",
      "path": "packages/@coder/common/preview",
    },
    {
      "name": "@coder/print-pdf",
      "path": "packages/@coder/common/print-pdf",
    },
    {
      "name": "@coder/rich-editor",
      "path": "packages/@coder/common/rich-editor",
    },
    {
      "name": "@coder/toolbar",
      "path": "packages/@coder/common/toolbar",
    },
    {
      "name": "@coder/file-uploador",
      "path": "packages/@coder/file-system/file-uploador",
    },
    {
      "name": "@coder/fs-api",
      "path": "packages/@coder/file-system/fs-api",
    },
    {
      "name": "@coder/fs-ui",
      "path": "packages/@coder/file-system/fs-ui",
    },
    {
      "name": "@coder/notify-api",
      "path": "packages/@coder/notify/notify-api",
    },
    {
      "name": "@coder/notify-ui",
      "path": "packages/@coder/notify/notify-ui",
    },
    {
      "name": "@coder/page-api",
      "path": "packages/@coder/pager-builder/page-api",
    },
    {
      "name": "@coder/page-designer",
      "path": "packages/@coder/pager-builder/page-designer",
    },
    {
      "name": "@coder/page-render",
      "path": "packages/@coder/pager-builder/page-render",
    },
    {
      "name": "@coder/swf-api",
      "path": "packages/@coder/swf/swf-api",
    },
    {
      "name": "@coder/swf-designer",
      "path": "packages/@coder/swf/swf-designer",
    },
    {
      "name": "@coder/swf-render",
      "path": "packages/@coder/swf/swf-render",
    },
    {
      "name": "@coder/swf-ui",
      "path": "packages/@coder/swf/swf-ui",
    },
    {
      "name": "@coder/system-api",
      "path": "packages/@coder/system/system-api",
    },
    {
      "name": "@coder/system-ui",
      "path": "packages/@coder/system/system-ui",
    },
    {
      "name": "@coder/vdesigner-form-antdv",
      "path": "packages/@coder/vdesigner-form/antdv",
    },
    {
      "name": "@coder/chart",
      "path": "packages/@coder/vdesigner-form/chart",
    },
    {
      "name": "@coder/vdesigner-core",
      "path": "packages/@coder/vdesigner-form/core",
    },
    {
      "name": "@coder/vdesigner-form-designer",
      "path": "packages/@coder/vdesigner-form/designer",
    },
    {
      "name": "@coder/vdesigner-plugins-axios",
      "path": "packages/@coder/vdesigner-form/plugins/vdesigner-plugins-axios",
    },
    {
      "name": "@coder/vdesigner-plugins-router",
      "path": "packages/@coder/vdesigner-form/plugins/vdesigner-plugins-router",
    },
    {
      "name": "@coder/vdesigner-form-render",
      "path": "packages/@coder/vdesigner-form/render",
    },
    {
      "name": "@coder/vdesigner-form-vant",
      "path": "packages/@coder/vdesigner-form/vant",
    },
    {
      "name": "@coder/vdesigner-widget-http-file",
      "path": "packages/@coder/vdesigner-form/widgets/http-file",
    },
    {
      "name": "@coder/vdesigner-performer",
      "path": "packages/@coder/vdesigner-form/widgets/performer-selector",
    },
    {
      "name": "@coder/vdesigner-widget-print-pdf",
      "path": "packages/@coder/vdesigner-form/widgets/printPdf",
    },
    {
      "name": "@vben-core/design",
      "path": "packages/@core/base/design",
    },
    {
      "name": "@vben-core/icons",
      "path": "packages/@core/base/icons",
    },
    {
      "name": "@vben-core/shared",
      "path": "packages/@core/base/shared",
    },
    {
      "name": "@vben-core/typings",
      "path": "packages/@core/base/typings",
    },
    {
      "name": "@vben-core/composables",
      "path": "packages/@core/composables",
    },
    {
      "name": "@vben-core/preferences",
      "path": "packages/@core/preferences",
    },
    {
      "name": "@vben-core/form-ui",
      "path": "packages/@core/ui-kit/form-ui",
    },
    {
      "name": "@vben-core/layout-ui",
      "path": "packages/@core/ui-kit/layout-ui",
    },
    {
      "name": "@vben-core/menu-ui",
      "path": "packages/@core/ui-kit/menu-ui",
    },
    {
      "name": "@vben-core/popup-ui",
      "path": "packages/@core/ui-kit/popup-ui",
    },
    {
      "name": "@vben-core/shadcn-ui",
      "path": "packages/@core/ui-kit/shadcn-ui",
    },
    {
      "name": "@vben-core/tabs-ui",
      "path": "packages/@core/ui-kit/tabs-ui",
    },
    {
      "name": "@hai-an/car-api",
      "path": "packages/@hai-an/car-api",
    },
    {
      "name": "@hai-an/car-ui",
      "path": "packages/@hai-an/car-ui",
    },
    {
      "name": "@hai-an/contract-api",
      "path": "packages/@hai-an/contract-api",
    },
    {
      "name": "@hai-an/contract-ui",
      "path": "packages/@hai-an/contract-ui",
    },
    {
      "name": "@hai-an/vdesigner-oa",
      "path": "packages/@hai-an/contract-vdesigner",
    },
    {
      "name": "@hai-an/diary-api",
      "path": "packages/@hai-an/diary-api",
    },
    {
      "name": "@hai-an/document-api",
      "path": "packages/@hai-an/document-api",
    },
    {
      "name": "@hai-an/document-ui",
      "path": "packages/@hai-an/document-ui",
    },
    {
      "name": "@hai-an/human-api",
      "path": "packages/@hai-an/human-api",
    },
    {
      "name": "@hai-an/oa-api",
      "path": "packages/@hai-an/oa/oa-api",
    },
    {
      "name": "@hai-an/oa-ui",
      "path": "packages/@hai-an/oa/oa-ui",
    },
    {
      "name": "@vben/constants",
      "path": "packages/constants",
    },
    {
      "name": "@vben/access",
      "path": "packages/effects/access",
    },
    {
      "name": "@vben/common-ui",
      "path": "packages/effects/common-ui",
    },
    {
      "name": "@vben/hooks",
      "path": "packages/effects/hooks",
    },
    {
      "name": "@vben/layouts",
      "path": "packages/effects/layouts",
    },
    {
      "name": "@vben/plugins",
      "path": "packages/effects/plugins",
    },
    {
      "name": "@vben/request",
      "path": "packages/effects/request",
    },
    {
      "name": "@vben/icons",
      "path": "packages/icons",
    },
    {
      "name": "@vben/locales",
      "path": "packages/locales",
    },
    {
      "name": "@vben/preferences",
      "path": "packages/preferences",
    },
    {
      "name": "@vben/stores",
      "path": "packages/stores",
    },
    {
      "name": "@vben/styles",
      "path": "packages/styles",
    },
    {
      "name": "@vben/types",
      "path": "packages/types",
    },
    {
      "name": "@vben/utils",
      "path": "packages/utils",
    },
    {
      "name": "@vben/playground",
      "path": "playground",
    },
    {
      "name": "@vben/turbo-run",
      "path": "scripts/turbo-run",
    },
    {
      "name": "@vben/vsh",
      "path": "scripts/vsh",
    },
  ],
}
