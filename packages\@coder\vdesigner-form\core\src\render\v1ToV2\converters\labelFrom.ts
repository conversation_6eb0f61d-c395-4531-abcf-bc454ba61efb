import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

export class Label<PERSON>romConvert extends V1Convert {
  constructor() {
    super('lable-form', 'CoderVDesignLabel');
  }
  override SetOption(v1: any, v2: Widget, _cfg: Record<string, any>): void {
    v2.options = {
      content: `\${${v1.options.name}}`,
      hidden: v1.options.hidden,
      label: v1.options.label,
      labelHidden: v1.options.labelHidden,
    };

    /**
     * 如果是合同编号，则需要特殊处理
     */
    switch (v1.options.label) {
      case '合同名称': {
        v2.type = 'CoderVDesignRemoteLabel';
        v2.options.accessPath = '_{getContractByCodeURL_return_contract_name}';
        v2.options.url = '_{GetContractByCodeURL}';

        break;
      }
      case '合同编号': {
        v2.type = 'CoderVDesignRemoteLabel';
        v2.options.accessPath = 'contractNo';

        v2.options.url = '_{GetContractByCodeURL}';

        break;
      }
      case '客户名称': {
        v2.options.content = `code:const a = formData.supplier\n if(a) return a.split('(')[0] \n return ''`;
        break;
      }
      case '项目名称': {
        v2.type = 'CoderVDesignRemoteLabel';

        v2.options.accessPath = '_{ProjectName}';

        v2.options.url = '_{GetProjectUrl}';
        break;
      }
      case '项目编号': {
        v2.type = 'CoderVDesignRemoteLabel';

        v2.options.accessPath = '_{ProjectCode}';

        v2.options.url = '_{GetProjectUrl}';
        break;
      }
      // No default
    }
  }
}
