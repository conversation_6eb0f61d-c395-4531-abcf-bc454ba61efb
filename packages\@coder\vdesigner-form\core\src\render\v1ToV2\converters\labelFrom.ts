import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

export class Label<PERSON>romConvert extends V1Convert {
  constructor() {
    super('lable-form', 'CoderVDesignLabel');
  }
  override SetOption(v1: any, v2: Widget, _cfg: Record<string, any>): void {
    v2.options = {
      content: `\${${v1.options.name}}`,
      hidden: v1.options.hidden,
      label: v1.options.label,
      labelHidden: v1.options.labelHidden,
    };

    /**
     * 如果是合同编号，则需要特殊处理
     */
    switch (v1.options.label) {
      case '合同名称': {
        v2.type = 'CoderVDesignRemoteLabel';

        v2.options.accessPath =
          // eslint-disable-next-line no-template-curly-in-string
          '${cfg:getContractByCodeURL_return_contract_name}';
        // eslint-disable-next-line no-template-curly-in-string
        v2.options.url = '${cfg:GetContractByCodeURL}';

        break;
      }
      case '合同编号': {
        v2.type = 'CoderVDesignRemoteLabel';
        v2.options.accessPath = 'contractNo';
        // eslint-disable-next-line no-template-curly-in-string
        v2.options.url = '${cfg:GetContractByCodeURL}';

        break;
      }
      case '客户名称': {
        v2.options.value = `code:const a = formData.supplier if(a) return a.split('(')[0] return ''`;
        break;
      }
      case '项目名称': {
        v2.type = 'CoderVDesignRemoteLabel';
        // eslint-disable-next-line no-template-curly-in-string
        v2.options.accessPath = '${cfg:ProjectName}';
        // eslint-disable-next-line no-template-curly-in-string
        v2.options.url = '${cfg:GetProjectUrl}';
        break;
      }
      // No default
    }
  }
}
