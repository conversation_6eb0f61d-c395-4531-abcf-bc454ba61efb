<script setup lang="ts">
import { computed, ref } from 'vue';

import { DownloadOutlined, UploadOutlined } from '@ant-design/icons-vue';
import {
  createHumanResourceApi,
  haianHumanOption as option,
} from '@hai-an/human-api';
import {
  <PERSON><PERSON> as <PERSON>utton,
  Modal as AModal,
  Upload as AUpload,
  message,
} from 'ant-design-vue';

import downloadFile from '../downloadFile';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'reload']);
const isVisible = computed(() => props.visible);

const API = createHumanResourceApi(option.request, option.humanPath);

const fileList = ref<any[]>([]);

const handleImportOk = async () => {
  if (fileList.value.length === 0) return message.error('请选择要上传的文件');
  try {
    const formData = new FormData();
    formData.append('file', fileList.value[0].originFileObj);
    await API.importAnnualLeave(formData);
    message.success('导入成功');
    handleClose();
    emit('reload');
  } catch {}
};
const handleClose = () => {
  emit('update:visible', false);
};

const downloadTemplate = async () => {
  try {
    const res: any = await API.exportTemplate();
    if (!res) throw new Error('下载失败：没有收到数据');
    const name = '年假导入模板';
    downloadFile(res.data, name);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log('err:', error);
  }
};
const beforeUpload = (file: File) => {
  const isExcel =
    file.type ===
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  if (!isExcel) {
    message.error('只能上传 Excel 文件！');
  }
  return isExcel || AUpload.LIST_IGNORE;
};
</script>

<template>
  <AModal
    v-model:visible="isVisible"
    title="导入年假"
    :width="500"
    ok-text="确认导入"
    cancel-text="取消"
    @ok="handleImportOk"
    @cancel="handleClose"
  >
    <div class="import-modal-content">
      <p>请先下载模板，按照模板格式填写数据后再上传：</p>
      <AButton type="link" @click="downloadTemplate">
        <template #icon><DownloadOutlined /></template>
        下载模板
      </AButton>
      <div class="upload-area">
        <AUpload
          v-model:file-list="fileList"
          :before-upload="beforeUpload"
          :max-count="1"
          class="custom-upload"
          :show-upload-list="{
            showPreviewIcon: false,
            showRemoveIcon: true,
          }"
        >
          <AButton>
            <template #icon><UploadOutlined /></template>
            选择文件
          </AButton>
        </AUpload>
        <p class="upload-tip">支持扩展名：.xlsx</p>
      </div>
    </div>
  </AModal>
</template>

<style scoped>
.upload-tip {
  margin-top: 8px;
  color: #999;
}
</style>
