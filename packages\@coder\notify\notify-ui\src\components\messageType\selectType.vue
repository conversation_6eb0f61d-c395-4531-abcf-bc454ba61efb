<script lang="ts" setup>
import type { MessageTypeSearcher, MessageTypeSubmit } from '@coder/notify-api';

import { computed, reactive, watch } from 'vue';

import { getMessageTypeApi } from '@coder/notify-api';
import { Select as ASelect } from 'ant-design-vue';
import { debounce } from 'lodash-es';

type PropsType = {
  modelValue: string;
  name?: string;
};
// ----------------- end -----------------------------------
const props = defineProps<PropsType>();

const emit = defineEmits<{
  (e: 'change', v: MessageTypeSubmit): void;
  (e: 'update:modelValue', v: string): void;
}>();

const api = getMessageTypeApi();
const MessageTypeId = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});

const dataSource = reactive([] as Array<any>);

const _handleSearch = (name: string) => {
  api
    .list({
      name: name ?? '',
      page: 1,
      pageSize: 10,
    } as MessageTypeSearcher)
    .then((resp) => {
      dataSource.splice(0);
      const data = resp.map((_) => {
        return {
          label: _.name,
          value: _.id,
        };
      });
      dataSource.push(...data);
    });
};

const handleSearch = debounce(_handleSearch, 500);
const onChange = (v: number) => {
  getMessageTypeApi()
    .get(v)
    .then((resp) => {
      emit('change', resp);
    });
};
watch(
  () => props.name,
  (v) => {
    if (v) {
      handleSearch(v);
    }
  },
  {
    immediate: true,
  },
);
</script>
<template>
  <div>
    <ASelect
      v-model:value="MessageTypeId"
      :default-active-first-option="false"
      :filter-option="false"
      :not-found-content="null"
      :options="dataSource"
      :show-arrow="false"
      placeholder="消息类型"
      show-search
      style="width: 200px"
      @change="(s) => onChange(s as string)"
      @search="(s) => handleSearch(s)"
    />
  </div>
</template>
