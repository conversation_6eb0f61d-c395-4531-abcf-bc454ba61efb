import type { InstanceListItemViewModel } from '@coder/swf-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import type { EmitsTypes } from './types';

import { ClipButton } from '@coder/clip-button';
import {
  createWorkflowApi,
  formatDateText,
  getProcessInstanceStatus,
} from '@coder/swf-api';
import { showSubmitResult, swfOption } from '@coder/swf-render';
import { Button, Divider, Modal, Space } from 'ant-design-vue';

import {
  canCancel,
  canDelete,
  canResume,
  canSuspend,
  getSubjectDescription,
  isManager,
} from './_utility';
import ManageButtons from './managerButtons/index.vue';

const workflowApi = createWorkflowApi(swfOption.request, swfOption.host);
/**
 * 处理按钮
 * @param record
 * @param emits
 * @returns
 */
const disposeBtn = (record: InstanceListItemViewModel, emits: EmitsTypes) => {
  const show = record.canDisposeWorkActivity || record.canAcceptWorkActivity;
  if (!show) return '';
  return (
    <Button onClick={() => emits('dispose', record)} type="primary">
      处理
    </Button>
  );
};
/** 显示启动processInstance的btn */
const startBtn = (
  record: InstanceListItemViewModel,

  reload: { (): void },
) => {
  const onStart = () => {
    Modal.confirm({
      cancelText: '不启动',
      content: () => '当你启动流程之后，流程会自动派发到相关工作人员。',
      okText: '启动流程',
      onOk: () => {
        workflowApi.start(record.id).then((resp) => {
          showSubmitResult(resp);
          if (resp.success) {
            reload();
          }
        });
      },
      title: `警告-` + `你正在启动流程${getSubjectDescription(record)}`,
    });
  };

  const show = record.canDisposeWorkActivity || record.canAcceptWorkActivity;
  if (!show) return '';
  return (
    <Button onClick={() => onStart()} type="primary">
      启动
    </Button>
  );
};

const manageButtonRender = (
  record: InstanceListItemViewModel,
  emits: EmitsTypes,
  reload: { (): void },
) => {
  const show =
    isManager(record) ||
    canResume(record) ||
    canSuspend(record) ||
    canCancel(record) ||
    canDelete(record);
  if (!show) return '';

  return (
    <ManageButtons
      modelValue={record}
      onDebug={() => emits('debug', record)}
      onDelEnd={() => reload()}
      onEdit={() => emits('edit', record)}
      reload={reload}
    />
  );
};

const makeColumn = (emits: EmitsTypes, reload: { (): void }, slots: any) => {
  return [
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        return (
          <Space>
            {disposeBtn(pi, emits)}
            {startBtn(pi, reload)}
            {slots.processInstance?.(pi)}
            {manageButtonRender(pi, emits, reload)}
          </Space>
        );
      },
      key: 'id',
      title: '操作',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        return (
          <>
            <ClipButton value={pi.subject} />
            <a
              onClick={() => emits('detail', pi)}
              style="color:hsl(var(--primary))"
            >
              {pi.subject}
            </a>
          </>
        );
      },
      dataIndex: 'subject',
      key: 'subject',

      title: '主题',
    },
    {
      dataIndex: 'workProcessName',
      key: 'workProcessName',
      minWidth: 130,
      title: '流程名称',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        const desc = getProcessInstanceStatus(pi.status);
        return desc;
      },
      dataIndex: 'status',
      key: 'status',
      title: '流程状态',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        const result = formatDateText(pi.createTime);

        if (pi.finishTime) {
          return (
            <>
              {result}
              <Divider style="font-size:10px;margin:5px 0;">到</Divider>
              {formatDateText(pi.finishTime)}
            </>
          );
        }
        return result;
      },
      dataIndex: 'createTime',
      key: 'createTime',
      title: '时间',
      width: 174,
    },

    {
      dataIndex: 'creatorName',
      key: 'creatorName',
      title: '申请人',
    },

    {
      dataIndex: 'workTaskName',
      key: 'workTaskName',
      title: '当前任务',
    },
    {
      dataIndex: 'workActivityDisposeUserName',
      key: 'workActivityDisposeUserName',
      title: '处理人',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        const slots = {
          default: () => pi.number,
        };
        return (
          <ClipButton
            type="customer"
            v-slots={slots}
            value={pi.number}
          ></ClipButton>
        );
      },
      dataIndex: 'number',
      key: 'number',
      title: '工单号',
    },
  ] as ColumnType[];
};

/* export default columns;*/

export const getColumns = (
  showProcessName: boolean | string | undefined,
  emits: EmitsTypes,
  reload: { (): void },
  slots: any,
): any => {
  const result = makeColumn(emits, reload, slots);
  if (showProcessName) result.splice(3, 1);
  return result;
};
