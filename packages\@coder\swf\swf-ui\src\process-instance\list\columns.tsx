import type { InstanceListItemViewModel } from '@coder/swf-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import type { EmitsTypes } from './types';

import { ClipButton } from '@coder/clip-button';
import { formatDateText, getProcessInstanceStatus } from '@coder/swf-api';
import { Button, Divider, Space } from 'ant-design-vue';

import {
  canCancel,
  canDelete,
  canResume,
  canSuspend,
  isManager,
} from './_utility';
import ManageButtons from './managerButtons/index.vue';

import './style.css';

/**
 * 处理按钮
 * @param record - 记录数据
 * @param emits - 事件触发器
 * @returns 渲染的按钮组件或空字符串
 */
const disposeBtn = (record: InstanceListItemViewModel, emits: EmitsTypes) => {
  const show = record.canDisposeWorkActivity || record.canAcceptWorkActivity;
  if (!show) return '';
  return (
    <Button onClick={() => emits('dispose', record)} type="primary">
      处理
    </Button>
  );
};

/**
 * 显示启动processInstance的btn
 * @param _record - 实例记录
 * @param _reload - 重新加载函数
 * @returns 渲染的按钮组件或空字符串
 */
const startBtn = (
  _record: InstanceListItemViewModel,
  _reload: { (): void },
) => {
  return '';
};

const manageButtonRender = (
  record: InstanceListItemViewModel,
  emits: EmitsTypes,
  reload: { (): void },
) => {
  const show =
    isManager(record) ||
    canResume(record) ||
    canSuspend(record) ||
    canCancel(record) ||
    canDelete(record);
  if (!show) return '';

  return (
    <ManageButtons
      modelValue={record}
      onDebug={() => emits('debug', record)}
      onDelEnd={() => reload()}
      onEdit={() => emits('edit', record)}
      reload={reload}
    />
  );
};

const makeColumn = (emits: EmitsTypes, reload: { (): void }, slots: any) => {
  return [
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        return (
          <Space>
            {disposeBtn(pi, emits)}
            {startBtn(pi, reload)}
            {slots.processInstance?.(pi)}
            {manageButtonRender(pi, emits, reload)}
          </Space>
        );
      },
      key: 'id',
      title: '操作',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        return (
          <>
            <ClipButton value={pi.subject} />
            <a
              onClick={() => emits('detail', pi)}
              style="color:hsl(var(--primary))"
            >
              {pi.subject}
            </a>
          </>
        );
      },
      dataIndex: 'subject',
      key: 'subject',
      title: '主题',
      width: 200,
    },
    {
      dataIndex: 'workProcessName',
      key: 'workProcessName',
      minWidth: 130,
      title: '流程名称',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        const desc = getProcessInstanceStatus(pi.status);
        return desc;
      },
      dataIndex: 'status',
      key: 'status',
      title: '流程状态',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        const result = formatDateText(pi.createTime);

        if (pi.finishTime) {
          return (
            <>
              {result}
              <Divider style="font-size:10px;margin:5px 0;">到</Divider>
              {formatDateText(pi.finishTime)}
            </>
          );
        }
        return result;
      },
      dataIndex: 'createTime',
      key: 'createTime',
      title: '时间',
      width: 174,
    },
    {
      dataIndex: 'creatorName',
      key: 'creatorName',
      title: '申请人',
    },
    {
      dataIndex: 'workTaskName',
      key: 'workTaskName',
      title: '当前任务',
    },
    {
      dataIndex: 'workActivityDisposeUserName',
      key: 'workActivityDisposeUserName',
      title: '处理人',
    },
    {
      customRender: ({ record }) => {
        const pi = record as InstanceListItemViewModel;
        const numberClass = {
          complete: pi.status === 2,
          underway: pi.status === 1,
        };
        return (
          <>
            <ClipButton value={pi.number} />
            <a class={numberClass} onClick={() => emits('detail', pi)}>
              {pi.number}
            </a>
          </>
        );
      },
      dataIndex: 'number',
      key: 'number',
      title: '工单号',
    },
  ] as ColumnType[];
};

export const getColumns = (
  showProcessName: boolean | string | undefined,
  emits: EmitsTypes,
  reload: { (): void },
  slots: any,
): any => {
  const result = makeColumn(emits, reload, slots);
  if (showProcessName) result.splice(3, 1);
  return result;
};
