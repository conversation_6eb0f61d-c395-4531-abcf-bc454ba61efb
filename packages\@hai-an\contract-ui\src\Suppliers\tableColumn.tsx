import type { SupplierViewModel } from '@hai-an/contract-api/src/types/supplier';
import type { ColumnsType } from 'ant-design-vue/es/table';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import { Button, Space, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { deleteSupplier, showDetail, showEditor } from './useModal';

const dayF = (val: any) => {
  return dayjs(val).format('YYYY-MM-DD');
};

export const makeColumns = (reload: { (): void }) => {
  return [
    {
      align: 'center',
      dataIndex: 'action',
      title: '操作',

      customRender({ record }) {
        const supplier = record as SupplierViewModel;
        if (supplier.isDeleted === true) {
          return <span>--无操作--</span>;
        }

        return (
          <Space>
            <Button onClick={() => showEditor(supplier, reload)} type="link">
              <EditOutlined />
              修改
            </Button>

            <Button
              onClick={() => deleteSupplier(supplier, reload)}
              type="link"
            >
              <DeleteOutlined />
              删除
            </Button>
          </Space>
        );
      },
    },
    {
      dataIndex: 'name',
      title: '公司全称',
      width: 200,
      customRender({ record }) {
        const supplier = record as SupplierViewModel;
        return <a onClick={() => showDetail(supplier)}>{supplier.name}</a>;
      },
    },
    {
      dataIndex: 'code',
      title: '纳税人识别号',
    },
    {
      dataIndex: 'inBlackList',
      title: '拉黑状态',
      customRender({ record }) {
        return (
          <Tag color={record.inBlackList ? 'volcano' : 'green'}>
            {record.inBlackList ? '已删除' : '正常'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'bankName',
      title: '开户银行名称',
    },
    {
      dataIndex: 'num',
      title: '银行账号',
    },
    {
      dataIndex: 'userName',
      title: '联系人',
    },
    {
      dataIndex: 'phone',
      title: '电话',
    },
    {
      dataIndex: 'address',
      title: '公司地址',
    },
    {
      dataIndex: 'isDeleted',
      title: '状态',
      customRender({ record }) {
        return (
          <Tag color={record.isDeleted ? 'volcano' : 'green'}>
            {record.isDeleted ? '已删除' : '正常'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
    },
    {
      dataIndex: 'createTime',
      title: '创建日期',
      customRender({ text }) {
        return <>{dayF(text)}</>;
      },
    },
  ] as ColumnsType;
};
