// Bootstrap Grid System Variables
$grid-columns: 12 !default;
$grid-gutter-width: 1.5rem !default;
$grid-row-columns: 6 !default;

$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
) !default;

$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
) !default;

// Bootstrap Grid System Mixins
@mixin make-row($gutter: $grid-gutter-width) {
  --bs-gutter-x: #{$gutter};
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}

@mixin make-col-ready() {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

@mixin make-col($size: false, $columns: $grid-columns) {
  @if $size {
    flex: 0 0 auto;
    width: percentage(divide($size, $columns));
  } @else {
    flex: 1 1 0;
    max-width: 100%;
  }
}

@mixin make-col-auto() {
  flex: 0 0 auto;
  width: auto;
}

@mixin make-col-offset($size, $columns: $grid-columns) {
  $num: divide($size, $columns);
  margin-left: if($num == 0, 0, percentage($num));
}

// Helper function for division
@function divide($dividend, $divisor) {
  @return calc($dividend / $divisor);
}

// Media query mixins
@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {
  $min: map-get($breakpoints, $name);
  @if $min {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {
  $max: map-get($breakpoints, $name);
  @if $max {
    @media (max-width: $max - 0.02) {
      @content;
    }
  } @else {
    @content;
  }
}

// Bootstrap Grid Classes
.container,
.container-fluid {
  --bs-gutter-x: #{$grid-gutter-width};
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}

@each $breakpoint, $container-max-width in $container-max-widths {
  .container-#{$breakpoint} {
    @extend .container-fluid;
  }

  @include media-breakpoint-up($breakpoint, $grid-breakpoints) {
    %responsive-container-#{$breakpoint} {
      max-width: $container-max-width;
    }

    @each $name, $width in $grid-breakpoints {
      @if ($container-max-width > $width or $breakpoint == $name) {
        .container#{if($breakpoint != $name, "-#{$breakpoint}", "")} {
          @extend %responsive-container-#{$breakpoint};
        }
      }
    }
  }
}

.row {
  @include make-row();

  > * {
    @include make-col-ready();
  }
}

.col {
  @include make-col();
}

@for $i from 1 through $grid-columns {
  .col-#{$i} {
    @include make-col($i, $grid-columns);
  }
}

.col-auto {
  @include make-col-auto();
}

@for $i from 0 through ($grid-columns - 1) {
  @if not ($i == 0) {
    .offset-#{$i} {
      @include make-col-offset($i, $grid-columns);
    }
  }
}

// Responsive columns
@each $breakpoint in map-keys($grid-breakpoints) {
  $infix: if($breakpoint == xs, "", "-#{$breakpoint}");

  @include media-breakpoint-up($breakpoint, $grid-breakpoints) {
    .col#{$infix} {
      @include make-col();
    }

    @for $i from 1 through $grid-columns {
      .col#{$infix}-#{$i} {
        @include make-col($i, $grid-columns);
      }
    }

    .col#{$infix}-auto {
      @include make-col-auto();
    }

    @for $i from 0 through ($grid-columns - 1) {
      @if not ($infix == "" and $i == 0) {
        .offset#{$infix}-#{$i} {
          @include make-col-offset($i, $grid-columns);
        }
      }
    }
  }
}

/*
移动widget的选择按钮。
*/
@mixin dragHandler() {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  display: none;
  justify-content: center;
  width: 18px;
  height: 18px;
  color: hsl(var(--primary-foreground));
  cursor: move;
  background-color: hsl(var(--primary));
}

/**
选型了widget之后， 的外框样式。
**/
@mixin select-rect() {
  &:hover {
    outline: 1px solid hsl(var(--primary));
  }

  &.selected {
    outline: 1px solid hsl(var(--primary));

    > .drag-handle {
      display: flex !important;
    }

    > .toolbar {
      display: flex !important;
    }
  }
}

