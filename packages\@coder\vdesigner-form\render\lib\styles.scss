/*
移动widget的选择按钮。
*/
@mixin dragHandler() {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  display: none;
  justify-content: center;
  width: 18px;
  height: 18px;
  color: hsl(var(--primary-foreground));
  cursor: move;
  background-color: hsl(var(--primary));
}

/**
选型了widget之后， 的外框样式。
**/
@mixin select-rect() {
  &:hover {
    outline: 1px solid hsl(var(--primary));
  }

  &.selected {
    outline: 1px solid hsl(var(--primary));

    > .drag-handle {
      display: flex !important;
    }

    > .toolbar {
      display: flex !important;
    }
  }
}

