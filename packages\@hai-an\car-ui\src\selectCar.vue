<script setup lang="ts">
import type { CarSearcher, CarSubmit } from '@hai-an/car-api';

import { computed, onMounted, reactive, ref } from 'vue';

import { CreateCarApi } from '@hai-an/car-api';
import { Select } from 'ant-design-vue';

import { haianCarOption } from './haianCarOption';

const props = defineProps({
  // carInfo: { type: String },
  modelValue: { default: () => null, type: String },
});
const emit = defineEmits(['update:modelValue']);
const api = CreateCarApi(haianCarOption.carPath, haianCarOption.request);

const loading = ref(false);
const searchForm = reactive<CarSearcher>({
  deleteFlag: false,
  page: 1,
  pageSize: 50,
});

const carInfo = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});
const datas = reactive<CarSubmit[]>([]);
const allDatas = reactive<CarSubmit[]>([]);

const toSearch = () => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    const cars: any[] = [];
    allDatas.splice(0);
    allDatas.push(...res);
    res.forEach((element) => {
      const carInfotem = `${element.carNo}(${element.driverName}:${
        element.driverPhone
      })`;
      cars.push({
        car: element,
        label: carInfotem,
        value: carInfotem,
      });
    });
    datas.splice(0);
    datas.push(...cars);
    loading.value = false;
  });
};
const handleSearch = (val: any) => {
  searchForm.carNo = val;
  toSearch();
};
onMounted(() => {
  toSearch();
});
</script>
<template>
  <Select
    v-model:value="carInfo"
    :default-active-first-option="false"
    :filter-option="false"
    :loading="loading"
    :not-found-content="null"
    :options="datas"
    :show-arrow="false"
    placeholder="请输入车牌号"
    show-search
    style="width: 300px"
    @search="handleSearch"
  />
</template>
