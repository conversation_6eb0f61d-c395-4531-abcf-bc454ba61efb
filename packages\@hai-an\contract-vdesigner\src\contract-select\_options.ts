import { generateId } from '@coder/vdesigner-core';

export class ContractSelectOptions {
  bookType?: string;
  changeEvent?: string;
  contractType?: string;
  hidden: boolean = true;
  isLock: boolean = false;
  isMaster: boolean = false;
  isWorkloadLock: boolean = false;
  label: string = '合同选择';
  labelHidden: boolean = false;
  name: string = `ContractSelect${generateId()}`;
  orgPath?: string;
}
