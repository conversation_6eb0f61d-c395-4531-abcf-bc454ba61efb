<script setup lang="ts">
import type { NotificationItem } from './types';

import { computed } from 'vue';

import { Bell } from '@vben/icons';
import { useNotifyStore } from '@vben/stores';

// import { getMessageTypeApi } from '@coder/notify-api';
// import type {
//   // MessageTypeListItem,
//   MessageTypeSearcher,
// } from '@coder/notify-api';
import {
  VbenIconButton,
  VbenPopover,
  VbenScrollbar,
} from '@vben-core/shadcn-ui';

import { useToggle } from '@vueuse/core';
import { TabPane, Tabs } from 'ant-design-vue';

import NoticeList from './components/notice-list.vue';

interface Props {
  /**
   * 显示圆点
   */
  dot?: boolean;
  /**
   * 消息列表
   */
  notifications?: NotificationItem[];
}

defineOptions({ name: 'NotificationPopup' });

const props = withDefaults(defineProps<Props>(), {
  dot: false,
  notifications: () => [],
});
const emit = defineEmits(['read']);

const [open, toggle] = useToggle();

function close() {
  open.value = false;
}

const notifyMessageStore = useNotifyStore();

const listData = computed(() => {
  return notifyMessageStore.types.map((name, index) => {
    return {
      key: index,
      name,
      // list: notifyMessageStore.message.filter(
      list: props.notifications.filter(
        (item: NotificationItem) => item.type === name,
      ),
    };
  });
});

const onNoticeClick = (record: NotificationItem) => {
  emit('read', record);
  close();
  setTimeout(() => {
    // 刷新消息通知数据
    notifyMessageStore.reload();
  }, 3000);
};
</script>

<template>
  <VbenPopover
    v-model:open="open"
    content-class="relative right-2 w-[360px] p-0"
  >
    <template #trigger>
      <div class="flex-center mr-2 h-full" @click.stop="toggle()">
        <VbenIconButton class="bell-button text-foreground relative">
          <span
            v-if="dot"
            class="bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"
          ></span>
          <Bell class="size-4" />
        </VbenIconButton>
      </div>
    </template>
    <div class="content">
      <Tabs style="min-width: 300px">
        <template v-for="item in listData" :key="item.key">
          <TabPane>
            <template #tab>
              {{ item.name }}
              <span v-if="item.list.length > 0">({{ item.list.length }})</span>
            </template>
            <VbenScrollbar v-if="notifications.length > 0">
              <!-- v-if="item.key === 0"  -->
              <NoticeList
                :list="item.list"
                @title-click="
                  (message: NotificationItem) => onNoticeClick(message)
                "
              />
            </VbenScrollbar>
          </TabPane>
        </template>
      </Tabs>
    </div>
  </VbenPopover>
</template>

<style scoped>
.content {
  min-height: 200px;
  padding: 12px 16px;
  color: #000000d9;
}
</style>
