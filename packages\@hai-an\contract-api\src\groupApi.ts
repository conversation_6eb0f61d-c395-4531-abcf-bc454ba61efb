import type { RequestClient } from '@vben/request';

import type { ContractResponse } from './types';
import type {
  ContractGroupListItem,
  ContractGroupSearch,
  ContractGroupSubmit,
  ContractGroupViewModel,
} from './types/group';

export const createGroupApi = (request: RequestClient, path: string) => {
  return {
    /**
     * Get contract group count
     */
    count(params: { isDeleted?: boolean; name?: string }): Promise<number> {
      return request.get(`${path}/ContractGroup/count`, { params });
    },

    /**
     * Delete contract group by ID
     */
    delete(id: number): Promise<{ message: string; success: boolean }> {
      return request.delete(`${path}/ContractGroup/${id}`);
    },

    /**
     * Get contract group by ID
     */
    getById(id: number): Promise<ContractGroupViewModel> {
      return request.get(`${path}/ContractGroup/${id}`, {
        responseReturn: 'body',
      });
    },

    /**
     * Get contract group info by ID
     */
    getInfo(id: number): Promise<ContractGroupListItem> {
      return request.get(`${path}/ContractGroup/get-info/${id}`);
    },

    /**
     * Save contract group
     */
    save(data: ContractGroupSubmit): Promise<ContractResponse> {
      return request.post(`${path}/ContractGroup/save`, data);
    },

    /**
     * Get contract group list
     */
    list(params: ContractGroupSearch): Promise<ContractGroupViewModel[]> {
      return request.get(`${path}/ContractGroup/list`, { params });
    },
  };
};
