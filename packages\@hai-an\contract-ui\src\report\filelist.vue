<script setup lang="ts">
import type { SFileSearcher, SFileSubmit, SFileViewModel } from '@coder/fs-api';
import type { TablePaginationConfig } from 'ant-design-vue';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { DownloadButton } from '@coder/file-download';
import { createFsApi } from '@coder/fs-api';
import { PreviewButton } from '@coder/kkfile-preview';
import { haianContractOption as fileOption } from '@hai-an/contract-api';
import {
  Card as ACard,
  Popconfirm as APopconfirm,
  Table as ATable,
  Button,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Space,
} from 'ant-design-vue';

import columns from './_column';
import clip from './clipboard';

const total = ref(0);
const fileApi = createFsApi(fileOption.request, fileOption.path);
const searcher = reactive<SFileSearcher>({
  createUser: '',
  name: '',
  page: 1,
  pageSize: 30,
  refId: '',
  system: '',
});
const pagination = computed(() => ({
  current: searcher.page,
  pageSize: searcher.pageSize,
  total: total.value,
}));

const dataSource = reactive<Array<SFileViewModel>>([]);

const search = () => {
  fileApi.list(searcher).then((resp) => {
    dataSource.splice(0);
    resp.forEach((item) => {
      item.edit = false;
      dataSource.push(item);
    });
  });
  fileApi.count(searcher).then((resp) => (total.value = resp));
};

const onPage = (pager: TablePaginationConfig) => {
  searcher.page = pager.current || 1;
  searcher.pageSize = pager.pageSize || 30;
  search();
};

const deleteFile = (fileViewModel: SFileViewModel) => {
  fileApi.delFile(fileViewModel.id).then(() => {
    message.info('删除成功。');
    search();
  });
};
const copyToClipboard = (fileViewModel: SFileViewModel) => {
  const value = fileApi.getDownloadFilePath(fileViewModel.id);
  clip(value.toString());
};

const getFileUrl = (fileViewModel: SFileViewModel) => {
  return fileApi.getDownloadFilePath(fileViewModel.id);
};

const onEdit = (item: SFileViewModel) => {
  item.edit = true;
};
const onSave = (item: SFileViewModel) => {
  const submit = {
    comment: item.comment,
    createUser: item.createUser,
    id: item.id,
    name: item.name,
    refId: item.refId,
    system: item.system,
  } as SFileSubmit;

  fileApi.save(submit).then((resp) => {
    message.info(resp.data.message);
    item.edit = false;
  });
};

onMounted(() => {
  search();
});

// package 有关 打包多个文件。

//const packageFiles = reactive<Array<string>>([]);

// const rowSelection = {
//   onChange: (selectedRowKeys: any, _selectedRows: Array<any>) => {
//     packageFiles.splice(0);
//     packageFiles.push(...selectedRowKeys);
//   },
// };

const getFileTicket = (file: SFileViewModel): Promise<string> => {
  return fileApi.getTicket(file.id).then((resp: any) => {
    const filePath = fileApi.getViewFilePath(file.id);
    if (resp.ticket) {
      return `http://gateway/${filePath}?_ctikt_=${resp.ticket}&fullfilename=${file.name}`;
    } else {
      Modal.error({ content: '创建失败' });
      throw new Error('创建失败');
    }
  });
};

const previewHost = ref(`${fileOption.previewHost}/api/filePreview`);
</script>

<template>
  <div>
    <ACard style="margin-bottom: 15px">
      <Form layout="inline">
        <FormItem label="文件名称">
          <Input v-model:value="searcher.name" placeholder="文件名称" />
        </FormItem>
        <FormItem>
          <Space>
            <Button type="primary" @click="search">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </Button>
          </Space>
        </FormItem>
      </Form>
    </ACard>
    <ACard>
      <ATable
        :columns="columns"
        :data-source="dataSource"
        :pagination="pagination"
        row-key="id"
        @change="onPage"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'op'">
            <Space v-if="!record.edit">
              <PreviewButton
                :get-file="async () => getFileTicket(record as SFileViewModel)"
                :preview-host="previewHost"
                size="small"
              />
              <Button
                size="small"
                type="primary"
                @click="onEdit(record as SFileViewModel)"
              >
                <template #icon>
                  <EditOutlined />
                </template>
              </Button>

              <APopconfirm
                :title="`是否删除文件${record.name}?`"
                cancel-text="取消"
                ok-text="删除"
                @confirm="deleteFile(record as SFileViewModel)"
              >
                <Button danger size="small" type="primary">
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </Button>
              </APopconfirm>
            </Space>

            <Space v-if="record.edit">
              <Button size="small" @click="onSave(record as SFileViewModel)">
                <template #icon> <CheckOutlined /> </template>保存
              </Button>
              <Button size="small" @click="record.edit = false">
                <template #icon> <CloseOutlined /> </template>取消
              </Button>
            </Space>
          </template>
          <template v-if="column.key === 'creator'">
            <span v-if="!record.edit">{{ record.createUser }}</span>
            <Input v-if="record.edit" v-model:value="record.createUser" />
          </template>
          <template v-if="column.key === 'name'">
            <Space>
              <Button
                v-if="!record.edit"
                size="small"
                @click="() => copyToClipboard(record as SFileViewModel)"
              >
                <template #icon>
                  <CopyOutlined />
                </template>
              </Button>
              <DownloadButton
                :file-name="record.name"
                :url="getFileUrl(record as SFileViewModel)"
              >
                {{ record.name }}
              </DownloadButton>
            </Space>
            <Input v-if="record.edit" v-model:value="record.name" />
          </template>

          <template v-if="column.key === 'system'">
            <span v-if="!record.edit">{{ record.system }}</span>
            <Input v-if="record.edit" v-model:value="record.system" />
          </template>

          <template v-if="column.key === 'rfid'">
            <span v-if="!record.edit">{{ record.refId }}</span>
            <Input v-if="record.edit" v-model:value="record.refId" />
          </template>
        </template>
      </ATable>
    </ACard>
  </div>
</template>
