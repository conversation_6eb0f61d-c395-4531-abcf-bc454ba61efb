import type { RequestClient } from '@vben/request';

export * from './contractApi';
export * from './groupApi';
export * from './historyApi';
export * from './invoiceApi';

export * from './projectApi';
export { createProjectApi } from './projectApi';
export * from './projectArchiveHistoryApi';
export * from './projectManagementApi';
export * from './reportApi';
export * from './supplierApi';
export * from './types';
export * from './types/history';
export * from './types/index';
export type { ProjectSearch, ProjectViewModel } from './types/project';

export * from './workloadApi';
export * from './workorderApi';

export interface IHaianContractOption {
  getToken: { (): string };
  path: string;
  previewHost?: string;
  request: RequestClient;
}

export const haianContractOption = {} as IHaianContractOption;

export const api = {
  install: (_app: any, options: IHaianContractOption) => {
    if (options.path.endsWith('/')) {
      options.path = options.path.slice(
        0,
        Math.max(0, options.path.length - 1),
      );
    }

    Object.assign(haianContractOption, options);
  },
};

export default api;
