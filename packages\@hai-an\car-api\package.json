{"name": "@hai-an/car-api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "typings": "./src/index.ts", "main": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@vben/icons": "workspace:*", "@vben/request": "workspace:*", "ant-design-vue": "catalog:", "crypto-js": "catalog:coder"}, "devDependencies": {"@types/crypto-js": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}