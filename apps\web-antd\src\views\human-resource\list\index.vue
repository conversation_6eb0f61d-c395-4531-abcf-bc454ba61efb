<script setup lang="ts">
import type { WorkActivityViewModel } from '@coder/swf-api';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ProcessInstanceViews } from '@coder/swf-render';
import { HumanResourceList } from '@hai-an/oa-ui';
import { Modal } from 'ant-design-vue';

// 控制弹出框显示状态和当前选择的工单信息
const modalVisible = ref(false);
const currentOrderId = ref<null | number>(null);
const modalTitle = ref('工单详细情况');

// 工单详情加载完成后的处理函数
const onProcessInstanceLoaded = (wa: WorkActivityViewModel) => {
  modalTitle.value = wa.subject || `工单 #${currentOrderId.value}`;
};

// 处理工单信息点击事件 - 弹出详情框
const handleOrderInfo = (orderId: number) => {
  currentOrderId.value = orderId;
  modalTitle.value = '工单详细情况'; // 默认标题，加载后会更新
  modalVisible.value = true;
};
</script>

<template>
  <Page description="" title="">
    <!-- com:{
       meta:{
        title:'人力资源列表',
        icon:'lucide:folder-kanban'
     },
     name:'HumanResourceList',
     path:'/humanResource/list',
    } -->
    <HumanResourceList @order-info="handleOrderInfo" />

    <!-- 工单详情弹出框 -->
    <Modal
      :visible="modalVisible"
      :footer="null"
      width="90%"
      :body-style="{ padding: '12px' }"
      @cancel="modalVisible = false"
    >
      <div v-if="currentOrderId !== null" class="order-detail-container">
        <ProcessInstanceViews
          :is-manage="false"
          :id="currentOrderId"
          @loaded="onProcessInstanceLoaded"
          :show-flow="true"
        />
      </div>
    </Modal>
  </Page>
</template>

<style scoped>
.order-detail-container {
  height: calc(90vh - 150px);
  overflow: auto;
}
</style>
