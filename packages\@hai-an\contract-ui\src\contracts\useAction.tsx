import type { ContractListItem } from '@hai-an/contract-api';

import { ref } from 'vue';

import { Button, Modal, Space } from 'ant-design-vue';

import ContractEditor from './components/edit.vue';
import AllInfo from './components/modal-info/index.vue';
// import ContractInfo from './components/info.vue';
import LinkContract from './linkContract.vue';

export const showEditorPanel = (contract: ContractListItem) => {
  const editorRef = ref();
  const onSave = () => {
    editorRef.value.save();
  };
  const onReset = () => {
    editorRef.value.reset();
  };

  const a = Modal.confirm({
    title: '编辑合同信息',
    content: () => {
      return (
        <ContractEditor
          id={contract.id}
          onSaved={() => a.destroy()}
          ref={editorRef}
        />
      );
    },
    width: 1060,
    footer: () => {
      return (
        <Space>
          <Button onClick={onReset}>重置</Button>

          <Button onClick={() => a.destroy()}>取消</Button>

          <Button onClick={onSave} type="primary">
            保存
          </Button>
        </Space>
      );
    },
  });
};

export const showLinkContractPanel = (contract: ContractListItem) => {
  const linkRef = ref();

  const a = Modal.confirm({
    title: '关联合同列表',
    content: () => {
      return <LinkContract code={contract.code} ref={linkRef} />;
    },
    width: 1060,
    footer: () => {
      return (
        <Space>
          <Button onClick={() => a.destroy()}>取消</Button>
        </Space>
      );
    },
  });
};

export const showDetail = (contract: ContractListItem) => {
  Modal.info({
    title: '合同详情信息',
    content: () => {
      // return <ContractInfo id={contract.id} />;
      return (
        <AllInfo
          contractNumber={contract.code}
          itemId={contract.id}
          name={contract.name}
        />
      );
    },
    width: 1200,
    maskClosable: false,
    okText: '确定',
    style: { width: '1200px' },
    closable: true,
  });
};
