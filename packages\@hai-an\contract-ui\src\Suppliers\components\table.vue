<!-- 一级仓table -->
<script setup lang="ts">
import type { TablePaginationConfig } from 'ant-design-vue';

import { reactive } from 'vue';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import {
  Button as <PERSON><PERSON>on,
  Divider as ADivider,
  Table as ATable,
  Tag as ATag,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import columnDefined from './tableColumn';

const props = defineProps({
  datas: { default: () => [], type: Array },
  isEidtList: { type: Boolean },
  loading: { type: Boolean },
  pagination: { default: () => ({}), type: Object },
});
const emit = defineEmits([
  'toEdit',
  'toDel',
  'handleTableChange',
  'LinkContract',
  'toInfo',
]);
const datas = computed(() => props.datas);
const dayF = (val) => {
  return dayjs(val).format('YYYY-MM-DD');
};

const columns = reactive(columnDefined);
const toEdit = (val) => {
  emit('toEdit', val);
};
const toDel = (val) => {
  emit('toDel', val);
};
const toInfo = (val) => {
  emit('toInfo', val);
};

const handleTableChange = (pagination: TablePaginationConfig) => {
  emit('handleTableChange', pagination);
};
</script>
<template>
  <ATable
    class="ant-table-striped"
    size="middle"
    :row-class-name="(_, index) => (index % 2 === 1 ? 'table-striped' : null)"
    bordered
    :row-key="(data) => data.id"
    :pagination="pagination"
    :columns="columns"
    :loading="loading"
    @change="handleTableChange"
    :data-source="datas"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'name'">
        <a @click="toInfo({ record })">{{ record.name }}</a>
      </template>
      <template v-if="column.dataIndex === 'isDeleted'">
        <ATag :color="record.isDeleted ? 'volcano' : 'green'">
          {{ record.isDeleted ? '已删除' : '正常' }}
        </ATag>
      </template>
      <template v-if="column.dataIndex === 'action'">
        <span v-if="!record.isDeleted">
          <AButton type="link" @click="toEdit({ record })">
            <template #icon> <EditOutlined /></template>
            修改
          </AButton>
          <ADivider type="vertical" />
          <AButton type="link" @click="() => toDel({ record })">
            <template #icon>
              <DeleteOutlined />
            </template>

            删除
          </AButton>
        </span>
        <span v-else>--无操作--</span>
      </template>
      <template v-if="column.dataIndex === 'createTime'">
        {{ record.createTime ? dayF(record.createTime) : '' }}
      </template>
    </template>
  </ATable>
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
