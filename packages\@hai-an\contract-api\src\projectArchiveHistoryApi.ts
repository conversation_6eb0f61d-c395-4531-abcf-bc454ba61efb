import type { RequestClient } from '@vben/request';

import type {
  ProjectArchiveHistorySearch,
  ProjectArchiveHistoryViewModel,
} from './types/projectArchiveHistory';

export const createProjectArchiveHistoryApi = (
  request: RequestClient,
  path: string,
) => {
  return {
    /**
     * Get project archive history count
     */
    count(params: ProjectArchiveHistorySearch): Promise<number> {
      return request.get(`${path}/ProjectArchiveHistory/count`, { params });
    },

    getById(id: number) {
      return request.get(`${path}/ProjectArchiveHistory/${id}`);
    },

    /**
     * Get project archive history list
     */
    list(
      params: ProjectArchiveHistorySearch,
    ): Promise<ProjectArchiveHistoryViewModel[]> {
      return request.get(`${path}/ProjectArchiveHistory/list`, { params });
    },
  };
};
