<script setup lang="ts">
import type { FormSubmitResult } from '@coder/common-api';

import type { AttachmentPropsType } from './useAttachment';

import { computed } from 'vue';

import { UploadButton } from '@coder/file-upload';
import { Table } from 'ant-design-vue';

import { createColumn } from './infoColumn';
import { useAttachments } from './useAttachment';

const props = defineProps<AttachmentPropsType>();
const emits = defineEmits<{
  (e: 'previewFile', url: string): void;
  (e: 'update:fileIds', fileIds: string[]): void;
}>();

const { fileList, getList, getUploadUrl, headers, loading } = useAttachments(
  props,
  emits,
);
const FileIds = computed(() => {
  return props.fileIds;
});
const columns = createColumn(
  (url: string) => {
    // 预览文件的逻辑，可以在这里打开新窗口或处理文件预览
    emits('previewFile', url);
  },
  props,
  fileList,
  getList,
);

const onUploadSuccess = (res: FormSubmitResult) => {
  res.response?.json().then((res: any) => {
    if (props.messageId === undefined && props.fileIds && FileIds.value) {
      FileIds.value.push(res.data.id);
    }
    getList();
  });
};
</script>

<template>
  <div>
    <UploadButton
      v-if="props.isEdit"
      :headers="headers"
      :upload-url="getUploadUrl()"
      @success="onUploadSuccess"
    />
    <Table
      :columns="columns"
      :data-source="fileList"
      :loading="loading"
      :pagination="false"
      :row-key="(data) => data.fileName"
      bordered
      size="small"
    />
  </div>
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
