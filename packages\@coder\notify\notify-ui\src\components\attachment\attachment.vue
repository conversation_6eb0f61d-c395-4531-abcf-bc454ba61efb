<script setup lang="ts">
import type { FormSubmitResult } from '@coder/common-api';

import type { AttachmentPropsType } from './useAttachment';

import { computed } from 'vue';

import { UploadButton } from '@coder/file-upload';
import { Table } from 'ant-design-vue';

import { createColumn } from './infoColumn';
import { useAttachments } from './useAttachment';

const props = defineProps<AttachmentPropsType>();
const emits = defineEmits<{ (e: 'update:fileIds', fileIds: string[]): void }>();
const { fileList, getList, getUploadUrl, headers, loading } = useAttachments(
  props,
  emits,
);
const FileIds = computed(() => {
  return props.fileIds;
});
const columns = createColumn(props, fileList, getList);

const onUploadSuccess = (res: FormSubmitResult) => {
  res.response?.json().then((res: any) => {
    if (props.messageId === undefined && props.fileIds && FileIds.value) {
      FileIds.value.push(res.data.id);
    }
    getList();
  });
};
</script>

<template>
  <UploadButton
    :headers="headers"
    :upload-url="getUploadUrl()"
    @success="onUploadSuccess"
  />
  <Table
    :columns="columns"
    :data-source="fileList"
    :loading="loading"
    :pagination="false"
    :row-key="(data: any) => data.fileName"
    bordered
    size="small"
  />
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
