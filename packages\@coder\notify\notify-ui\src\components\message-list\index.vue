<script setup lang="ts">
import type { MessageAction, MessageViewModel } from '@coder/notify-api';
import type { TablePaginationConfig } from 'ant-design-vue';

import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { notifyOption } from '@coder/notify-api';
import {
  Button as AButton,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  Space as ASpace,
  Table as ATable,
} from 'ant-design-vue';
import TextClamp from 'vue3-text-clamp';

import { formatDate } from '../../formatDate';
import { useMessageList } from './useMessageList';

const props = defineProps<{ type: string }>();

const {
  columns,
  commands,
  dataSource,
  load,
  loading,
  onSearch,
  pagination,
  searcher,
} = useMessageList(props.type);

const onChange = (paginationInfo: TablePaginationConfig) => {
  searcher.page = paginationInfo.current || 1;
  searcher.pageSize = paginationInfo.pageSize || 50;
  load();
};

onMounted(() => {
  onSearch();
});

const router = useRouter();
const messageTypeActionCall = (
  action: MessageAction,
  message: MessageViewModel,
) => {
  const token = notifyOption.getToken();

  // eslint-disable-next-line no-new-func
  const fun = new Function(
    'message',
    'router',
    'token',
    'axios',
    action.script,
  );

  fun(message, router, token, notifyOption.request.instance);
};
</script>

<template>
  <ACard :bordered="false" style="margin-bottom: 10px">
    <AForm :label-col="{ span: 4 }" layout="inline">
      <AFormItem>
        <ARadioGroup v-model:value="searcher.status">
          <ARadio :value="0">未读</ARadio>
          <ARadio :value="1">已读</ARadio>
          <ARadio :value="null">全部</ARadio>
        </ARadioGroup>
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="onSearch">刷新</AButton>
        </ASpace>
      </AFormItem>
    </AForm>
  </ACard>

  <ACard :bordered="false">
    <ATable
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      row-key="id"
      @change="onChange"
    >
      <template #bodyCell="{ column, /*text,*/ record }">
        <template v-if="column.dataIndex === 'action'">
          <ASpace>
            <AButton
              v-for="cmd in commands"
              :key="cmd.name"
              type="link"
              @click="
                () => messageTypeActionCall(cmd, record as MessageViewModel)
              "
            >
              {{ cmd.name }}
            </AButton>
          </ASpace>
        </template>

        <template v-if="column.dataIndex === 'content'">
          <TextClamp :max-lines="2" :text="record.content" />
        </template>

        <template v-if="column.dataIndex === 'createTime'">
          {{ formatDate(record.createTime) }}
        </template>
      </template>
    </ATable>
  </ACard>
</template>
