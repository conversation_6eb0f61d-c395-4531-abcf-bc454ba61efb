<script setup lang="ts">
import type { PropType } from 'vue';

import type { TempItem } from './useTemplate';

import { computed, ref } from 'vue';
import { useDraggable } from 'vue-draggable-plus';

import { Col as ACol, Row as ARow } from 'ant-design-vue';

import { LStorage } from '../../../storage';
import { DrapItemType } from '../useDrap';
import TemplateItem from './_template-item.vue';
import { findData } from './utils';

const props = defineProps({
  drapType: { default: () => DrapItemType.widget, type: String },
  list: {
    default: () => [],
    type: Array as PropType<Array<TempItem>>,
  },
});
const tempList = computed(() => props.list);

const onClone = (e: any) => {
  LStorage.set('drag-component-data', e); // 保存拖拽是缓存的表单展示数据
  const find = findData(
    e.values.rootWidget || e.values.widget,
    'CoderVDesignPage',
  );
  return find;

  // window.localStorage.setItem('drag-component-data', JSON.stringify(e)); // 保存拖拽是缓存的表单展示数据
  // return e.values.rootWidget ?? e.values;
};

const tempListRef = ref();
useDraggable(tempListRef, tempList, {
  clone: onClone,
  group: {
    name: props.drapType,
    pull: 'clone',
    put: false,
  },
  sort: false,
});
</script>

<template>
  <ARow ref="tempListRef" :gutter="[12, 12]" style="margin-top: 6px">
    <ACol v-for="(item, index) in tempList" :key="index" :span="12">
      <TemplateItem :widget="item" />
    </ACol>
  </ARow>
</template>

<style scoped></style>
