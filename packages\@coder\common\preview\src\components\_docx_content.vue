<script lang="tsx" setup>
import { ref, watch } from 'vue';

interface Props {
  fileName?: string;
  src: string;
}

const props = withDefaults(defineProps<Props>(), {
  fileName: 'Word文档',
  headers: undefined,
});

const content = ref<string>('');
const loading = ref<boolean>(false);
const error = ref<string>('');

// 解析 DOCX 文件
const parseDocx = async (arrayBuffer: ArrayBuffer): Promise<string> => {
  try {
    // 动态导入 docx-preview 库
    const { renderAsync } = await import('docx-preview');

    // 创建一个临时容器来渲染文档
    const tempContainer = document.createElement('div');

    await renderAsync(arrayBuffer, tempContainer, undefined, {
      className: 'docx-wrapper',
      inWrapper: true,
      ignoreWidth: false,
      ignoreHeight: false,
      ignoreFonts: false,
      breakPages: true,
      ignoreLastRenderedPageBreak: true,
      experimental: true,
      trimXmlDeclaration: true,
    });

    return tempContainer.innerHTML;
  } catch (error_) {
    console.error('Failed to parse DOCX:', error_);
    throw new Error('无法解析 DOCX 文档');
  }
};

const fetchContent = async (): Promise<void> => {
  loading.value = true;
  error.value = '';

  try {
    // 直接从 blob URL 获取数据
    const response = await fetch(props.src, { headers: props.headers });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    const htmlContent = await parseDocx(arrayBuffer);
    content.value = htmlContent;
  } catch (error_) {
    error.value = error_ instanceof Error ? error_.message : '加载文档失败';
    console.error('Failed to fetch DOCX content:', error_);
  } finally {
    loading.value = false;
  }
};

const retry = async (): Promise<void> => {
  error.value = '';
  return await fetchContent();
};

defineExpose({
  retry,
});

watch(
  () => props.src,
  () => {
    if (!content.value && !error.value) {
      retry();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <div class="docx-content">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在解析 Word 文档...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <svg
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="15" y1="9" x2="9" y2="15" />
          <line x1="9" y1="9" x2="15" y2="15" />
        </svg>
      </div>
      <h3>加载失败</h3>
      <p>{{ error }}</p>
      <button @click="retry" class="retry-button">重新加载</button>
    </div>

    <!-- 文档内容 -->
    <div v-else-if="content" class="document-wrapper">
      <div class="document-content" v-html="content"></div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <svg
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
          />
          <polyline points="14 2 14 8 20 8" />
          <line x1="16" y1="13" x2="8" y2="13" />
          <line x1="16" y1="17" x2="8" y2="17" />
          <polyline points="10 9 9 9 8 9" />
        </svg>
      </div>
      <p>暂无文档内容</p>
    </div>
  </div>
</template>
<style>
.docx-content {
  position: relative;
  flex: 1;
  overflow: hidden;
  background-color: #f5f5f5;
}

.loading {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.error-state,
.empty-state {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  background-color: white;
}

.error-icon,
.empty-icon {
  margin-bottom: 16px;
  color: #ccc;
}

.error-state h3 {
  margin: 0 0 8px;
  color: #333;
}

.error-state p,
.empty-state p {
  margin: 0 0 16px;
  text-align: center;
}

.retry-button {
  padding: 8px 16px;
  color: white;
  cursor: pointer;
  background-color: #007bff;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.retry-button:hover {
  background-color: #0056b3;
}

.document-wrapper {
  height: 100%;
  padding: 20px;
  overflow: auto;
  background-color: white;
}

.document-content {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 0 10px rgb(0 0 0 / 10%);
}

/* DOCX 文档样式 */
.document-content :deep(.docx-wrapper) {
  padding: 40px;
  font-family: 'Times New Roman', Times, serif;
  line-height: 1.6;
  color: #333;
}

.document-content :deep(.docx-wrapper p) {
  margin: 0 0 12px;
}

.document-content :deep(.docx-wrapper h1) {
  margin: 20px 0 16px;
  font-size: 24px;
  font-weight: bold;
  color: #222;
}

.document-content :deep(.docx-wrapper h2) {
  margin: 18px 0 14px;
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.document-content :deep(.docx-wrapper h3) {
  margin: 16px 0 12px;
  font-size: 16px;
  font-weight: bold;
  color: #444;
}

.document-content :deep(.docx-wrapper table) {
  width: 100%;
  margin: 16px 0;
  border-collapse: collapse;
}

.document-content :deep(.docx-wrapper table td),
.document-content :deep(.docx-wrapper table th) {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #ddd;
}

.document-content :deep(.docx-wrapper table th) {
  font-weight: bold;
  background-color: #f5f5f5;
}

.document-content :deep(.docx-wrapper ul),
.document-content :deep(.docx-wrapper ol) {
  padding-left: 24px;
  margin: 12px 0;
}

.document-content :deep(.docx-wrapper li) {
  margin: 4px 0;
}

.document-content :deep(.docx-wrapper img) {
  max-width: 100%;
  height: auto;
  margin: 12px 0;
}

.document-content :deep(.docx-wrapper .page-break) {
  padding-top: 40px;
  margin-top: 40px;
  border-top: 1px dashed #ccc;
  page-break-before: always;
}
</style>
