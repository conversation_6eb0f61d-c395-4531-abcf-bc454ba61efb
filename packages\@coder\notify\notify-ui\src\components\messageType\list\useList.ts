import type {
  MessageTypeListItem,
  MessageTypeSearcher,
} from '@coder/notify-api';
import type { MessageArgsProps } from 'ant-design-vue';

import { computed, reactive, ref, toRaw } from 'vue';

import { getMessageTypeApi } from '@coder/notify-api';
import { message } from 'ant-design-vue';

export const useList = () => {
  const dataSource = reactive<Array<MessageTypeListItem>>([]);
  const total = ref(0);
  const loading = ref(false);
  const searcherForm = reactive({
    name: '',
    page: 1,
    pageSize: 40,
  } as MessageTypeSearcher);

  const pagination = computed(() => {
    return {
      current: toRaw(searcherForm.page),
      pageSize: toRaw(searcherForm.pageSize),
      total: total.value,
    };
  });
  const _reload = () => {
    const api = getMessageTypeApi();
    loading.value = true;
    const t1 = api.list(toRaw(searcherForm)).then((resp) => {
      dataSource.splice(0);
      resp.forEach((el) => {
        dataSource.push(el);
      });
    });
    const t2 = api.count(toRaw(searcherForm)).then((resp) => {
      total.value = resp;
    });
    Promise.all([t1, t2]).then(() => {
      loading.value = false;
    });
  };

  return {
    changePage: (page: number, pageSize: number) => {
      searcherForm.page = page;
      searcherForm.pageSize = pageSize;
      _reload();
    },
    dataSource,
    deleteType: (id: number) => {
      const api = getMessageTypeApi();
      loading.value = true;
      api.deleteType(id).then((resp) => {
        message.info({
          content: () => resp.message,
        } as MessageArgsProps);
        _reload();
      });
    },
    loading,
    pagination,
    search: () => {
      searcherForm.page = 1;
      _reload();
    },
    searcherForm,
  };
};
