<script setup lang="ts">
import type { PreviewOption } from './usePreview';

import { nextTick, ref } from 'vue';

import { Button } from 'ant-design-vue';

import { makePreviewUrl } from './usePreview';

const props = defineProps<PreviewOption>();

const previewUrlRef = ref();
const previewUrl = ref();
const onPreview = () => {
  makePreviewUrl(props).then((str) => {
    previewUrl.value = str;
    nextTick(() => {
      previewUrlRef.value.click();
    });
  });
};
</script>

<template>
  <a ref="previewUrlRef" :href="previewUrl" target="blank"></a>
  <Button :size="props.size" @click="onPreview"><slot> 预览 </slot></Button>
</template>
