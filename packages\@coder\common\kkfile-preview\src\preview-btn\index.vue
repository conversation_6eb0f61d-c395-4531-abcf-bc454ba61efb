<script setup lang="ts">
import type { PreviewOption } from './usePreview';

import { nextTick, ref } from 'vue';

import { makePreviewUrl } from './usePreview';

const props = defineProps<PreviewOption>();

const previewUrlRef = ref();
const previewUrl = ref();
const onPreview = () => {
  makePreviewUrl(props).then((str) => {
    previewUrl.value = str;
    nextTick(() => {
      previewUrlRef.value.click();
    });
  });
};
</script>

<template>
  <div>
    <a ref="previewUrlRef" :href="previewUrl" target="_blank"></a>
    <span style="white-space: pre-line" :size="props.size" @click="onPreview">
      <slot>预览</slot>
    </span>
  </div>
</template>
