<script lang="ts" setup>
import type { Rule } from 'ant-design-vue/es/form';

import { computed, reactive, ref } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import {
  Button as <PERSON>utton,
  Card as ACard,
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Modal as AModal,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Textarea as ATextarea,
  Divider,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';

import SelectProject from '../../Projects/selectProject.vue';
import SelectSupplier from '../../Suppliers/selectSupplier.vue';

const props = defineProps(['open']);

const emits = defineEmits(['update:open', 'doSave']);

const formRef = ref();
const display = computed({
  get() {
    return props.open;
  },
  set(v) {
    emits('update:open', v);
  },
});

const formRules = reactive<Record<string, Rule[]>>({
  applyDate: [
    {
      message: '请输入申请时间',
      required: true,
      trigger: 'blur',
    },
  ],
  bookDate: [
    {
      message: '请选择签订日期',
      required: true,
      trigger: 'blur',
    },
  ],
  code: [
    {
      message: '请输入合同编号',
      required: true,
      trigger: 'blur',
    },
  ],
  contractPrice: [
    {
      message: '请输入合同单价',
      required: false,
      trigger: 'blur',
    },
  ],
  contractPriceInfo: [
    {
      message: '请输入合同单价说明',
      required: false,
      trigger: 'blur',
    },
  ],
  contractTotal: [
    {
      message: '请输入合同总价(万元)',
      required: false,
      trigger: 'blur',
    },
  ],
  contractTotalInfo: [
    {
      message: '请输入合同总价说明',
      required: false,
      trigger: 'blur',
    },
  ],
  contractType: [
    {
      message: '请选择合同类型',
      required: true,
      trigger: 'blur',
    },
  ],
  endDate: [
    {
      message: '请选择合同截止日期',
      required: true,
      trigger: 'blur',
    },
  ],
  name: [
    {
      message: '请输入合同名称',
      required: true,
      trigger: 'blur',
    },
  ],
  oppositeName: [
    {
      message: '请输入对方名称',
      required: true,
      trigger: 'change',
    },
  ],
  orgPath: [
    {
      message: '请输入所属组织机构',
      required: false,
      trigger: 'blur',
    },
  ],
  outline: [
    {
      message: '请输入合同概况',
      required: false,
      trigger: 'blur',
    },
  ],
  planBookDate: [
    {
      message: '请选择预计签订日期',
      required: true,
      trigger: 'blur',
    },
  ],
  projectName: [
    {
      message: '请输入项目名称',
      required: true,
      trigger: 'change',
    },
  ],
  promiseDateType: [
    {
      message: '请输入约定付款时间及方式',
      required: false,
      trigger: 'blur',
    },
  ],
  promiseServiceTerm: [
    {
      message: '请输入约定服务期限',
      required: false,
      trigger: 'blur',
    },
  ],
  serviceContent: [
    {
      message: '请输入服务内容',
      required: false,
      trigger: 'blur',
    },
  ],
  serviceShip: [
    {
      message: '请输入提供服务的船舶',
      required: false,
      trigger: 'blur',
    },
  ],
  serviceTerm: [
    {
      message: '请输入实际服务期限',
      required: false,
      trigger: 'blur',
    },
  ],
  startDate: [
    {
      message: '请选择合同开始日期',
      required: true,
      trigger: 'blur',
    },
  ],
  startPayDate: [
    {
      message: '请选择合同起算日期',
      required: true,
      trigger: 'blur',
    },
  ],
});

const formState = reactive<{
  applyDate: string;
  bookDate: string;
  bookType: number | string;
  code: string;
  contractPrice: number | string;
  contractPriceInfo: string;
  contractTotal: number | string;
  contractTotalInfo: string;
  contractType: number | string;
  endDate: string;
  name: string;
  oppositeName: string;
  orgPath: string;
  outline: string;
  planBookDate: string;
  projectName: string;
  promiseDateType: string;
  promiseServiceTerm: string;
  serviceContent: string;
  serviceShip: string;
  serviceTerm: string;
  startDate: string;
  startPayDate: string;
}>({
  applyDate: '',
  bookDate: '',
  bookType: '0',
  code: '',
  contractPrice: '0',
  contractPriceInfo: '',
  contractTotal: '0',
  contractTotalInfo: '',
  contractType: '',
  endDate: '',
  name: '',
  oppositeName: '',
  orgPath: '',
  outline: '',
  planBookDate: '',
  projectName: '',
  promiseDateType: '',
  promiseServiceTerm: '',
  serviceContent: '',
  serviceShip: '',
  serviceTerm: '',
  startDate: '',
  startPayDate: '',
});
const handleOk = (): void => {
  formRef.value?.validate().then(() => {
    emits('doSave', { ...formState });
  });
};

const handleReset = () => {
  formRef.value?.resetFields();
};

const handelCancel = () => {
  handleReset();
  emits('update:open', false);
};

defineExpose({ handelCancel });
</script>
<template>
  <AModal
    v-model:open="display"
    :mask-closable="false"
    style="top: 5vh; width: 90%"
    title="新增合同信息"
    @cancel="handelCancel"
    @ok="handleOk"
  >
    <ACard>
      <AForm
        ref="formRef"
        :label-col="{ span: 8 }"
        :model="formState"
        :rules="formRules"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        name="basic"
      >
        <ARow :gutter="16">
          <ACol :span="8">
            <AFormItem has-feedback label="合同名称" name="name">
              <AInput
                v-model:value="formState.name"
                placeholder="请输入合同名称"
                autocomplete="off"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="合同编号" name="code">
              <AInput
                v-model:value="formState.code"
                placeholder="请输入合同编号"
                autocomplete="off"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="合同类型" name="contractType">
              <ASelect
                v-model:value="formState.contractType"
                placeholder="请选择合同类型"
                style="width: 100%"
              >
                <ASelectOption value="0">收款合同</ASelectOption>
                <ASelectOption value="1">付款合同</ASelectOption>
              </ASelect>
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="签订方式" name="bookType">
              <ASelect
                v-model:value="formState.bookType"
                placeholder="请选择签订方式"
                style="width: 100%"
              >
                <ASelectOption value="0" checked>总公司签订</ASelectOption>
                <ASelectOption value="1">分公司签订</ASelectOption>
                <ASelectOption value="2">分报总公司签订</ASelectOption>
              </ASelect>
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="申请时间" name="applyDate">
              <ADatePicker
                v-model:value="formState.applyDate"
                :locale="locale"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="项目名称" name="projectName">
              <SelectProject v-model:model-value="formState.projectName" />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="对方名称" name="oppositeName">
              <SelectSupplier v-model:model-value="formState.oppositeName" />
            </AFormItem>
          </ACol>
          <ACol :span="0">
            <AFormItem has-feedback hidden label="所属组织机构" name="orgPath">
              <AInput v-model:value="formState.orgPath" autocomplete="off" />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="预计签订日期" name="planBookDate">
              <ADatePicker
                v-model:value="formState.planBookDate"
                :locale="locale"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="签订日期" name="bookDate">
              <ADatePicker
                v-model:value="formState.bookDate"
                :locale="locale"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="合同起算日期" name="startPayDate">
              <ADatePicker
                v-model:value="formState.startPayDate"
                :locale="locale"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="合同开始日期" name="startDate">
              <ADatePicker
                v-model:value="formState.startDate"
                :locale="locale"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="合同截止日期" name="endDate">
              <ADatePicker
                v-model:value="formState.endDate"
                :locale="locale"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <Divider style="margin-top: 0" />
          <ACol :span="8">
            <AFormItem has-feedback label="合同单价" name="contractPrice">
              <AInputNumber
                v-model:value="formState.contractPrice"
                autocomplete="off"
                min="0"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem
              has-feedback
              label="合同单价说明"
              name="contractPriceInfo"
            >
              <ATextarea
                v-model:value="formState.contractPriceInfo"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8" />

          <ACol :span="8">
            <AFormItem has-feedback label="合同总价(万元)" name="contractTotal">
              <AInputNumber
                v-model:value="formState.contractTotal"
                autocomplete="off"
                min="0"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>

          <ACol :span="8">
            <AFormItem
              has-feedback
              label="合同总价说明"
              name="contractTotalInfo"
            >
              <ATextarea
                v-model:value="formState.contractTotalInfo"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <Divider style="margin-top: 0" />
          <ACol :span="8">
            <AFormItem has-feedback label="合同概况" name="outline">
              <ATextarea
                v-model:value="formState.outline"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem
              has-feedback
              label="约定付款时间及方式"
              name="promiseDateType"
            >
              <ATextarea
                v-model:value="formState.promiseDateType"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem
              has-feedback
              label="约定服务期限"
              name="promiseServiceTerm"
            >
              <ATextarea
                v-model:value="formState.promiseServiceTerm"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="实际服务期限" name="serviceTerm">
              <ATextarea
                v-model:value="formState.serviceTerm"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="服务内容" name="serviceContent">
              <ATextarea
                v-model:value="formState.serviceContent"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
          <ACol :span="8">
            <AFormItem has-feedback label="提供服务的船舶" name="serviceShip">
              <ATextarea
                v-model:value="formState.serviceShip"
                autocomplete="off"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
        </ARow>
      </AForm>
    </ACard>
    <template #footer>
      <div
        style="
          display: flex;
          justify-content: space-between;
          width: 20%;
          margin: 0 auto;
        "
      >
        <AButton @click="handelCancel">
          <CloseOutlined />
          取消
        </AButton>
        <AButton @click="handleReset">
          <RedoOutlined />
          重置
        </AButton>
        <AButton type="primary" @click="handleOk">
          <CheckOutlined />
          保存
        </AButton>
      </div>
    </template>
  </AModal>
</template>

<style scoped></style>
