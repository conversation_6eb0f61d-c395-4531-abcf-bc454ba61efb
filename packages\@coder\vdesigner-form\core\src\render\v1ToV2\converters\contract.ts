import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

/*
 {
              "key": 97641,
              "type": "link-contract",
              "icon": "alert",
              "formItemFlag": true,
              "options": {
                "name": "guanlian",
                "label": "关联合同",
                "folded": false,
                "showFold": true,
                "cardWidth": "100%",
                "shadow": "never",
                "customClass": [],
                "defaultValue": null,
                "disabled": false,
                "hidden": false,
                "labelAlign": "label-right-align",
                "labelHidden": false,
                "labelWidth": "100",
                "readonly": false,
                "size": "",
                "onChange": ""
              },
              "id": "linkcontract84625"
            }*/
export class ContractLinkConvert extends V1Convert {
  constructor() {
    super('link-contract', 'link-contract');
  }
  override SetOption(v1Widget: any, v2: Widget): void {
    const v1 = v1Widget.options;
    v2.options = {
      name: v1.name,
      label: v1.label,
      hidden: v1.hidden,
    };
  }
}
/* {
              "key": 82466,
              "type": "select-contract",
              "icon": "alert",
              "formItemFlag": true,
              "options": {
                "name": "guanlian",
                "label": "关联合同",
                "folded": false,
                "showFold": true,
                "cardWidth": "100%",
                "shadow": "never",
                "customClass": [],
                "defaultValue": null,
                "disabled": false,
                "hidden": false,
                "labelAlign": "label-right-align",
                "labelHidden": false,
                "labelWidth": "100",
                "readonly": true,
                "required": false,
                "requiredHint": "",
                "isLock": false,
                "isMaster": true,
                "isWorkloadLock": false,
                "attributeOrgPath": "",
                "attributeContractType": "",
                "attributeBookType": "",
                "size": "",
                "onCreated": "",
                "onChange": ""
              },
              "id": "selectcontract101642"
            }*/
export class ContractSelectConvert extends V1Convert {
  constructor() {
    super('select-contract', 'select-contract');
  }
  override SetOption(
    v1Widget: any,
    v2: Widget,
    _cfg: Record<string, any>,
  ): void {
    const v1 = v1Widget.options;
    v2.options = {
      name: v1.name,
      label: v1.label,
      hidden: v1.hidden,
      orgPath: v1.attributeOrgPath,
      contractType: v1.attributeContractType,
      bookType: v1.attributeBookType,
    };
  }
}

/*
 "key": 37716,
              "type": "select-contractGroup",
              "icon": "alert",
              "formItemFlag": true,
              "options": {
                "name": "contractGroupId",
                "label": "选择合同组",
                "folded": false,
                "showFold": true,
                "cardWidth": "100%",
                "shadow": "never",
                "customClass": [],
                "defaultValue": null,
                "disabled": false,
                "hidden": false,
                "labelAlign": "label-right-align",
                "labelHidden": false,
                "labelWidth": "100",
                "readonly": false,
                "size": "",
                "onChange": "var contractGroupName = this.getWidgetRef('contractGroupName')\r\ncontractGroupName.setValue(value.name)"
              },
              "id": "selectcontractGroup74237"
              */

export class ContractGroupSelectConvert extends V1Convert {
  constructor() {
    super('select-contractGroup', 'select-contractGroup');
  }
  override SetOption(v1Widget: any, v2: Widget): void {
    const v1 = v1Widget.options;
    v2.options = {
      name: v1.name,
      label: v1.label,
      hidden: v1.hidden,
      /**
       * 全局只有一个地方使用到，所以直接转换。
       */
      onChange: `
      // data 是 附加属性
      formData.contractGroupName = data.name
      `,
    };
  }
}
