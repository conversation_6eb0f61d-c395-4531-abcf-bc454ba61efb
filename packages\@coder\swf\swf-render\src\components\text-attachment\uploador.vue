<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { UploadButton } from '@coder/file-upload';
import { createWorkflowApi } from '@coder/swf-api';

import { swfOption } from '../../swfOption';

const props = defineProps<{ processInstanceId: number | string }>();
const fileName = ref('');
const _url = computed(() => {
  if (!swfOption.host.startsWith('http'))
    return `api/${swfOption.host}/workflow/upload_text/${props.processInstanceId}`;
  return `${swfOption.host}/workflow/upload_text/${props.processInstanceId}`;
});
const headers = computed(() => {
  return {
    Authorization: `Bearer ${swfOption.getToken()}`,
  };
});

const updateTextAttachmentInfo = async () => {
  const files = await createWorkflowApi(
    swfOption.request,
    swfOption.host,
  ).getFilesTextByProcessInstanceId(props.processInstanceId);

  const file = files[0];
  if (file) {
    fileName.value = file.fileName;
  }
};

onMounted(async () => {
  updateTextAttachmentInfo();
});
const onSuceess = async () => {
  await updateTextAttachmentInfo();
};
</script>

<template>
  <div>
    <UploadButton
      :axios="swfOption.request.instance"
      :headers="headers"
      :upload-url="_url"
      file-form-key="file"
      btn-text="上传正文"
      @success="onSuceess"
    />
    {{ fileName }}
  </div>
</template>
