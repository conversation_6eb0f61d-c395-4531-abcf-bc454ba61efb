import type { RequestClient } from '@vben/request';

import type { ResponseMessage } from '../types';

export const createFolderPermissionApi = (
  request: RequestClient,
  basePath: string,
) => {
  const path = `${basePath}/FolderPermission`;

  const save = async (params: any): Promise<ResponseMessage> => {
    const data = await request.post(`${path}/save`, params);
    return data;
  };

  const searchPerformers = async (params: any): Promise<any> => {
    const data = await request.get(`${path}/search-performers`, {
      params,
    });
    return data;
  };

  const searchPerformersCount = async (params: any): Promise<number> => {
    const data = await request.get(`${path}/search-performers-count`, {
      params,
    });
    return data;
  };

  return {
    save,
    searchPerformers,
    searchPerformersCount,
  };
};
