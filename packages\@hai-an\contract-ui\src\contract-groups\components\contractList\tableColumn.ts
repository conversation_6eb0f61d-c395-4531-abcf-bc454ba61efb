import type { ColumnType } from 'ant-design-vue/es/table';

import dayjs from 'dayjs';

const dayF = (val: any) => {
  return dayjs(val).format('YYYY-MM-DD');
};
export default [
  {
    dataIndex: 'code',
    slots: { customRender: 'code' },
    title: '合同编号',
    width: 200,
  },
  {
    dataIndex: 'name',
    slots: { customRender: 'name' },
    title: '合同名称',
  },
  {
    dataIndex: 'projectName',
    slots: { customRender: 'projectName' },
    title: '项目名称',
  },
  {
    dataIndex: 'oppositeName',
    slots: { customRender: 'oppositeName' },
    title: '对方名称',
  },
  {
    customRender: ({ record }) => {
      return dayF(record.bookDate);
    },
    dataIndex: 'bookDate',
    slots: { customRender: 'bookDate' },
    title: '签订日期',
  },
  {
    dataIndex: 'contractTotal',
    slots: { customRender: 'contractTotal' },
    title: '合同总价',
  },
  {
    dataIndex: 'completeWorkload',
    slots: { customRender: 'completeWorkload' },
    title: '已完成工作量',
  },
  {
    dataIndex: 'payCount',
    slots: { customRender: 'payCount' },
    title: '已结算次数',
  },
  {
    dataIndex: 'payTotal',
    slots: { customRender: 'payTotal' },
    title: '已结算金额',
  },
] as ColumnType[];
