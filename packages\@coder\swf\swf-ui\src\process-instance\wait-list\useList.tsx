import type {
  InstanceListItemViewModel,
  ProcessInstanceSearcher,
} from '@coder/swf-api';
import type { PaginationProps } from 'ant-design-vue';

import type { EmitsTypes } from './types';

import { computed, reactive, ref, toRaw } from 'vue';

import { createWorkflowApi, ProcessInstanceStatus } from '@coder/swf-api';
import { swfOption } from '@coder/swf-render';

const useList = (_props: any, _emits: EmitsTypes) => {
  const dataSource = reactive([] as Array<InstanceListItemViewModel>);
  const loading = ref(false);
  const searcherForm = reactive({
    name: new Array<string>(),
    number: '',
    page: 1,
    pageSize: 50,
    status: [ProcessInstanceStatus.Processing],
    tags: new Array<string>(),
  } as ProcessInstanceSearcher);

  const total = ref(0);

  const pager = computed(() => {
    return {
      current: searcherForm.page,
      pageSize: searcherForm.pageSize,
      showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
      total: total.value,
    };
  });

  const reload = () => {
    const api = createWorkflowApi(swfOption.request, swfOption.host);
    loading.value = true;

    const dataTask = api
      .listProcessInstance(toRaw(searcherForm))
      .then((resp) => {
        dataSource.splice(0);
        dataSource.push(...resp);
      });
    const countTask = api
      .countProcessInstance(toRaw(searcherForm))
      .then((resp) => {
        total.value = resp;
      });

    Promise.all([countTask, dataTask]).finally(() => (loading.value = false));
  };

  const onSearch = () => {
    searcherForm.page = 1;
    reload();
  };

  const onPagerChanged = (pagination: PaginationProps) => {
    if (pagination.pageSize) {
      searcherForm.pageSize = pagination.pageSize;
    }
    if (pagination.current) {
      searcherForm.page = pagination.current;
    }
    reload();
  };

  return {
    dataSource,
    loading,
    pager,
    reload,
    searcherForm,
    onSearch,
    onPagerChanged,
  };
};

export default useList;
