import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

export class SupplierSelectConvert extends V1Convert {
  constructor() {
    super('select-supplier', 'select-supplier');
  }
  override SetOption(v1: any, v2: Widget, _cfg: Record<string, any>): void {
    const v1Option = v1.options;
    // eslint-disable-next-line no-console
    console.log('v1-select-supplier', v1);
    v2.options = {
      name: v1Option.name,
      label: v1Option.label,
      hidden: v1Option.hidden,
    };
  }
}
