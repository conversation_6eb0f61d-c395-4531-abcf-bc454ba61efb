{"name": "@hai-an/document-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "main": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/common-api": "workspace:*", "@coder/file-download": "workspace:*", "@coder/file-uploador": "workspace:*", "@coder/fs-api": "workspace:^", "@coder/kkfile-preview": "workspace:*", "@coder/system-api": "workspace:*", "@coder/system-ui": "workspace:^", "@vben/hooks": "workspace:*", "@coder/toolbar": "workspace:^", "@hai-an/document-api": "workspace:*", "@vben/icons": "workspace:*", "@vben/request": "workspace:*", "@vben/utils": "workspace:^", "ant-design-vue": "catalog:", "dayjs": "catalog:", "naive-ui": "catalog:", "splitpanes": "catalog:coder", "vue": "catalog:", "vue-clipboard3": "catalog:coder"}, "devDependencies": {"@types/spark-md5": "catalog:coder", "@types/splitpanes": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}