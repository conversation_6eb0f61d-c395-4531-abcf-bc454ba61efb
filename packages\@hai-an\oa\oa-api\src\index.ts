import type { RequestClient } from '@vben/request';

export * from './maintenanceApi';
// export * from './types';
export * from './workflowApi';

export const Test = () => {
  return '我是oa-api------';
};

export interface IHaianOAOption {
  getToken: { (): string };
  path: string;
  previewHost?: string;
  request: RequestClient;
  // swfPath: string;
  workflowPath: string;
}

export const haianOAOption = {} as IHaianOAOption;

export const api = {
  install: (_app: any, options: IHaianOAOption) => {
    if (options.path.endsWith('/')) {
      options.path = options.path.slice(
        0,
        Math.max(0, options.path.length - 1),
      );
    }

    Object.assign(haianOAOption, options);
  },
};

export default api;
