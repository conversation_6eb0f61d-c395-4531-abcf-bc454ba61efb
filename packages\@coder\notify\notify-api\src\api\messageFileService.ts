import type { SimpleSFileViewModel } from '../types';

import { RequestClient } from '@vben/request';

export const createMessageFileApi = (
  request: RequestClient,
  notifyPath: string,
) => {
  notifyPath += '/messageFile';
  const delByMessageFile = (messageId: number, fileId: string) => {
    return request.delete(`${notifyPath}/remove-file/${messageId}/${fileId}`);
  };

  const delFile = (
    fileId: string,
    messageId: number = 0,
  ): Promise<{ message: string; success: boolean }> => {
    return request.delete(`${notifyPath}/remove-file/${messageId}/${fileId}`);
  };

  const getDownloadFilePath = (fileId: string, fileName: string): string => {
    const path = `api/${notifyPath}/get-file-by-id/${fileId}/${fileName}`;
    return path;
  };
  const getUploadFilePath = (messageId: number | undefined) => {
    return messageId
      ? `${notifyPath}/upload-file/${messageId}`
      : `${notifyPath}/upload-file`;
  };

  const listByMessage = (
    messageId: number,
  ): Promise<Array<SimpleSFileViewModel>> => {
    return request.get(`${notifyPath}/list-files/${messageId}`);
  };

  const listFiles = (
    fileIds: Array<string>,
  ): Promise<Array<SimpleSFileViewModel>> => {
    const ids = new Array<string>();
    fileIds.forEach((element: string) => {
      ids.push(`id=${element}`);
    });
    const query = ids.join('&');
    return request.get(`${notifyPath}/list-files?${query}`);
  };

  const getPreviewTicket = (fileId: string, messageId: number) => {
    return request.get(
      `${notifyPath}/get-preview-ticket/${messageId}/${fileId}`,
    );
  };

  return {
    delByMessageFile,
    delFile,
    getDownloadFilePath,
    getPreviewTicket,
    getUploadFilePath,
    listByMessage,
    listFiles,
  };
};
