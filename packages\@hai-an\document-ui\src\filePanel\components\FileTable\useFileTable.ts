import type {
  FileListItem,
  FileSearcher,
  FolderViewModel,
} from '@hai-an/document-api';

import { reactive, ref } from 'vue';

import { useFormData } from '@coder/common-api';
import { createFileApi, createFolderApi } from '@hai-an/document-api';

import { documentOptions } from '../../..';

type UploadStatus = FileListItem & {
  current: number;
  message: string;
  total: number;
};

export type FileItem = FileListItem & {
  uploadStatus: UploadStatus;
};

export const useFileTable = (props: { folderId: number }) => {
  const fileApi = createFileApi(documentOptions.request, documentOptions.path);
  const folderApi = createFolderApi(
    documentOptions.request,
    documentOptions.path,
  );
  const headers = {
    Authorization: () =>
      documentOptions.getToken ? documentOptions.getToken() : '',
    // 'Content-Type': 'multipart/form-data',
  };

  const folderViewModel = reactive<FolderViewModel>({} as FolderViewModel);
  const fileSearcher = reactive<FileSearcher>({
    deleteFlag: false,
    folderId: props.folderId,
    page: 1,
    pageSize: 10,
  });
  /**
   * 根据folederId获取文件夹信息
   */
  const loadFolderViewModel = async () => {
    if (!props.folderId) return;
    try {
      const viewModel = await folderApi.getById(props.folderId);
      console.error('folderViewModel', viewModel);
      Object.assign(folderViewModel, viewModel);
    } catch (error) {
      console.error('Error loading folder view model:', error);
    }
  };

  /**
   * 添加了 上传需要的
   */
  const tableData = reactive<FileItem[]>([]);
  const tablePagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const tableLoading = ref(false);

  const loadTableData = async () => {
    tableLoading.value = true;
    try {
      const [files, total] = await Promise.all([
        fileApi.list(fileSearcher),
        fileApi.count(fileSearcher),
      ]);
      tableData.splice(0);
      files.forEach((item) => {
        tableData.push(item as any);
      });
      tablePagination.value.total = total;
    } finally {
      tableLoading.value = false;
    }
  };

  const handleTableChange = (pagination: any) => {
    if (!props.folderId) return;
    tablePagination.value.current = pagination.current;
    tablePagination.value.pageSize = pagination.pageSize;
    loadTableData();
  };

  const uploadFile = (file: File) => {
    const position = tableData.push({
      fileLength: file.size,
      name: file.name,
      uploadStatus: {
        current: 0,
        message: '上传中',
        total: file.size,
      },
    } as unknown as FileItem);
    const doc = tableData[position - 1];
    if (!doc) throw new Error('文件不存在');
    doc.id = -1 * position;

    useFormData({
      method: 'post',
      url: () => {
        const fileApi = createFileApi(
          documentOptions.request,
          `api${documentOptions.path}`,
        );

        return fileApi.getUploadUrl(props.folderId);
      },
    })
      .addHeaders(headers)
      .addFile('file', file)
      .addProgressEvent((opt) => {
        if (doc) {
          doc.uploadStatus.current = opt.loaded;
          doc.uploadStatus.total = opt.total;
        }
      })
      .submit()
      .then((result) => {
        if (result.complete === 'success' && result.response) {
          result.response.json().then((_resp: any) => {
            // Handle upload success
            doc.uploadStatus.message = '上传成功';
            // 上传成功后刷新列表
            setTimeout(() => {
              loadTableData();
            }, 500); // 延迟500ms确保服务器处理完成
          });
        } else {
          // Handle upload error
          doc.uploadStatus.message = '上传失败';
        }
      })
      .catch((error) => {
        // Handle upload error
        doc.uploadStatus.message = '上传失败';
        console.error('Upload error:', error);
      });
  };

  return {
    handleTableChange,
    loadTableData,
    loadFolderViewModel,
    tableData,
    tableLoading,
    tablePagination,
    folderViewModel,
    uploadFile,
    fileSearcher,
  };
};
