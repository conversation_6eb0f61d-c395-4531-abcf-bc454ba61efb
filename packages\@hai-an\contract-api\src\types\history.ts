import type { BookType, ContractType } from '../types';

export interface ContractHistoryViewModel {
  bookType: BookType;
  changeComment: string;
  changeTime: string;
  changeUserName: string;
  contractNumber: string;
  contractNumberName: string;
  contractType: ContractType;
  diff: string[];
}

export interface ContractHistorySearch {
  contractNumber?: string;
  page?: number;
  pageSize?: number;
}
