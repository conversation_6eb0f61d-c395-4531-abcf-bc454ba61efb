<script setup lang="ts">
import type { FileListItem } from '@hai-an/document-api';

import type { PropType } from 'vue';

import { computed } from 'vue';

import { createFileApi } from '@hai-an/document-api';
import {
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Modal as AModal,
  message,
} from 'ant-design-vue';

import { documentOptions } from '..';

const props = defineProps({
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  files: {
    type: Array as PropType<any | FileListItem[]>,
    // type: Array,
    default: () => [],
  },
  fileIds: {
    type: Array as PropType<(number | string)[]>,
    default: () => {},
  },
});

const emit = defineEmits(['update:visible', 'clear']);

const fileApi = createFileApi(documentOptions.request!, documentOptions.path);

const isVisible = computed(() => props.visible);
const fileName = computed(() => {
  if (props.files.length === 0) return '';
  return `${props.files[0].name}等文件`;
});

const close = () => {
  emit('update:visible', false);
};

const handleOk = async () => {
  try {
    const formData = {
      name: fileName.value,
      fileId: props.fileIds,
    } as any;
    const res: any = await fileApi.packFiles(formData);
    message.info(res.message || '成功请求创建打包文件');
    emit('clear');
  } catch {}
};
</script>

<template>
  <AModal
    v-model:visible="isVisible"
    @ok="handleOk"
    ok-text="批量下载"
    cancel-text="取消"
    @cancel="close"
  >
    <ACard>
      <AForm>
        <AFormItem>
          <AInput v-model:value="fileName" :disabled="true" />
        </AFormItem>
      </AForm>
    </ACard>
  </AModal>
</template>

<style scoped></style>
