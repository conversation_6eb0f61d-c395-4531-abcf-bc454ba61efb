<!-- 车辆管理列表 -->
<script setup lang="ts">
import type { CarSubmit } from '@hai-an/car-api';

import { createVNode, onMounted, reactive, ref } from 'vue';

import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { CreateCarApi } from '@hai-an/car-api';
import {
  Button as AButton,
  Card as ACard,
  Modal as AModal,
  message,
  Modal,
} from 'ant-design-vue';

import { haianCarOption } from '../haianCarOption';
import CarEidt from './components/edit.vue';
import CarForm from './components/form.vue';
import CarTable from './components/table.vue';

const api = CreateCarApi(haianCarOption.carPath, haianCarOption.request);
const editTitle = ref('');
const infoTitle = ref('');
const isInfo = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const editId = ref(0);
const infoId = ref(0);
const searchForm = reactive({
  page: 0,
  pageSize: 10,
});
const data = reactive<CarSubmit[]>([]);
const pagination = reactive({
  current: 1, // 当前页数
  pageSize: 10, // 每页中显示10条数据
  pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
  total: 0,
});

const filter = () => {
  loading.value = true;

  api.list(searchForm).then((resp) => {
    data.splice(0);
    resp.forEach((item) => data.push(item));
    loading.value = false;
  });

  api.count(searchForm).then((resp: number) => {
    pagination.total = resp;
  });
};
const search = () => {
  searchForm.page = 1;
  pagination.current = 1;

  filter();
};
const onAdd = () => {
  editTitle.value = '新增车辆信息';
  isEdit.value = true;
  editId.value = 0;
};
const onEdit = (val: any) => {
  editTitle.value = '编辑车辆信息';
  isEdit.value = true;
  editId.value = val.record.id;
};
const onInfo = (val: any) => {
  infoTitle.value = '车辆详细信息';
  isInfo.value = true;
  infoId.value = val.record.id;
};

const canceled = () => {
  isEdit.value = false;
  isInfo.value = false;
};
const saved = () => {
  canceled();
  search();
};
const onSearch = (val: any) => {
  Object.assign(searchForm, val); // 赋值到searchForm
  search();
};

// 分页按钮调用
const handleTableChange = (val: any) => {
  pagination.current = val.current;
  pagination.pageSize = val.pageSize;
  searchForm.page = val.current;
  searchForm.pageSize = val.pageSize;
  filter();
};

const onDel = (val: any) => {
  Modal.confirm({
    cancelText: '取消',
    content: () =>
      createVNode(
        'div',
        { style: 'color:red;' },
        '是否确定删除选定车辆信息?删除后将无法恢复！',
      ),
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: '确认删除',
    onCancel() {
      message.info('取消删除！');
    },
    onOk() {
      api.delete(val.record.id).then((res: any) => {
        if (res.success) {
          message.success(res.message);
          filter();
        } else {
          message.error(res.message);
        }
      });
    },
    title: () => '删除确认',
  });
};

onMounted(() => {
  search();
});
</script>
<template>
  <ACard>
    <CarForm @on-search="onSearch" />
    <!-- {{ SelectCard }}
    <select-car v-model="SelectCard" />
    <LableCar :carId="carId"></LableCar> -->
  </ACard>
  <ACard>
    <div class="space-align-container">
      <AButton shape="round" size="small" type="primary" @click="onAdd">
        <PlusOutlined />新增
      </AButton>
    </div>
    <CarTable
      :data="data"
      :loading="loading"
      :pagination="pagination"
      @handle-table-change="handleTableChange"
      @to-del="onDel"
      @to-edit="onEdit"
      @to-info="onInfo"
    />
  </ACard>
  <AModal
    :destroy-on-close="true"
    :mask-closable="false"
    :open="isEdit"
    :title="editTitle"
    :width="700"
    footer=""
    @cancel="canceled"
  >
    <CarEidt :id="editId" @do-cancel="canceled" @do-save="saved" />
  </AModal>

  <AModal
    :destroy-on-close="true"
    :mask-closable="false"
    :open="isInfo"
    :title="infoTitle"
    :width="1200"
    footer=""
    @cancel="canceled"
  >
    <CarInfo :id="infoId" @do-cancel="canceled" />
  </AModal>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
