<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import {
  createContractApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import { Table as ATable, Tag as ATag } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  projectId: { type: Number, default: null },
  code: {type: String, default: ''}
});

const api = createContractApi(options.request, options.path);

const loading = ref(false);
const datas = reactive<any[]>([]);
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total: number) => `共有 ${total} 条数据`,
});

const searchForm = reactive({
  page: 1,
  pageSize: 10,
  projectId: 0,
  projectArchiveCode: ''
});

// 日期格式化
const dayF = (val: Date | number | string) => {
  return dayjs(val).format('YYYY-MM-DD');
};

// 表格列定义
const columns = reactive([
  {
    title: '合同编号',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '合同名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '合同类型',
    dataIndex: 'contractType',
    key: 'contractType',
  },
  {
    title: '签订日期',
    dataIndex: 'bookDate',
    key: 'bookDate',
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
  },
  {
    title: '截止日期',
    dataIndex: 'endDate',
    key: 'endDate',
  },
]);

// 加载数据
const loadData = async () => {
  if (props.projectId) {
    searchForm.projectId = props.projectId;
    searchForm.projectArchiveCode = props.code
    loading.value = true;
    try{
      // const [res, count] = await Promise.all([api.listByProjectId(searchForm), api.countByProjectId(searchForm)])
      const [res, count] = await Promise.all([api.list(searchForm), api.count(searchForm)])
      datas.splice(0);
      datas.push(...res);
      pagination.total = res;
      loading.value = false
    }catch(err){
      loading.value = false
    }
  }
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  searchForm.page = pag.current;
  searchForm.pageSize = pag.pageSize;
  loadData();
};

onMounted(() => {
  loadData();
});
</script>
<template>
  <!-- 关联合同 -->
  <div>
    <ATable
      class="ant-table-striped"
      size="middle"
      :row-class-name="
        (_record, index) => (index % 2 === 1 ? 'table-striped' : '')
      "
      bordered
      :row-key="(data) => data.id"
      :pagination="pagination"
      :columns="columns"
      :loading="loading"
      @change="handleTableChange"
      :data-source="datas"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'code'">
          <a>{{ record.code }}</a>
        </template>
        <template v-if="column.dataIndex === 'contractType'">
          <ATag :color="record.contractType === 0 ? 'green' : 'volcano'">
            {{ record.contractType === 0 ? '收款合同' : '付款合同' }}
          </ATag>
        </template>
        <template v-if="column.dataIndex === 'bookDate'">
          {{ record.bookDate ? dayF(record.bookDate) : '' }}
        </template>
        <template v-if="column.dataIndex === 'startDate'">
          {{ record.startDate ? dayF(record.startDate) : '' }}
        </template>
        <template v-if="column.dataIndex === 'endDate'">
          {{ record.endDate ? dayF(record.endDate) : '' }}
        </template>
      </template>
    </ATable>
  </div>
</template>
<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
