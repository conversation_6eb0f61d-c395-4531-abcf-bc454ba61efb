import type { ProjectSearch, ProjectViewModel } from '@hai-an/contract-api';
import type { TablePaginationConfig } from 'ant-design-vue';

import { computed, reactive, ref } from 'vue';

import { createProjectApi, haianContractOption } from '@hai-an/contract-api';

export const useProjectList = () => {
  const api = createProjectApi(
    haianContractOption.request,
    haianContractOption.path,
  );
  const searchForm = reactive<ProjectSearch>({
    page: 1,
    pageSize: 10,
  });
  const loading = ref(false);
  const total = ref(0);
  const dataSource = reactive<ProjectViewModel[]>([]);

  const filter = () => {
    loading.value = true;
    api.list(searchForm).then((res) => {
      dataSource.splice(0);
      dataSource.push(...res);
      loading.value = false;
    });
    api
      .count({
        code: searchForm.code,
        name: searchForm.name,
      })
      .then((res) => {
        pagination.value.total = res;
        total.value = res;
      });
  };

  const search = () => {
    searchForm.page = 1;
    filter();
  };

  const pagination = computed(() => {
    return {
      pageSize: searchForm.pageSize,
      pageSizeOptions: ['10', '20', '50', '100'],
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total: any) => `共有 ${total} 条数据`,
      total: total.value,
    } as TablePaginationConfig;
  });

  return {
    searchForm,
    pagination,
    dataSource,
    search,
    filter,
    loading,
    pageChange: (page: TablePaginationConfig) => {
      searchForm.page = page.current ?? 1;
      searchForm.pageSize = page.pageSize ?? 10;
      filter();
    },
  };
};
