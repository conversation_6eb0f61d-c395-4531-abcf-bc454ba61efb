<script lang="ts" setup>
import { useRoute } from 'vue-router';

import { PreviewContent } from '@coder/preview';

defineOptions({
  name: 'PreviewDocument',
});
const route = useRoute();
const url = route.query.url as string;
</script>

<template>
  <div class="preview-container">
    <PreviewContent :url="url">
      <!-- com:{
meta:{
 title :'文件预览',
 hideInMenu:true,
},
name:'PreviewDocument',
path:'/preview'
      } -->
    </PreviewContent>
  </div>
</template>

<style scoped>
.preview-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
}

.preview-container :deep(.preview-content) {
  flex: 1;
  height: 100%;
}
</style>
