<script lang="ts" setup>
import type { WidgetPropsType } from '@coder/vdesigner-core';

import type { CarSelectorOption } from './_options';

import { computed } from 'vue';

import {
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { SelectCar } from '@hai-an/car-ui';

const props = defineProps<WidgetPropsType>();
const { getComponent } = useWidgetRegistry();
const { widget, value } = useWidget(props.widget, props.renderId);
const renderStore = useRenderStore(props.renderId);
const options = widget.value.options as CarSelectorOption;
const CoderVDesignFormItem = getComponent(
  'CoderVDesignFormItem',
  renderStore.implement,
);
const style = computed(() => {
  return {
    display: options.hidden ? 'none' : 'block',
  };
});
</script>
<template>
  <CoderVDesignFormItem v-bind="props">
    <SelectCar v-model="value" :style="style" />
  </CoderVDesignFormItem>
</template>
