import type { RequestClient } from '@vben/request';
// import { RequestClient } from '@vben/request';

export interface WorkflowSearch {
  page?: number;
  pageSize?: number;
}

export interface WorkflowListItem {
  canCreateProcessInstance: boolean;
  childNodes: any;
  creator: string;
  group: string;
  icon: string;
  id: number;
  isManager: boolean;
  isWorkProcessCreator: boolean;
  name: string;
  publish: boolean;
  version: number;
}

export const createWorkflowApi = (request: RequestClient, host: string) => {
  // const path = '/swf/WorkProcess'
  const path = `${host}/WorkProcess`;
  return {
    list(params: WorkflowSearch): Promise<WorkflowListItem[]> {
      // return request.get(`${path}/swf/WorkProcess/list`, { params });
      return request.get(`${path}/list`, { params });
    },
  };
};
