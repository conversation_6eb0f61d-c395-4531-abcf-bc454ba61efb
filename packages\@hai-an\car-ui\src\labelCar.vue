<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { CreateCarApi } from '@hai-an/car-api';

import { haianCarOption } from './haianCarOption';

const props = defineProps({
  carId: { default: () => 0, type: Number },
});
const api = CreateCarApi(haianCarOption.carPath, haianCarOption.request);

const carInfo = ref('');
const toSearch = () => {
  if (props.carId === 0) {
    carInfo.value = '';
  } else {
    api.getById(props.carId).then((res: any) => {
      carInfo.value = `${res.data.carNo}（${res.data.driverName}:${
        res.data.driverPhone
      })`;
    });
  }
};

watch(
  () => props.carId,
  () => {
    toSearch();
  },
);
onMounted(() => {
  toSearch();
});
</script>
<template>
  {{ carInfo }}
</template>
