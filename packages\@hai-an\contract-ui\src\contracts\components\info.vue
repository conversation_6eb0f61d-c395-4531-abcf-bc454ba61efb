<script setup lang="ts">
import type { ContractViewModel } from '@hai-an/contract-api';

import { onMounted, reactive } from 'vue';

import {
  BookType,
  ContractType,
  createContractApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: 0, type: Number },
});


const api = createContractApi(options.request, options.path);
const submitForm = reactive<ContractViewModel>({
  applyDate: '',
  bookDate: '',
  bookType: BookType.Expense,
  code: '',
  contractPrice: '',

  contractPriceInfo: '',
  contractTotal: 0,
  contractTotalInfo: '',
  contractType: ContractType.None,
  createBy: '',
  createTime: '',
  endDate: '',
  groupId: 0,
  groupName: '',
  id: 0,
  isDeleted: false,
  lockInfo: '',
  name: '',
  oppositeCode: '',
  oppositeName: '',
  orderCloseDate: '',
  orderNo: '',
  orders: [],
  orgPath: '',
  outline: '',
  payCount: 0,
  payDate: '',
  payTotal: 0,
  planBookDate: '',
  projectCode: '',
  projectName: '',
  promiseDateType: '',
  promiseServiceTerm: '',
  requestTotal: 0,
  serviceContent: '',
  serviceShip: '',
  serviceTerm: '',
  startDate: '',
  startPayDate: '',
  updateBy: '',
  updateTime: '',
  workloadDate: '',
  workloadLockInfo: '',
});
const dayF = (val: string | undefined) => {
  return val ? dayjs(val).format('YYYY-MM-DD') : '';
};

const numF = (val: number, n: number) => {
  const num = val / n;
  return Math.round(num * 100_000) / 100_000;
};

const realod = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};
onMounted(() => {
  realod();
});
</script>
<template>
  <div style="padding: 10px; background-color: #ececec">
    <ADescriptions bordered size="small">
      <ADescriptionsItem label="合同编号">
        {{ submitForm.code }}
      </ADescriptionsItem>
      <ADescriptionsItem label="申请时间">
        {{ dayF(submitForm.applyDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="闭单时间">
        {{ dayF(submitForm.orderCloseDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同名称">
        {{ submitForm.name }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同类型">
        {{ submitForm.contractType ? '付款合同' : '收款合同' }}
      </ADescriptionsItem>
      <ADescriptionsItem label="项目名称">
        {{ submitForm.projectName }}
      </ADescriptionsItem>
      <ADescriptionsItem label="工单编号">
        {{ submitForm.orderNo }}
      </ADescriptionsItem>
      <ADescriptionsItem label="对方名称">
        {{ submitForm.oppositeName }}
      </ADescriptionsItem>
      <ADescriptionsItem label="所属组织机构">
        {{ submitForm.orgPath }}
      </ADescriptionsItem>
      <ADescriptionsItem label="预计签订日期">
        {{ dayF(submitForm.planBookDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同总价(万元)">
        {{ numF(submitForm.contractTotal, 1) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同起算日期">
        {{ dayF(submitForm.startPayDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="结算日期">
        {{ dayF(submitForm.payDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="签订日期">
        {{ dayF(submitForm.bookDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同开始日期">
        {{ dayF(submitForm.startDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同截止日期">
        {{ dayF(submitForm.endDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="开票金额(万元)">
        {{ numF(submitForm.requestTotal, 10000) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="结算次数">
        {{ submitForm.payCount }}
      </ADescriptionsItem>
      <ADescriptionsItem label="结算金额(万元)">
        {{ numF(submitForm.payTotal, 10000) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同单价" :span="3">
        {{ submitForm.contractPrice }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同概况" :span="3">
        {{ submitForm.outline }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同单价说明" :span="3">
        {{ submitForm.contractPriceInfo }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同总价说明" :span="3">
        {{ submitForm.contractTotalInfo }}
      </ADescriptionsItem>
      <ADescriptionsItem label="约定付款时间及方式" :span="3">
        {{ submitForm.promiseDateType }}
      </ADescriptionsItem>
      <ADescriptionsItem label="约定服务期限" :span="3">
        {{ submitForm.promiseServiceTerm }}
      </ADescriptionsItem>
      <ADescriptionsItem label="实际服务期限" :span="3">
        {{ submitForm.serviceTerm }}
      </ADescriptionsItem>
      <ADescriptionsItem label="服务内容" :span="3">
        {{ submitForm.serviceContent }}
      </ADescriptionsItem>
      <ADescriptionsItem label="提供服务的船舶" :span="3">
        {{ submitForm.serviceShip }}
      </ADescriptionsItem>
    </ADescriptions>
  </div>
</template>
