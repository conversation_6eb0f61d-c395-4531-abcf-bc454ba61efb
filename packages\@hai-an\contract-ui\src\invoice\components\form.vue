<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
} from 'ant-design-vue';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({
  applicant: '',
  invoiceNo: '',
  isDeleted: '',
  orderNo: '',
  voucherNo: '',
});
const isDeleteds = [
  { key: 'false', label: '正常', value: 'false' },
  { key: 'true', label: '已删除', value: 'true' },
];

const doSearch = () => {
  emit('onSearch', searchForm);
};

const isDeletedChange = (sel: any) => {
  searchForm.isDeleted = sel ? sel.value : '';
};
</script>
<template>
  <div class="space-align-container">
    <AForm layout="inline" :model="searchForm">
      <AFormItem label="发票号码">
        <AInput v-model:value="searchForm.invoiceNo" />
      </AFormItem>
      <AFormItem label="开票申请人">
        <AInput v-model:value="searchForm.applicant" />
      </AFormItem>
      <AFormItem label="凭证号">
        <AInput v-model:value="searchForm.voucherNo" />
      </AFormItem>
      <AFormItem label="所属工单">
        <AInput v-model:value="searchForm.orderNo" />
      </AFormItem>
      <AFormItem label="删除标识">
        <ASelect
          v-model="searchForm.isDeleted"
          label-in-value
          style="width: 120px"
          :options="isDeleteds"
          :allow-clear="true"
          @change="isDeletedChange"
        />
      </AFormItem>
      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
