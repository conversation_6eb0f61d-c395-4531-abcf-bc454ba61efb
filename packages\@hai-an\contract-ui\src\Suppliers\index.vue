<!-- 供应商管理 -->
<script setup lang="ts">
import { onMounted } from 'vue';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Card as ACard,
  Table as ATable,
} from 'ant-design-vue';

import ContractForm from './components/form.vue';
import { makeColumns } from './tableColumn';
import { showAdd } from './useModal';
import { useSupplierList } from './useSupplierList';

const {
  searchForm,
  pagination,
  dataSource,
  search,
  filter,
  loading,
  pageChange,
} = useSupplierList();

const onSearch = (val: any) => {
  Object.assign(searchForm, val); // 赋值到searchForm
  search();
};

const onAdd = () => {
  showAdd(filter);
};

const columns = makeColumns(filter);

onMounted(() => {
  search();
});
</script>
<template>
  <ACard>
    <ContractForm @on-search="onSearch" />
  </ACard>
  <ACard>
    <div class="space-align-container">
      <AButton type="primary" shape="round" @click="onAdd" size="small">
        <PlusOutlined />新增
      </AButton>
    </div>
    <ATable
      class="ant-table-striped"
      size="middle"
      bordered
      :row-key="(data) => data.id"
      :pagination="pagination"
      :columns="columns"
      :loading="loading"
      @change="pageChange"
      :data-source="dataSource"
    />
  </ACard>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
