<script lang="ts" setup>
import type { WorkActivityViewModel } from '@coder/swf-api';

import { ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { DisposeForm } from '@coder/swf-render';

const router = useRoute();

const id = router.params.id
  ? Number.parseInt(router.params.id as string)
  : undefined;
const title = ref('处理工作');
const description = ref('');
const tabbs = useTabs();
const onLoaded = (wa: WorkActivityViewModel) => {
  title.value = wa.subject;
  tabbs.setTabTitle(wa.subject + wa.number);
};
const onFinish = () => {
  tabbs.closeCurrentTab();
};
</script>
<template>
  <Page :description="description" :title="title">
    <!-- com:{
    meta:{
      title:'处理工作',
      icon:'lucide:code-square',
      hideInMenu:true,
    },
    path:"/works/dispose/:id",
    name:"SwfWorkActivityDispose"
}
-->
    <DisposeForm v-if="id" :id="id" @loaded="onLoaded" @resolve="onFinish" />
  </Page>
</template>
