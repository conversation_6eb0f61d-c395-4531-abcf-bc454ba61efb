<script setup lang="ts" name="MultiPaymentInputForm">
import { computed, onMounted, PropType, ref, watch } from 'vue';

import {
  But<PERSON> as <PERSON>utton,
  Col as ACol,
  DatePicker as ADatePicker,
  Input as AInput,
  InputNumber as AInputNumber,
  Row as ARow,
  Table as ATable,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';

const props = defineProps({
  readonly: {
    default: false,
    type: Boolean,
  },
  value: {
    default: () => [],
    type: Array as PropType<Array<any>>,
  },
});

const emits = defineEmits(['update:value']);

const columns = ref([
  {
    dataIndex: 'id',
    key: 'action',
    title: '操作',
  },
  {
    dataIndex: 'payAmount',
    key: 'payAmount',
    title: '支付金额',
  },
  {
    dataIndex: 'payAmountDate',
    key: 'payAmountDate',
    title: '支付日期',
  },
  {
    dataIndex: 'voucherNo',
    key: 'voucherNo',
    title: '支付凭证',
  },
]);

const readOnly = computed(() => props.readonly);

const data = computed({
  get: () => props.value,
  set: (v) => {
    emits('update:value', v);
  },
});
const onAdd = () => {
  const now = new Date();

  // month
  let monthStr = `${now.getMonth() + 1}`;
  if (monthStr.length < 2) {
    monthStr = `0${monthStr}`;
  }
  // day
  let dateStr = `${now.getDate()}`;
  if (dateStr.length < 2) {
    dateStr = `0${dateStr}`;
  }

  data.value.push({
    payAmount: 0,
    payAmountDate: `${now.getFullYear()}-${monthStr}-${dateStr}`,
    voucherNo: '',
  });
};
const onDelete = (index: number) => {
  data.value.splice(index, 1);
};

const formatData = (data: string) => {
  if (!data) return '';
  return data?.split(' ')[0];
};

const initColumns = () => {
  if (props.readonly) {
    columns.value.shift();
  } else if (columns.value[0].key !== 'action') {
    columns.value.unshift({
      dataIndex: 'id',
      key: 'action',
      title: '操作',
    });
  }

  data.value = [...data.value];
};
watch(
  () => props.readonly,
  () => {
    initColumns();
  },
);

onMounted(() => {
  initColumns();
});
</script>

<template>
  <ARow>
    <ACol :span="24">
      <div style="margin: 15px">
        <AButton @click="onAdd" type="primary">添加</AButton>
      </div>
    </ACol>
  </ARow>
  <ARow>
    <ACol :span="24">
      <ATable row-key="id" :data-source="data" :columns="columns">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <AButton @click="onDelete(record)" type="primary" danger>
              删除
            </AButton>
          </template>
          <template v-if="column.key === 'payAmount'">
            <AInputNumber
              v-model:value="record.payAmount"
              :precision="2"
              v-if="!readOnly"
            />
            <template v-if="readOnly">{{ record.payAmount }}</template>
          </template>

          <template v-if="column.key === 'payAmountDate'">
            <ADatePicker
              v-model:value="record.payAmountDate"
              v-if="!readOnly"
              :locale="locale"
              value-format="YYYY-MM-DD"
              :allow-clear="false"
            />
            <template v-if="readOnly">
              {{ formatData(record.payAmountDate) }}
            </template>
          </template>

          <template v-if="column.key === 'voucherNo'">
            <AInput v-model:value="record.voucherNo" v-if="!readOnly" />
            <template v-if="readOnly">{{ record.voucherNo }}</template>
          </template>
        </template>
      </ATable>
    </ACol>
  </ARow>
</template>
