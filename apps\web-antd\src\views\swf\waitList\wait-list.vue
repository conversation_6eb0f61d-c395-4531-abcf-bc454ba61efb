<script setup lang="ts">
import { onActivated, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { MyWaitList } from '@coder/swf-ui';
import { OrgFilter } from '@hai-an/oa-ui';
import { Col as ACol, FormItem as AFormItem } from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

const tags = ref([]);
const swfRef = ref();
const status = ref(Number(route.query.status) || 1);
const workProcessName = ref(route.params.workName as string);

const goDetail = (id: any) => {
  router.push({
    name: 'SwfProcessInstanceDetail',
    params: { id },
  });
};
const onDeposit = (workActivityId: any) => {
  router.push({
    path: `/works/dispose/${workActivityId}`,
  });
};

onActivated(() => {
  if (swfRef.value) swfRef.value.reload();
});
</script>

<template>
  <Page description="我的待办工单" title="">
    <!-- com:{
      meta:{
        title:'我的待办工单',
        icon: 'ant-design:align-right-outlined'
      },
      path:"/wait-list",
      name:"SwfMyWaitList"
    }
    -->
    <MyWaitList
      ref="swfRef"
      :tags="tags"
      :status="status"
      :work-process-name="workProcessName"
      @detail="goDetail"
      @dispose="onDeposit"
    >
      <template #filter="searchForm">
        <ACol :span="6">
          <AFormItem label="部门/分公司">
            <OrgFilter
              v-model:tags="searchForm.searchModel.tags"
              :tag-index="0"
              placeholder="请选择组织架构"
            />
          </AFormItem>
        </ACol>
      </template>
    </MyWaitList>
  </Page>
</template>

<style scoped></style>
