<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
} from 'ant-design-vue';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({
  code: '',
  contractType: '',
  isDeleted: false,
  name: '',
  oppositeName: '',
  orgPath: '',
  projectName: '',
  serviceShip: '',
  projectArchiveCode: '',
  projectArchiveName: '',
});

const contractTypes = [
  { key: '0', label: '收款合同', value: '0' },
  { key: '1', label: '付款合同', value: '1' },
];

defineExpose({ searchForm });

const doSearch = () => {
  emit('onSearch', searchForm);
};

const contractTypeChange = (sel: any) => {
  searchForm.contractType = sel ? sel.value : '';
};
</script>
<template>
  <div class="space-align-container">
    <AForm layout="inline" :model="searchForm">
      <AFormItem label="合同编号">
        <AInput v-model:value="searchForm.code" />
      </AFormItem>
      <AFormItem label="合同名称">
        <AInput v-model:value="searchForm.name" />
      </AFormItem>
      <AFormItem label="项目编码">
        <AInput v-model:value="searchForm.projectArchiveCode" />
      </AFormItem>
      <AFormItem label="项目名称">
        <AInput v-model:value="searchForm.projectArchiveName" />
      </AFormItem>

      <AFormItem label="合同项目名称">
        <AInput v-model:value="searchForm.projectName" />
      </AFormItem>
      <AFormItem label="合同类型">
        <ASelect
          v-model="searchForm.contractType"
          label-in-value
          style="width: 120px"
          :options="contractTypes"
          :allow-clear="true"
          @change="contractTypeChange"
        />
      </AFormItem>
      <AFormItem label="对方名称">
        <AInput v-model:value="searchForm.oppositeName" />
      </AFormItem>
      <AFormItem label="所属组织机构">
        <AInput v-model:value="searchForm.orgPath" />
      </AFormItem>

      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
