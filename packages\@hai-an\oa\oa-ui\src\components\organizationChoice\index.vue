<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import { OrgSelector } from '@coder/system-ui';

const props = defineProps<{
  placeholder: string;
  tagIndex: number;
  tags: Array<string>;
}>();

const _tags = computed(() => props.tags).value;

const isNothing = (val: any) => {
  return !val;
};
const _tagIndex = computed(() => props.tagIndex).value;
const ids = ref<string>('');
watch(ids, (newVal) => {
  newVal && onSelect(newVal);
});

const onSelect = (orgs: any) => {
  if (isNothing(orgs)) {
    if (_tags.length > 0) {
      _tags.splice(_tagIndex, 1);
    }
  } else {
    // const orgPathAry = orgs.map((item:any) => item.name);
    // const path = orgPathAry.join('/');

    const orgIndex = _tags.findIndex((_) => _.startsWith('org:'));

    if (orgIndex === -1) {
      _tags.push(`org:${orgs}`);
    } else {
      _tags[orgIndex] = `org:${orgs}`;
    }
  }
};
</script>

<template>
  <OrgSelector v-model:value="ids" :placeholder="placeholder" />
</template>
