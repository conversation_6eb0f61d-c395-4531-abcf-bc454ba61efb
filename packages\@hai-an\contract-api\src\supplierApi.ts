import type { RequestClient } from '@vben/request';

import type { ResponseResult } from './types/commont';
import type { SupplierSearch, SupplierViewModel } from './types/supplier';

export interface SupplierSubmit {
  address: string;
  bankName: string;
  code: string;
  id: number;
  name: string;
  num: string;
  phone: string;
  userName: string;
}

export const createSupplierApi = (request: RequestClient, path: string) => {
  return {
    /**
     * Get supplier count
     */
    count(params: { code?: string; name?: string }): Promise<number> {
      return request.get(`${path}/Supplier/count`, { params });
    },

    /**
     * Get supplier by ID
     */
    getById(id: number): Promise<SupplierViewModel> {
      return request.get(`${path}/Supplier/${id}`);
    },

    save(submit: SupplierSubmit): Promise<ResponseResult> {
      return request.post(`${path}/Supplier/save`, submit);
    },

    /**
     * Get supplier list
     */
    list(params: SupplierSearch): Promise<SupplierViewModel[]> {
      return request.get(`${path}/Supplier/list`, { params });
    },
    delete(id: number | string) {
      return request.delete(`${path}/Supplier/${id}`);
    },
  };
};
