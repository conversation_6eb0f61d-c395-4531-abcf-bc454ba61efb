<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { updatePreferences } from '@vben/preferences';

import { SwfDesigner } from '@coder/swf-designer';

defineOptions({
  name: 'SwfDesigner',
});
const router = useRoute();

// workprocess's id

const id = router.params.id
  ? Number.parseInt(router.params.id as string)
  : undefined;

const DesignerRef = ref();

onMounted(() => {
  updatePreferences({
    sidebar: {
      hidden: true,
    },
  });

  const processInstanceId = router.query.processInstanceId;
  if (processInstanceId) {
    DesignerRef.value.attachInstance(Number(processInstanceId));
  }
});
</script>
<template>
  <div class="bg-background text-foreground" style="height: 100%">
    <SwfDesigner :id="id" style="height: 100%" ref="DesignerRef">
      <!-- com:{
       meta:{
        title:'设计器',
        icon:'lucide:code-square',
        hideInMenu:true,
       },
  path:"designer/:id?",
  name:"SwfEditor",
}
-->
    </SwfDesigner>
  </div>
</template>
