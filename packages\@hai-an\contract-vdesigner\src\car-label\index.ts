import type { WidgetDefinition } from '@coder/vdesigner-core';

import { FileOutlined as icon } from '@ant-design/icons-vue';
import { useWidgetRegistry, WidgetGroup } from '@coder/vdesigner-core';

import { CarLabelEditor } from './_editor';
import { CarLabelOption } from './_options';
// import intelliSense from './intelliSense.d.ts?raw';
import Widget from './widget.vue';

export * from './_options';
/**
 * 注册 contract-link-button 组件
 * @param extendOptions 扩展属性，默认就是空。而且基础属性按照ant-design-vue 设计
 * @param componentInput input widget实现的组件
 */
export const registerLabelCar = (): WidgetDefinition => {
  const { registerWithOptions } = useWidgetRegistry();
  return registerWithOptions(
    {
      group: WidgetGroup.Customer, // 是一个自定义分组
      groupLabel: '合同', // 自定义分组组名
      icon, // 呈现的icon
      intelliSense: '', // 代码提示，可以为空，如果要，请用 d.ts定义就可以
      label: '车辆显示', // 中文名字
      mode: 'Item', // 这是一个组件，不是容器。
      optionEditors: CarLabelEditor(), // 编辑器
      order: 2, // 排序大小，越大，越后
      showOnPanel: true, // 是否组件列表中显示。
      type: 'select-car', // 标记名字，必须全局唯一。
    },
    () => new CarLabelOption(),
    undefined, // 扩展属性，如果是全新实现的，无需要例会这个属性
    Widget,
    'default', // 实现方式，为default， 也有可能是 vant/ antd。如果有多个组件应用在不同的UI框架上，可以用这个来区分
  );
};
