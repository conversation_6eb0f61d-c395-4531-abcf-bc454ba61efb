<script lang="ts" setup>
import { computed, ref } from 'vue';

import { getMessageTypeApi, type MessageTypeSearcher } from '@coder/notify-api';
import { Select } from 'ant-design-vue';

const props = defineProps<{ value?: string }>();
const emits = defineEmits<{ (e: 'update:value', val: string): void }>();
const data = ref<any[]>([]);
const value = computed({
  get: () => props.value,
  set: (v: string) => emits('update:value', v),
});
const handleSearch = (val?: string) => {
  getMessageTypeApi()
    .list({
      name: val,
    } as MessageTypeSearcher)
    .then((resp) => {
      data.value.splice(0);
      resp.forEach((r) => {
        data.value.push({
          label: r.name,
          value: r.name,
        });
      });
    });
};
const handleChange = (val: string) => {
  value.value = val;
  handleSearch();
};
</script>
<template>
  <Select
    v-model:value="value"
    :default-active-first-option="false"
    :filter-option="false"
    :not-found-content="null"
    :options="data"
    :show-arrow="false"
    placeholder="input search text"
    show-search
    style="width: 200px"
    @change="(v) => handleChange(v as string)"
    @search="() => handleSearch()"
  />
</template>
