<script lang="ts" setup>
import type { MessageTypeSearcher } from '@coder/notify-api';

import { computed, ref } from 'vue';

import { getMessageTypeApi } from '@coder/notify-api';
import { Select } from 'ant-design-vue';

const props = defineProps<{ value?: string }>();
const emits = defineEmits<{ (e: 'update:value', val: string): void }>();
const data = ref<any[]>([]);
const value = computed({
  get: () => props.value,
  set: (v: string | undefined) => emits('update:value', v || ''),
});
const handleSearch = (val?: string) => {
  getMessageTypeApi()
    .list({
      name: val,
    } as MessageTypeSearcher)
    .then((resp) => {
      data.value.splice(0);
      resp.forEach((r) => {
        data.value.push({
          label: r.name,
          value: r.name,
        });
      });
    });
};
const handleChange = (val: any) => {
  const stringVal = val ? String(val) : '';
  value.value = stringVal;
  handleSearch();
};
</script>
<template>
  <Select
    :value="value"
    :default-active-first-option="false"
    :filter-option="false"
    :not-found-content="null"
    :options="data"
    :show-arrow="false"
    allow-clear
    placeholder="input search text"
    show-search
    style="width: 200px"
    @change="handleChange"
    @search="handleSearch"
  />
</template>
