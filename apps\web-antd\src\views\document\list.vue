<script setup lang="ts">
import { useRouter } from 'vue-router';

import { Page as PageWrapper } from '@vben/common-ui';

import { fileManagementPanel } from '@hai-an/document-ui';

defineOptions({
  name: 'DocumentList',
});

const router = useRouter();

const handlePreviewFile = (url: string) => {
  router.push({
    name: 'PreviewDocument',
    query: {
      url,
    },
  });
};
</script>

<template>
  <PageWrapper>
    <!-- com:{
     meta:{
      title:'文档管理',
      icon:'lucide:square-menu'
    },
     name:'DocumentList',
     path:'list-document'

    } -->
    <fileManagementPanel @preview-file="handlePreviewFile" />
  </PageWrapper>
</template>
