<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
} from 'ant-design-vue';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({
  address: '',
  bankName: '',
  code: '',
  isDeleted: '',
  name: '',
  phone: '',
  userName: '',
});
const isDeleteds = [
  { key: 'false', label: '正常', value: 'false' },
  { key: 'true', label: '已删除', value: 'true' },
];

const doSearch = () => {
  emit('onSearch', searchForm);
};

const isDeletedChange = (sel: any) => {
  searchForm.isDeleted = sel ? sel.value : '';
};
</script>
<template>
  <div class="space-align-container">
    <AForm layout="inline" :model="searchForm">
      <AFormItem label="公司全称">
        <AInput v-model:value="searchForm.name" />
      </AFormItem>
      <AFormItem label="纳税人识别号">
        <AInput v-model:value="searchForm.code" />
      </AFormItem>
      <AFormItem label="开户银行名称">
        <AInput v-model:value="searchForm.bankName" />
      </AFormItem>
      <AFormItem label="联系人">
        <AInput v-model:value="searchForm.userName" />
      </AFormItem>
      <AFormItem label="电话">
        <AInput v-model:value="searchForm.phone" />
      </AFormItem>
      <AFormItem label="公司地址">
        <AInput v-model:value="searchForm.address" />
      </AFormItem>
      <AFormItem label="删除标识">
        <ASelect
          v-model="searchForm.isDeleted"
          label-in-value
          style="width: 120px"
          :options="isDeleteds"
          :allow-clear="true"
          @change="isDeletedChange"
        />
      </AFormItem>
      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
