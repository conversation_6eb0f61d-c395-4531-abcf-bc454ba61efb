<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  But<PERSON> as AButton,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({
  changeComment: '',
  changeTime: '',
  changeUserName: '',
  contractNumber: '',
  contractNumberName: '',
});

const doSearch = () => {
  const form = { ...searchForm };
  if (form.changeTime) {
    form.changeTime = dayjs(form.changeTime).format('YYYY-MM-DD');
  }
  emit('onSearch', form);
};
</script>
<template>
  <div class="space-align-container">
    <AForm layout="inline" :model="searchForm">
      <AFormItem label="合同编码">
        <AInput v-model:value="searchForm.contractNumber" />
      </AFormItem>
      <AFormItem label="合同名称">
        <AInput v-model:value="searchForm.contractNumberName" />
      </AFormItem>
      <AFormItem label="处理账号">
        <AInput v-model:value="searchForm.changeUserName" />
      </AFormItem>
      <AFormItem label="操作内容">
        <AInput v-model:value="searchForm.changeComment" />
      </AFormItem>
      <AFormItem label="处理时间">
        <ADatePicker v-model:value="searchForm.changeTime" :locale="locale" />
      </AFormItem>

      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
