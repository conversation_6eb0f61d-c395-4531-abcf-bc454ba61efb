<script setup lang="ts">
import type { WorkTaskSubmit } from '@coder/swf-api';

import { computed, ref, watch } from 'vue';

import { CodeEditor } from '@coder/code-editor';
import { TabPane, Tabs } from 'ant-design-vue';

import {
  IntellisensType,
  useSwfInteliSense,
} from '../../hooks/useInsteliSense';
import Assigner from '../assigners/index.vue';
import Basic from './basic.vue';

const props = defineProps<{
  activityKey?: string;
  storeId: string;
  workTask: WorkTaskSubmit;
}>();
const activeKey = ref('basic');
const workTask = computed(() => props.workTask);

const SetIntellisence = (intell: IntellisensType) => {
  useSwfInteliSense().setIntell(intell);
};

const workActivityCompleteScript = computed({
  get: () => workTask.value.workActivityCompleteScript?.script ?? '',
  set: (v) => {
    if (!workTask.value) return;
    if (!workTask.value.workActivityCompleteScript) {
      workTask.value.workActivityCompleteScript = {
        script: v,
      };
      return;
    }

    workTask.value.workActivityCompleteScript.script = '';
  },
});

const workTaskStart = computed({
  get: () => {
    return workTask.value?.workTaskStartScript?.script ?? '';
  },
  set: (v) => {
    if (!workTask.value) return;
    if (!workTask.value.workTaskStartScript) {
      workTask.value.workTaskStartScript = {
        script: v,
      };
      return;
    }

    workTask.value.workTaskStartScript.script = '';
  },
});

const workTaskComplete = computed({
  get: () => workTask.value?.workTaskCompleteScript?.script ?? '',
  set: (v) => {
    if (!workTask.value) return;
    if (!workTask.value.workTaskCompleteScript) {
      workTask.value.workTaskCompleteScript = {
        script: v,
      };
      return;
    }

    workTask.value.workTaskCompleteScript.script = '';
  },
});

watch(
  () => props.activityKey,
  (v) => {
    if (v) {
      activeKey.value = v;
    }
  },
  {
    immediate: true,
  },
);
defineExpose({
  set(v: string) {
    activeKey.value = v;
  },
});
</script>

<template>
  <Tabs v-model:active-key="activeKey" tab-position="left" type="card">
    <TabPane key="basic" tab="基本">
      <Basic :store-id="props.storeId" :work-task="props.workTask" />
    </TabPane>

    <TabPane key="assigner" tab="派发">
      <Assigner :store-id="props.storeId" :work-task="props.workTask" />
    </TabPane>
    <TabPane key="启动" tab="任务开始">
      <h3>任务开始</h3>
      <CodeEditor
        v-if="workTask"
        v-model:code="workTaskStart"
        type="javascript"
        @focus="() => SetIntellisence(IntellisensType.WorkTaskStart)"
      />
    </TabPane>
    <TabPane key="活动结束" tab="活动结束">
      <h3>活动结束</h3>
      <CodeEditor
        v-if="workTask"
        v-model:code="workActivityCompleteScript"
        type="javascript"
        @focus="() => SetIntellisence(IntellisensType.WorkActivityComplete)"
      />
    </TabPane>

    <TabPane key="结束" tab="任务结束">
      <h3>任务结束</h3>
      <CodeEditor
        v-if="workTask"
        v-model:code="workTaskComplete"
        type="javascript"
        @focus="() => SetIntellisence(IntellisensType.WorkTaskOnComplete)"
      />
    </TabPane>
  </Tabs>
</template>
