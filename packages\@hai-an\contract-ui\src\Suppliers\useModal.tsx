import type { SupplierViewModel } from '@hai-an/contract-api/src/types/supplier';

import { ref } from 'vue';

import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  createSupplierApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import { Button, message, Modal, Space } from 'ant-design-vue';

import ContractEdit from './components/edit.vue';
import ContractInfo from './components/info.vue';

const api = createSupplierApi(options.request, options.path);

export const showDetail = (supplier: SupplierViewModel) => {
  Modal.info({
    title: '供应商详细信息',
    content: () => <ContractInfo id={supplier.id} />,
    width: 1200,
    maskClosable: false,
    okText: '确定',
  });
};

export const showAdd = (reload: { (): void }) => {
  const editorRef = ref();
  const onSave = () => {
    editorRef.value.save().then(() => {
      modal.destroy();
      reload();
    });
  };
  const onReset = () => {
    editorRef.value.reset();
  };
  const modal = Modal.info({
    title: '新增供应商信息',
    content: () => <ContractEdit id={0} ref={editorRef} />,
    width: 1000,
    maskClosable: false,
    footer: () => {
      return (
        <Space>
          <Button onClick={() => modal.destroy()}>取消</Button>
          <Button onClick={() => onReset()}>重置</Button>
          <Button onClick={() => onSave()} type="primary">
            保存
          </Button>
        </Space>
      );
    },
  });
};

export const showEditor = (
  supplier: SupplierViewModel,
  reload: { (): void },
) => {
  const editorRef = ref();
  const onSave = () => {
    editorRef.value.save().then(() => {
      modal.destroy();
      reload();
    });
  };
  const onReset = () => {
    editorRef.value.reset();
  };
  const modal = Modal.info({
    title: '编辑供应商信息',
    content: () => <ContractEdit id={supplier.id} ref={editorRef} />,
    width: 1000,
    maskClosable: false,
    footer: () => {
      return (
        <Space>
          <Button onClick={() => modal.destroy()}>取消</Button>
          <Button onClick={() => onReset()}>重置</Button>
          <Button onClick={() => onSave()} type="primary">
            保存
          </Button>
        </Space>
      );
    },
  });
};

export const deleteSupplier = (
  val: SupplierViewModel,
  reload: { (): void },
) => {
  Modal.confirm({
    cancelText: '取消',
    content: () => {
      return (
        <div style="color:red;">是否确定删除选定信息?删除后将无法恢复！</div>
      );
    },
    icon: () => <ExclamationCircleOutlined />,
    okText: '确认删除',
    onCancel() {
      message.info('取消删除！');
    },
    async onOk() {
      try {
        // 确保 api 初始化完成
        await Promise.resolve();
        const res = await api.delete(val.id);
        if (res.success) {
          message.success(res.message);
          reload();
        } else {
          message.error(res.message);
        }
      } catch (error) {
        message.error(`删除失败：${(error as Error).message}`);
      }
    },
    title: () => '删除确认',
  });
};
