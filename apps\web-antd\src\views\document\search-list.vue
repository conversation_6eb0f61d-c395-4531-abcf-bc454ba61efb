<script setup lang="ts">
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { FileSearch } from '@hai-an/document-ui';

defineOptions({
  name: 'DocumentSearch',
});
const router = useRouter();

const handlePreviewFile = (url: string) => {
  router.push({
    name: 'PreviewDocument',
    query: {
      url,
    },
  });
};
</script>

<template>
  <Page title="文档查询" description="">
    <!-- com:{
     meta:{
      title:'文档查询',
      icon:'lucide:square-menu'
    },
     name:'DocumentSearch',
     path:'document-search'
    } -->
    <FileSearch @preview-file="handlePreviewFile" />
  </Page>
</template>

<style scoped></style>
