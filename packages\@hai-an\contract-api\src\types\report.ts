import type { ContractType } from '../types';

export interface ReportContractSearcher {
  code: string;
  contractType: ContractType | null;
  createBy: string;
  departmentType: string;
  isAllData: boolean;
  isInRole: boolean;
  isOrgData: boolean;
  oppositeName: string;
  orgName: string;
  orgPaths: string[];
  projectName: string;
  reportDateEnd: null | string;
  reportDateStar: null | string;
}
