<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
} from 'ant-design-vue';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({
  contractCode: '',
  contractName: '',
  projectCode: '',
  projectName: '',
});
const doSearch = () => {
  emit('onSearch', searchForm);
};
</script>
<template>
  <div class="space-align-container">
    <AForm layout="inline" :model="searchForm">
      <AFormItem label="合同编号">
        <AInput v-model:value="searchForm.contractCode" />
      </AFormItem>
      <AFormItem label="合同名称">
        <AInput v-model:value="searchForm.contractName" />
      </AFormItem>
      <AFormItem label="项目编号">
        <AInput v-model:value="searchForm.projectCode" />
      </AFormItem>
      <AFormItem label="项目名称">
        <AInput v-model:value="searchForm.projectName" />
      </AFormItem>
      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
