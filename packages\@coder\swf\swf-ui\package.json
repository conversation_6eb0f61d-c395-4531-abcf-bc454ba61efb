{"name": "@coder/swf-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "main": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/clip-button": "workspace:*", "@coder/common-api": "workspace:*", "@coder/swf-api": "workspace:*", "@coder/swf-designer": "workspace:*", "@coder/swf-render": "workspace:*", "@coder/system-api": "workspace:*", "@coder/system-ui": "workspace:*", "@coder/vdesigner-form-render": "workspace:*", "@hai-an/oa-api": "workspace:*", "@morev/vue-transitions": "catalog:coder", "@vben-core/icons": "workspace:*", "@vben/common-ui": "workspace:^", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/request": "workspace:*", "@vben/utils": "workspace:^", "ant-design-vue": "catalog:", "dayjs": "catalog:", "vue": "catalog:", "vue-clipboard3": "catalog:coder", "vue-draggable-plus": "catalog:coder"}, "devDependencies": {"@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}