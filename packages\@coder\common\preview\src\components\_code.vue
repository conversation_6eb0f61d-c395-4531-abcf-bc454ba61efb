<script setup lang="ts">
import { onUnmounted, ref } from 'vue';

interface Props {
  blob: Blob | undefined;
  fileName?: string;
  language?: string;
  src: string;
}

const props = withDefaults(defineProps<Props>(), {
  fileName: 'code',
  language: 'javascript',
});

const visible = ref<boolean>(false);
const content = ref<string>('');
const loading = ref<boolean>(false);

const fetchContent = async (): Promise<void> => {
  loading.value = true;
  try {
    const response = await fetch(props.src);
    content.value = await response.text();
  } catch (error) {
    content.value = '// 无法加载文件内容';
    console.error('Failed to fetch content:', error);
  } finally {
    loading.value = false;
  }
};

const show = async (): Promise<void> => {
  visible.value = true;
  if (!content.value) {
    await fetchContent();
  }
};

const hide = (): void => {
  visible.value = false;
};

const handleKeydown = (e: KeyboardEvent): void => {
  if (e.key === 'Escape' && visible.value) {
    hide();
  }
};

const handleBackdropClick = (e: MouseEvent): void => {
  if (e.target === e.currentTarget) {
    hide();
  }
};

const copyCode = async (): Promise<void> => {
  try {
    await navigator.clipboard.writeText(content.value);
    // 可以添加提示信息
  } catch (error) {
    console.error('Failed to copy code:', error);
  }
};

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

defineExpose({
  show,
  hide,
});
</script>

<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="code-modal-overlay"
      @click="handleBackdropClick"
      @keydown="handleKeydown"
    >
      <div class="code-modal-container">
        <div class="modal-header">
          <h3 class="modal-title">{{ props.fileName }}</h3>
          <div class="header-actions">
            <div class="copy-btn" @click="copyCode" title="复制代码">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                <path
                  d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                />
              </svg>
            </div>
            <div class="modal-close-btn" @click="hide" title="关闭">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </div>
          </div>
        </div>

        <div class="code-content">
          <div v-if="loading" class="loading">加载中...</div>
          <pre v-else class="code-block"><code>{{ content }}</code></pre>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style scoped>
.code-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 10001;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(0 0 0 / 80%);
  backdrop-filter: blur(4px);
}

.code-modal-container {
  display: flex;
  flex-direction: column;
  width: 90vw;
  max-width: 1000px;
  height: 80vh;
  overflow: hidden;
  color: #d4d4d4;
  background-color: #1e1e1e;
  border-radius: 8px;
  box-shadow: 0 20px 60px rgb(0 0 0 / 30%);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #2d2d30;
  border-bottom: 1px solid #333;
}

.modal-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #d4d4d4;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.copy-btn,
.modal-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: #d4d4d4;
  cursor: pointer;
  background-color: transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.copy-btn:hover,
.modal-close-btn:hover {
  background-color: rgb(255 255 255 / 10%);
}

.code-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
}

.code-block {
  margin: 0;
  font-family: 'Fira Code', Monaco, Menlo, 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #d4d4d4;
}

.code-block code {
  color: inherit;
  background: transparent;
}
</style>
