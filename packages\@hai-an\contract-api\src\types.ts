export enum ContractType {
  None = 0,
  Purchase = 1,
  Sale = 2,
}
export enum OrderType {
  签订,
  付款,
  收款,
}
export enum BookType {
  Expense = 2,
  Income = 1,
  None = 0,
}
export interface OrdersViewModel {
  amount: number;
  completeWorkload: number;
  orderNo: string;
  orderType: OrderType;
  unitPrice: number;
}
export interface ContractListItem {
  applyDate: string;
  bookDate: string;
  bookType: BookType;
  code: string;
  contractPrice: string;
  contractPriceInfo: string;
  contractTotal: number;
  contractTotalInfo: string;
  contractType: ContractType;
  createBy: string;
  createTime: string;
  endDate: string;
  groupId: number;
  groupName: string;
  id: number;
  isDeleted: boolean;
  lockInfo: string;
  name: string;
  oppositeCode: string;
  oppositeName: string;
  orderCloseDate: string;
  orderNo: string;
  orders: OrdersViewModel[];
  orgPath: string;
  outline: string;
  payCount: number;
  payDate: string;
  payTotal: number;
  planBookDate: string;
  projectArchiveCode: string;
  projectArchiveId: number;
  projectArchiveName: string;
  projectCode: string;
  projectName: string;
  promiseDateType: string;
  promiseServiceTerm: string;
  requestTotal: number;
  serviceShip: string;
  serviceTerm: string;
  startDate: string;
  startPayDate: string;
  workloadDate: string;
  workloadLockInfo: string;
}
export interface ContractViewModel extends ContractListItem {
  serviceContent: string;
  updateBy: string;
  updateTime: string;
}
export interface ContractSearch {
  applyDate?: Date;
  bookDate?: Date;
  bookType?: BookType;
  code?: string;
  contractType?: ContractType;
  createBy?: string;
  id?: number;
  isAllData?: boolean;
  isDeleted?: boolean;
  isLock?: boolean;
  isMaster?: boolean;
  isOrgData?: boolean;
  isWorkloadLock?: boolean;
  linkCode?: string;
  name?: string;
  oppositeCode?: string;
  oppositeName?: string;
  orderCloseDate?: Date;
  orgPath?: string;
  orgPaths?: string[];
  page?: number;
  pageSize?: number;
  planBookDate?: Date;
  projectArchiveCode?: string;
  projectArchiveName?: string;
  projectName?: string;
  serviceShip?: string;
}

export interface ContractSubmit {
  applyDate: string;
  bookDate: string;
  bookType: BookType;
  code: string;
  contractPrice: string;
  contractPriceInfo: string;
  contractTotal: number;
  contractTotalInfo: string;
  contractType: ContractType;
  endDate: string;
  id: number;
  lockInfo: string;
  name: string;
  oppositeCode: string;
  oppositeName: string;
  orderCloseDate: string;
  orderNo: string;
  orgPath: string;
  outline: string;
  payCount: number;
  payDate: string;
  payTotal: number;
  planBookDate: string;
  projectArchiveCode: string;
  projectArchiveId: number;
  projectArchiveName: string;
  projectCode: string;
  projectName: string;
  promiseDateType: string;
  promiseServiceTerm: string;
  requestTotal: number;
  serviceContent: string;
  serviceShip: string;
  serviceTerm: string;
  startDate: string;
  startPayDate: string;
  taxRate: number;
  workloadDate: string;
  workloadLockInfo: string;
}

export interface ContractResponse {
  code: number;
  data?: any;
  message: string;
  success: boolean;
}
