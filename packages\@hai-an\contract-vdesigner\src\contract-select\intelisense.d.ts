type ContractListItem = {
  applyDate: string;
  bookDate: string;
  bookType: BookType;
  code: string;
  contractPrice: string;
  contractPriceInfo: string;
  contractTotal: number;
  contractTotalInfo: string;
  contractType: ContractType;
  createBy: string;
  createTime: string;
  endDate: string;
  groupId: number;
  groupName: string;
  id: number;
  isDeleted: boolean;
  lockInfo: string;
  name: string;
  oppositeCode: string;
  oppositeName: string;
  orderCloseDate: string;
  orderNo: string;
  orders: OrdersViewModel[];
  orgPath: string;
  outline: string;
  payCount: number;
  payDate: string;
  payTotal: number;
  planBookDate: string;
  projectCode: string;
  projectName: string;
  promiseDateType: string;
  promiseServiceTerm: string;
  requestTotal: number;
  serviceShip: string;
  serviceTerm: string;
  startDate: string;
  startPayDate: string;
  workloadDate: string;
  workloadLockInfo: string;
};
declare const data: { contract: ContractListItem };
