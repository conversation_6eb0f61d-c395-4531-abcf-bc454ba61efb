import type { IHaianContractOption } from '@hai-an/contract-api';

import { useAccessStore } from '@vben/stores';

import car from '@hai-an/contract-api';

import { requestClient } from '#/api/request';

const option = {
  path: '/contract',
  request: requestClient,
  previewHost: 'http://localhost:801/api/filePreview',
  getToken() {
    const store = useAccessStore();
    return `Bearer ${store.accessToken}`;
  },
} as IHaianContractOption;

const install = (app: any) => {
  app.use(car, option);
};

export default install;
