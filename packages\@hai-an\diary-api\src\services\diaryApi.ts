import type { RequestClient } from '@vben/request';

import type {
  DiaryFormData,
  DiaryItems,
  DiarySearch,
} from '../../../contract-api/src/types/diary';

export const createDiaryApi = (
  request: RequestClient,
  host: string | undefined,
) => {
  const path = `${host}/oa/diary`;
  const filePath = `${host}/oa/diaryfile`;
  return {
    list(
      params: DiarySearch,
    ): Promise<{ code: number; data: any; message: string }> {
      return request.get(`${path}/listWithFiles`, {
        params,
        responseReturn: 'body',
      });
    },
    add(
      formData: DiaryFormData,
    ): Promise<{ code: number; data: any; message: string }> {
      return request.post(`${path}/save`, formData, { responseReturn: 'body' });
    },
    update(
      formData: DiaryFormData,
    ): Promise<{ code: number; data: any; message: string }> {
      return request.put(`${path}/update`, formData, {
        responseReturn: 'body',
      });
    },
    del(id: string) {
      return request.delete(`${path}/delete`, {
        params: { id },
        responseReturn: 'body',
      });
    },
    getBayId(id: string): Promise<DiaryItems[]> {
      return request.get(`${path}/getInfo`, {
        params: { id },
        responseReturn: 'body',
      });
    },
    exportDiary(searcher: DiarySearch) {
      return request.get(`${path}/export`, {
        params: searcher,
        responseType: 'blob',
      });
    },
    /**
     * 下载文件
     * @param fileId
     * @returns
     */
    downloadFile(fileId: string) {
      return request.get(`${filePath}/download`, {
        params: { id: fileId },
        responseType: 'blob',
      });
    },
    /**
     * 上传文件
     * @param file
     * @param name
     * @param diaryId
     * @param refId
     * @param comment
     * @returns
     */
    uploadFile(
      file: File,
      name: string,
      diaryId: string,
      refId?: string,
      comment?: string,
    ) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', name);
      formData.append('diaryId', diaryId);
      if (refId) {
        formData.append('refId', refId);
      }
      if (comment) {
        formData.append('comment', comment);
      }
      return request.post(`${filePath}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseReturn: 'body', // rwa
      });
    },
    /**
     * 删除文件
     * @param fileId
     * @returns
     */
    deleteFile(fileId: string) {
      return request.delete(`${filePath}/delete`, {
        params: { id: fileId },
        responseReturn: 'body',
      });
    },
    /**
     * 预览文件路径
     * @param fileId 文件ID
     * @returns 文件二进制数据
     */
    getPreviewFileUrl(fileId: string): string {
      let urlTemp = `${filePath}/previewByFileId/${fileId}`;
      if (!urlTemp.startsWith('http')) {
        urlTemp = `api${urlTemp}`;
      }
      return urlTemp;
    },
    getTicket(id: string): Promise<string> {
      return request.get(`${filePath}/get-preview-ticket/${id}`, {
        responseReturn: 'body',
      });
    },
  };
};
