import type { RequestClient } from '@vben/request';

import type { MessageViewModel } from '../types';

import qs from 'qs';

import { ReadStatus } from '../types';

export interface MessageUserSearcher {
  content?: string;
  page: number;
  pageSize: number;
  status?: ReadStatus;
  type?: string[];
  user?: string;
}
const aspnetCoreParamerSerial = (params: any) => {
  return qs.stringify(params, {
    allowDots: true,
    arrayFormat: 'repeat',
  });
};

export const createNotifyMessageApi = (
  request: RequestClient,
  notifyPath: string,
) => {
  const path = `${notifyPath}/NotifyMessage`;
  return {
    count(searcher: MessageUserSearcher): Promise<number> {
      let query = `count?type=${searcher.type}&page=${searcher.page}&pageSize=${searcher.pageSize}`;
      if (searcher.status || searcher.status === 0) {
        query += `&status=${searcher.status}`;
      }
      if (searcher.content) {
        query += `&content=${searcher.content}`;
      }
      return request.get(`${path}/${query}`);
    },
    /**
     *
     * @param searcher
     * @returns
     */
    get(id: number) {
      return request.get<MessageViewModel>(`${path}/message/${id}`);
    },
    markAllRead() {
      return request.put<{ message: string; success: boolean }>(
        `${path}/mark-all-read`,
      );
    },

    /**
     *
     * @param searcher
     * @returns
     */ /**
     *
     * @param id message W
     * @param status
     * @returns
     */
    markRead(id: number, status: ReadStatus) {
      return request.put<{ message: string; success: boolean }>(path, {
        id,
        status,
      });
    },
    list(searcher: MessageUserSearcher): Promise<Array<any>> {
      return request.get(`${path}`, {
        params: searcher,
        paramsSerializer: aspnetCoreParamerSerial,
      });
    },
  };
};
