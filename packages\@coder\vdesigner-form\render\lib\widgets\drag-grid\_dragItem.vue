<script setup lang="ts">
import type { Widget, WidgetPropsType } from '@coder/vdesigner-core';
import type { LayoutItem } from 'grid-layout-plus';

import { computed, onMounted, ref } from 'vue';

import { FullscreenOutlined } from '@ant-design/icons-vue';
import {
  useMitter,
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';

import { useDragItem } from '../_dragGridUtility';
import { DragGridItemName } from './_options';

defineOptions({ name: DragGridItemName });
const props = defineProps<
  WidgetPropsType & {
    layoutItem: LayoutItem;
    marginY: number;
    rowHeight: number;
    showWidget: boolean;
  }
>();
const emits = defineEmits<{
  (e: 'delete', widget: Widget): void;
}>();
const { contentRef } = useDragItem(props, emits);
const store = useRenderStore(props.renderId);
const { getComponent } = useWidgetRegistry();
const imple = computed(() => store.implement);

const { childWidgets, isDesign } = useWidget(props.widget, props.renderId);
const inDev = computed(() => store.isDev);
const colRef = ref();
const mitter = useMitter(props.renderId);
mitter.emitAddContainer(colRef, childWidgets, props.parentWidget);
const widgetOptions = computed(() => props.widget.options);

const style = computed(() => {
  let bg = widgetOptions.value.backgroundColor;

  if (!props.showWidget) {
    bg = '#ccc';
  }
  return {
    backgroundColor: `${bg} !important`,
    minHeight: '100px',
    textAlign: 'center',
  };
});
const isSelected = ref(false);

onMounted(() => {
  mitter.onUnSelect(({ widget }) => {
    if (widget.id === props.widget.id) {
      isSelected.value = false;
    }
  });
});
const testItemInfo = computed(() => {
  return `h: ${props.layoutItem.h},w: ${props.layoutItem.w},x: ${props.layoutItem.x},y: ${props.layoutItem.y}`;
});

const onSelectWidget = (childWidget: Widget) => {
  isSelected.value = true;
  mitter.emitSelect(childWidget, props.widget);
};

const showWidgetInfo = (childWidget: Widget) => {
  return `${childWidget.id}/${childWidget.type.slice(12)}`;
};
</script>

<template>
  <div
    ref="contentRef"
    :class="{ selected: isSelected }"
    class="grid-item"
    :style="style as any"
    v-if="isDesign"
    v-widget-menu="{
      widgetProps: props,
    }"
  >
    <div class="dev-header-menus" v-if="inDev">
      <a style="display: inline">
        {{ testItemInfo }}
      </a>
    </div>
    <div class="vue-header-menus" v-if="isDesign">
      <a class="draggable-handle" title="拖拽" @click.stop>
        <FullscreenOutlined />
      </a>
    </div>
    <div class="no-drag" ref="colRef" :style="style as any">
      <template
        v-for="childWidget in childWidgets.filter((_) => _ !== undefined)"
        :key="childWidget.id"
      >
        <component
          v-show="showWidget"
          :is="getComponent(childWidget.type, imple)"
          :parent-widget="props.parentWidget"
          :render-id="props.renderId"
          :widget="childWidget"
        />
        <ul v-show="!showWidget">
          <li>
            <a @click.stop="() => onSelectWidget(childWidget)">
              {{ showWidgetInfo(childWidget) }}
            </a>
          </li>
        </ul>
      </template>
    </div>
  </div>
  <div :style="style as any" v-else>
    <template v-for="childWidget in childWidgets" :key="childWidget.id">
      <component
        :is="getComponent(childWidget.type, imple)"
        :parent-widget="props.parentWidget"
        :render-id="props.renderId"
        :widget="childWidget"
      />
    </template>
  </div>
</template>
