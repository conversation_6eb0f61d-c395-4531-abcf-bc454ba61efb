import { createApp } from 'vue';

import CoderMember, { ICoderMemberOption } from '@coder/system-ui';
import performerSelector from 'coder-performer-select';

import App from './app.vue';
import DevelopComponent, { IDocumentOption } from './index';

import 'ant-design-vue/dist/antd.css';

const BaseUrl = 'http://************/api';

const DevelopCompentsUrl = 'http://************/api/docments';
createApp(App)
  .use(DevelopComponent, {
    getPreviewFileHost: 'http://gateway/docments/file',
    getToken: () => {
      return window.localStorage.getItem('token');
    },
    path: `${DevelopCompentsUrl}`,
    previewHost: 'http://************/api/filePreview',
    request: axios,
  } as IDocumentOption)
  .use(CoderMember, {
    getToken() {
      return window.localStorage.getItem('token');
    },
    path: `${BaseUrl}/member`,
    request: axios,
  } as unknown as ICoderMemberOption)
  .use(performerSelector, {
    MemberUrl: `${BaseUrl}/member`,
    OrgUrl: `${BaseUrl}/org`,
    Request: axios,
  })
  .mount('#app');
