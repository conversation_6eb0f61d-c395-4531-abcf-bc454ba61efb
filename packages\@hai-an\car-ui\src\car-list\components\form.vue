<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
} from 'ant-design-vue';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({ carNo: '', deleteFlag: '', driverName: '' });
const options = [
  { key: 'false', label: '正常', value: 'false' },
  { key: 'true', label: '已删除', value: 'true' },
];
const doSearch = () => {
  emit('onSearch', searchForm);
};
const handleChange = (sel: any) => {
  searchForm.deleteFlag = sel ? sel.value : '';
};
</script>
<template>
  <div class="space-align-container">
    <AForm :model="searchForm" layout="inline">
      <AFormItem label="车牌号">
        <AInput v-model:value="searchForm.carNo" />
      </AFormItem>
      <AFormItem label="联系人">
        <AInput v-model:value="searchForm.driverName" />
      </AFormItem>
      <AFormItem label="删除标识">
        <ASelect
          v-model="searchForm.deleteFlag"
          :allow-clear="true"
          :options="options"
          label-in-value
          style="width: 120px"
          @change="handleChange"
        />
      </AFormItem>
      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
