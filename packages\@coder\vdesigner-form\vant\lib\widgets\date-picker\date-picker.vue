<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';
import type { DatePickerOptions } from '@coder/vdesigner-form-render';

import { ref } from 'vue';

import { useWidget } from '@coder/vdesigner-core';
import { DatePicker as VanDatePicker } from 'vant';

import FormItem from '../formItem/formItem.vue';

const props = defineProps<WidgetPropsType>();

const showPicker = ref(false);

const { callJsCode, isDesign, value } = useWidget(props.widget, props.renderId);
const options = props.widget.options as DatePickerOptions;

const onChange = () => {
  if (!options.changeEvent || isDesign.value) return;
  callJsCode(options.changeEvent);
};

const onConfirm = (selectedValues: any) => {
  value.value = (selectedValues as Array<string>).join('/');
  showPicker.value = false;
  onChange();
};
</script>
<template>
  <FormItem
    :parent-widget="props.parentWidget"
    :render-id="props.renderId"
    :widget="props.widget"
    @click="showPicker = true"
  />
  <van-popup v-model:show="showPicker" position="bottom">
    <VanDatePicker
      @cancel="showPicker = false"
      @confirm="(ev) => onConfirm(ev)"
    />
  </van-popup>
</template>
