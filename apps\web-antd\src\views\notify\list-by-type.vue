<script lang="ts" setup>
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { NotifyMyMessageList } from '@coder/notify-ui';

const router = useRoute();
const type = router.params.type as string;

const { setTabTitle } = useTabs();
onMounted(() => {
  setTabTitle(`我的${type}`);
});
</script>
<template>
  <Page description="消息类型管理" title="消息类型">
    <!-- com:{
meta:{
 title:'消息列表-类型',
 icon:'lucide:message-square',
 hideInMenu:true,
},
name:'CoderNotifyListByType',
path:'list/:type'
    } -->
    <NotifyMyMessageList :type="type" />
  </Page>
</template>
