<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { NotifyMyMessageList } from '@coder/notify-ui';

const router = useRoute();
const type = router.params.type as string;

const myTitle = ref(`我的${type}`);
const { setTabTitle } = useTabs();
onMounted(() => {
  setTabTitle(`我的${type}`);
  if (type === '文件') {
    myTitle.value = '文件下载';
  }
});
</script>
<template>
  <Page :title="myTitle">
    <!-- com:{
meta:{
 title:'消息列表-类型',
 icon:'lucide:message-square',
 hideInMenu:true,
},
name:'CoderNotifyListByType',
path:'list/:type'
    } -->
    <NotifyMyMessageList :type="type" />
  </Page>
</template>
