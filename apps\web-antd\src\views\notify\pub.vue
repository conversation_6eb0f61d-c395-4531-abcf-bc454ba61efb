<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { NotifyPubMessage } from '@coder/notify-ui';

defineOptions({
  name: 'NotifyManagePub',
});
const typeName = useRoute().params.typeName as string;
const id = useRoute().params.id
  ? Number.parseInt(useRoute().params.id as string)
  : undefined;
const { closeCurrentTab } = useTabs();
const title = ref(`发布${typeName}`);
const onComplete = () => {
  closeCurrentTab();
};
const routerL = useRouter();
const handlePreviewFile = (url: string) => {
  routerL.push({
    name: 'PreviewDocument',
    query: {
      url,
    },
  });
};
</script>
<template>
  <Page :title="title" description="发布或者修改已发布消息内容。">
    <!-- com:{
meta:{
  title:'发布消息',
  icon:'lucide:message-square',
  hideInMenu:true,
},
name:'NotifyManagePub',
path:'pub/:typeName/:id?',
    } -->
    <NotifyPubMessage
      :id="id"
      :type-name="typeName"
      @complete="onComplete"
      @preview-file="handlePreviewFile"
    />
  </Page>
</template>
