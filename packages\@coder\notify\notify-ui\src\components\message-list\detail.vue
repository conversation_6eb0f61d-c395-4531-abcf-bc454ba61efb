<script setup lang="ts">
import type { MessageViewModel } from '@coder/notify-api';

import { computed, onMounted, ref, watch } from 'vue';

import { getUserMessageApi, ReadStatus } from '@coder/notify-api';
import { FormRender } from '@coder/vdesigner-form-render';
import {
  Card as ACard,
  message as antMessage,
  Switch as ASwitch,
} from 'ant-design-vue';

import AttachmentsComponent from '../attachment/attachment.vue';

const props = defineProps<{ messageId: number }>();
const emits = defineEmits<{
  (e: 'loaded', message: MessageViewModel): void;
  (e: 'changeState', message: MessageViewModel): void;
  (e: 'previewFile', url: string): void;
}>();

const vFormRef = ref();
const formJson = ref({});
const formData = ref({});
const visible = ref(false);
const message = ref<MessageViewModel>();
const api = getUserMessageApi();

const markRead = (checked: boolean | number | string) => {
  const checkedBool = typeof checked === 'boolean' ? checked : Boolean(checked);
  const status = checkedBool ? ReadStatus.read : ReadStatus.unread;
  api.markRead(props.messageId, status).then((resp) => {
    if (message.value === undefined) return;
    if (resp.success) {
      antMessage.info(resp.message as any);
      message.value.status = status;
      emits('changeState', message.value);
    } else {
      antMessage.error(resp.message as any);
    }
  });
};
const handlePreviewFile = (url: string) => {
  emits('previewFile', url);
};
const read = computed({
  get: () => message?.value?.status === ReadStatus.read,
  set: (v) => {
    markRead(v);
  },
});

const reload = () => {
  if (!props.messageId) return;
  api.get(props.messageId).then((resp) => {
    message.value = resp;
    // formJson.value = JSON.parse(message.value.detailFormDesign);
    formJson.value = JSON.parse(message.value.detailFormDesign).rootWidget;
    formData.value = message.value;

    emits('loaded', message.value);

    if (message.value.status === ReadStatus.unread) {
      setTimeout(() => {
        markRead(true);
      }, 2000);
    }
    visible.value = true;
  });
};
watch(
  () => props.messageId,
  () => reload(),
);
onMounted(() => {
  reload();
});
</script>

<template>
  <ACard>
    <template #extra>
      <ASwitch
        :checked="read"
        checked-children="已读"
        un-checked-children="未读"
        @change="markRead"
      />
    </template>
    <FormRender
      v-if="visible"
      ref="vFormRef"
      :form-data="formData"
      :render-config="formJson"
    />
    <AttachmentsComponent
      :is-edit="false"
      :message-id="messageId"
      @preview-file="handlePreviewFile"
    />
  </ACard>
</template>
