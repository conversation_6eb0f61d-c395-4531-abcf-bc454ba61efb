import type {
  WidgetDefinition,
  WidgetOptionEditorSetting,
} from '@coder/vdesigner-core';

import type { Component } from 'vue';

import { MinusOutlined as Icon } from '@ant-design/icons-vue';
import {
  stringEditor,
  useWidgetRegistry,
  WidgetGroup,
} from '@coder/vdesigner-core';

export type DividerOptions = {
  text: string;
};
export class DividerOption {
  [key: string]: any;
  activatedEvent: string | undefined;
  deactivatedEvent: string | undefined;

  mountedEvent: string | undefined;
  text: string = '分割线';
  unmountedEvent: string | undefined;
}
const editor = () => {
  const CheckboxOptionsEditor: Record<string, WidgetOptionEditorSetting> = {
    text: stringEditor('文字'),
  };
  return CheckboxOptionsEditor;
};

export const WidgetDividerType = 'CoderVDesignDivider';
export const registerDivider = (
  componentInput: Component,
  implement: string = 'default',
) => {
  const dividerDefine = {
    group: WidgetGroup.Basic,
    icon: Icon,
    optionEditors: editor(),
    intelliSense: `delcare class CoderVDesignDividerOptions {
      text: string;
      label:string;
    };
    delcare class CoderVDesignDivider implements Widget{
      options:CoderVDesignDividerOptions
    }
    `,
    label: '分割线',
    mode: 'Item',
    options: () => new DividerOption(),
    order: 5,
    type: WidgetDividerType,
  } as WidgetDefinition;

  const { register } = useWidgetRegistry();
  register(dividerDefine, componentInput, implement);
};
