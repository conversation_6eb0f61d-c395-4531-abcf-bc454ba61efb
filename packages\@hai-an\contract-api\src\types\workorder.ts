export interface ContractWorkOrderViewModel {
  contractCode: string;
  contractId: number;
  contractName: string;
  createBy: string;
  createTime: string;
  id: number;
  workOrderId: number;
  workOrderNo: string;
  workOrderStatus: string;
  workOrderSubject: string;
  workOrderWorkProcessId: number;
  workOrderWorkProcessName: string;
}

export interface ContractWorkOrderSearcher {
  contractCode: string;
  contractId?: number;
  contractName: string;
  page?: number;
  pageSize?: number;
  workOrderNo: string;
}
