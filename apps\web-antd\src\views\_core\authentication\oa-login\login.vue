<script setup lang="ts">
import AppLogo from './app-logo.vue';
import LoginForm from './login-form.vue';

const signInTitle = '海安办公系统';
const signInDesc = '请输入公司员工号登录。如果没有请联系人事主管';

const prefixCls = 'login';
const title = '海安OA系统';
</script>

<template>
  <div :class="prefixCls" class="relative h-full w-full px-4">
    <span class="-enter-x xl:hidden">
      <AppLogo :always-show-title="true" />
    </span>
    <div class="container relative mx-auto h-full py-2 sm:px-10">
      <div class="flex h-full">
        <div class="mr-4 hidden min-h-full pl-4 xl:flex xl:w-6/12 xl:flex-col">
          <AppLogo class="-enter-x" />
          <div class="my-auto">
            <img
              :alt="title"
              src="./assets/login-box-bg.svg"
              class="-enter-x -mt-16 w-1/2"
            />
            <div class="-enter-x mt-10 font-medium text-white">
              <span class="mt-4 inline-block text-3xl"> {{ signInTitle }}</span>
            </div>
            <div
              class="text-md -enter-x mt-5 font-normal text-white dark:text-gray-500"
            >
              {{ signInDesc }}
            </div>
          </div>
        </div>
        <div
          class="flex h-full w-full py-5 xl:my-0 xl:h-auto xl:w-6/12 xl:py-0"
        >
          <div
            :class="`${prefixCls}-form`"
            class="enter-x relative mx-auto my-auto w-full rounded-md px-5 py-8 shadow-md sm:w-3/4 sm:px-8 lg:w-2/4 xl:ml-16 xl:w-auto xl:bg-transparent xl:p-4 xl:shadow-none"
          >
            <LoginForm />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 小屏幕设备（手机） */
@media screen and (max-width: 1279px) {
  .container {
    width: 100%;
  }

  .login::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: '';
    background-color: #293146 !important;
    background-image: url('./assets/login-bg-dark.svg');
    background-repeat: no-repeat;
    background-position: 100%;
    background-size: auto 100%;
  }

  .login-form {
    background-color: #fff;
  }
}

/* 大屏幕设备（桌面） */
@media screen and (min-width: 1280px) {
  .login::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin-left: -48%;
    content: '';
    background-image: url('./assets/login-bg.svg');
    background-repeat: no-repeat;
    background-position: 100%;
    background-size: auto 100%;
  }

  .login-form {
    width: 100%;
    padding-right: 50px;
  }
}

.login {
  min-height: 100%;
  overflow: hidden;
}
</style>
