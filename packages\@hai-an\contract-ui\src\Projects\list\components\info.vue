<script setup lang="ts">
import { onMounted, reactive } from 'vue';

import {
  createProjectApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
} from 'ant-design-vue';

import { dayF } from '../../../util';

const props = defineProps({
  id: { default: 0, type: Number },
});

const api = createProjectApi(options.request, options.path);
const submitForm = reactive({
  code: '',
  createBy: '',
  createTime: null,
  id: 0,
  manager: '',
  name: '',
  phone: '',
});

const reload = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};
onMounted(() => {
  reload();
});
</script>
<template>
  <div style="padding: 10px; background-color: #ececec">
    <ADescriptions bordered size="small">
      <ADescriptionsItem label="合同项目编号">
        {{ submitForm.code }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同项目名称">
        {{ submitForm.name }}
      </ADescriptionsItem>
      <ADescriptionsItem label="负责人">
        {{ submitForm.manager }}
      </ADescriptionsItem>
      <ADescriptionsItem label="负责人电话">
        {{ submitForm.phone }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建人">
        {{ submitForm.createBy }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建日期">
        {{ dayF(submitForm.createTime) }}
      </ADescriptionsItem>
    </ADescriptions>
  </div>
</template>
