import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

export class LinkContractFromConvert extends V1Convert {
  constructor() {
    super('link-contract', 'link-contract');
  }
  override SetOption(
    v1Widget: any,
    v2: Widget,
    _cfg: Record<string, any>,
  ): void {
    const v1 = v1Widget.options;
    // eslint-disable-next-line no-console
    console.log('v1 link-contract', v1);
    v2.options = {
      name: v1.name,
      labelHidden: v1.labelHidden,
      label: v1.label ?? '合同关联',
    };
  }
}
