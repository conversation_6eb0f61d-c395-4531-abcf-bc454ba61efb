import type { HumanResourceLogItem } from '@hai-an/human-api';
import type { TableColumnType } from 'ant-design-vue';

import { h } from 'vue';

import { Button } from 'ant-design-vue';
import dayjs from 'dayjs';

/**
 * 人力资源业务类型映射
 */
const humanResourceTypes: Record<number, string> = {
  101: '出差',
  201: '年休假',
  202: '婚假',
  203: '产假',
  204: '陪产假',
  205: '育儿假',
  206: '独生子女护理假',
  207: '工伤假',
  208: '丧假',
  209: '病假',
  210: '事假',
  211: '补休假',
  299: '其他',
  301: '法定节假日加班',
  302: '工作和公休日加班',
};

/**
 * 创建表格列定义
 * @param onOrderInfo 点击工单编号时的回调函数
 * @returns 表格列定义
 */
export const createColumns = (
  onOrderInfo: (orderId: number) => void,
): TableColumnType<HumanResourceLogItem>[] => [
  { title: '工单类型', dataIndex: 'orderType', width: 120, align: 'center' },
  {
    title: '工单编号',
    key: 'action',
    width: 120,
    fixed: 'right',
    align: 'center',
    customRender: ({ record }: { record: HumanResourceLogItem }) => {
      return h(
        Button,
        {
          type: 'link',
          onClick: () => onOrderInfo(record.orderId),
        },
        () => record.orderNo,
      );
    },
  },

  {
    title: '类型',
    dataIndex: 'humanResourceType',
    width: 120,
    align: 'center',
    customRender: ({ text }: { text: number }) =>
      humanResourceTypes[text] || text,
  },
  {
    title: '日期',
    dataIndex: 'humanResourceDate',
    align: 'center',
    customRender: ({ text }: { text: string }) =>
      text ? dayjs(text).format('YYYY年MM月DD日') : '',
  },
  // {
  //   title: '持续时间（天）',
  //   dataIndex: 'humanResourceuration',
  //   align: 'center',
  //   customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00'
  // },
  { title: '时间段', dataIndex: 'timeDescription', align: 'center' },
];
