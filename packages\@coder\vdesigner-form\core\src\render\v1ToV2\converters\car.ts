import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

/*
v1:otion
{
              "key": 30635,
              "type": "select-car",
              "icon": "alert",
              "formItemFlag": true,
              "options": {
                "name": "carInfo",
                "label": "选择车辆",
                "folded": false,
                "showFold": true,
                "cardWidth": "100%",
                "shadow": "never",
                "customClass": [],
                "defaultValue": "",
                "disabled": false,
                "hidden": false,
                "labelAlign": "label-right-align",
                "labelHidden": false,
                "labelWidth": "100",
                "readonly": false,
                "size": "",
                "onChange": ""
              },
              "id": "selectcar62028"
            },*/

export class CarSelectConvert extends V1Convert {
  constructor() {
    super('select-car', 'select-car');
  }
  override SetOption(
    v1Widget: any,
    v2: Widget,
    _cfg: Record<string, any>,
  ): void {
    const v1 = v1Widget.options;
    v2.options = {
      name: v1.name,
      label: v1.label,
      hidden: v1.hidden,
    };
  }
}
