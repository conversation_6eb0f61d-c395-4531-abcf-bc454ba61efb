import type { RequestClient } from '@vben/request';

export interface GetToken {
  (): string;
}

export interface IDocumentOption {
  /**
   * 内网访问文件的路径，这样速度更快更安全。
   * 如 http://gateway/document/file/get-file-by-id/1
   */
  getPreviewFileHost: string;
  getToken?: GetToken;
  path: string;
  /**
   * 外网访问的预览链接，客户端能够正常访问的链接
   */
  previewHost: string;
  request: RequestClient;
}

export { default as fileManagementPanel } from './filePanel/index.vue';
export { default as FileSearch } from './fileSearch/index.vue';

export const documentOptions: IDocumentOption = {
  getPreviewFileHost: '',
  getToken: undefined,
  path: '',
  previewHost: '',
  request: {} as RequestClient,
};

export default {
  install: (_app: any, option: IDocumentOption) => {
    // console.error('option:', option);
    option.path = option.path.replace(/\/$/, '');

    if (!option.request) {
      throw new Error('request is required');
    }
    if (option.getPreviewFileHost)
      documentOptions.getPreviewFileHost = option.getPreviewFileHost.replace(
        /\/$/,
        '',
      );
    if (option.getPreviewFileHost)
      documentOptions.previewHost = option.previewHost;
    documentOptions.path = option.path;
    documentOptions.request = option.request;
    documentOptions.getToken = option.getToken;
  },
};
