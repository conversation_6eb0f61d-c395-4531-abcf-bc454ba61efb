<script setup lang="ts">
import type { HumanResourceLogItem } from '@hai-an/human-api';

import type { PropType } from 'vue';

import { computed, ref, watch } from 'vue';

import {
  createHumanResourceApi,
  haianHumanOption as option,
} from '@hai-an/human-api';
import { Modal as AModal, Table as ATable } from 'ant-design-vue';

import { createColumns } from './columns';

const props = defineProps({
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  itemId: {
    type: String as PropType<string>,
    default: '',
  },
});

const emit = defineEmits(['update:visible', 'orderInfo']);

// 在组件中
const columns = createColumns((orderId: number) => {
  // 处理工单信息点击事件
  emit('orderInfo', orderId);
});

const API = createHumanResourceApi(option.request, option.path);

const isVisible = computed(() => props.visible);
const loading = ref<boolean>(false);
const tableData = ref<HumanResourceLogItem[]>([]);

const handleCancel = () => {
  emit('update:visible', false);
};

watch(
  () => props.visible,
  (newVal) => {
    newVal && loadData();
  },
);
const loadData = async () => {
  loading.value = true;
  try {
    const res: any = await API.listDetail(props.itemId);
    loading.value = false;
    tableData.value = res.rows.filter(
      (item: HumanResourceLogItem) => item.delFlag !== '1',
    );
  } catch {
    loading.value = false;
  }
};
</script>

<template>
  <AModal
    :visible="isVisible"
    title="查看详情"
    :width="1100"
    ok-text="确认"
    cancel-text="取消"
    @ok="handleCancel"
    @cancel="handleCancel"
  >
    <ATable
      :data-source="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="false"
      bordered
    />
  </AModal>
</template>

<style scoped></style>
