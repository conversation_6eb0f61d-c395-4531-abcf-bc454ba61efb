import type { HaianCarOption } from './haianCarOption';

import { haianCarOption } from './haianCarOption';

export { default as CarList } from './car-list/index.vue';
export * from './haianCarOption';
export { default as LabelCar } from './labelCar.vue';
export { default as SelectCar } from './selectCar.vue';

export default {
  /**
   * car
   * @param _ app
   * @param opt 配置
   */
  install: (_: any, opt: HaianCarOption) => {
    if (opt.carPath?.endsWith('/')) {
      opt.carPath = opt.carPath?.slice(0, Math.max(0, opt.carPath.length - 1));
    }

    Object.assign(haianCarOption, opt);
  },
};
