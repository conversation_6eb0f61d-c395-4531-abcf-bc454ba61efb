import type { HumanResourceListItem } from '@hai-an/human-api';

import { h } from 'vue';

import { Button } from 'ant-design-vue';

export const createColumns = (
  onPreview: (id: string) => void,
  type: string,
) => [
  { title: '用户账号', dataIndex: 'userName', width: 120, align: 'center' },
  { title: '姓名', dataIndex: 'name', width: 100, align: 'center' },
  { title: '部门', dataIndex: 'orgName', width: 250, align: 'center' },
  {
    title: type === '2' ? '年' : '年月',
    dataIndex: 'yearMonth',
    width: 100,
    align: 'center',
    customRender: ({ text }: { text: string }) => {
      if (type === '2') {
        return text.slice(0, 4);
      }
      return `${text.slice(0, 4)}-${text.slice(5)}`;
    },
  },
  {
    title: '病假天数',
    dataIndex: 'bingJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '产假天数',
    dataIndex: 'chanJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '出差天数',
    dataIndex: 'chuChaiDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '工伤假天数',
    dataIndex: 'gongShangJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '工休日加班天数',
    dataIndex: 'gongXiuRiJiaBanDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '护理假天数',
    dataIndex: 'huLiJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '婚假天数',
    dataIndex: 'hunJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '节假日加班天数',
    dataIndex: 'jieJiaRiJiaBanDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '年休假请假天数',
    dataIndex: 'nianXiuJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '年假总天数',
    dataIndex: 'totalDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '年假剩余天数',
    dataIndex: 'remainingDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '陪产假天数',
    dataIndex: 'peiChanJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '丧假天数',
    dataIndex: 'sangJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '事假天数',
    dataIndex: 'shiJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '育儿假天数',
    dataIndex: 'yuErJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '补休假总天数',
    dataIndex: 'buXiuJiaTotalDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '补休假请假天数',
    dataIndex: 'buXiuJiaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '补休假剩余天数',
    dataIndex: 'buXiuJiaRemainingDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },
  {
    title: '其他',
    dataIndex: 'qiTaDays',
    align: 'center',
    customRender: ({ text }: { text: number }) => text?.toFixed(2) || '0.00',
  },

  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right',
    align: 'center',
    customRender: ({ record }: { record: HumanResourceListItem }) => {
      return h(
        Button,
        {
          type: 'link',
          onClick: () => onPreview(record.id),
        },
        () => '查看详情',
      );
    },
  },
];
