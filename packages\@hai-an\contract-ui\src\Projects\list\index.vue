<!-- 项目管理 -->
<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { PlusOutlined } from '@ant-design/icons-vue';
import { Button as AButton, Card as ACard, Table } from 'ant-design-vue';

import ContractForm from './components/form.vue';
import { makeColumns } from './tableColumn';
import { showAdd } from './useModal';
import { useProjectList } from './useProjectList';

// 使用 useProjectList 钩子替换原有的数据加载和搜索逻辑
const {
  searchForm,
  dataSource,
  pagination,
  search,
  filter,
  loading,
  pageChange,
} = useProjectList();

const columns = ref(makeColumns(filter));

const onAdd = () => {
  showAdd(filter);
};

const onSearch = (val: any) => {
  Object.assign(searchForm, val); // 赋值到searchForm
  search();
};

onMounted(() => {
  search();
});
</script>
<template>
  <ACard>
    <ContractForm @on-search="onSearch" />
  </ACard>
  <ACard class="mt-3">
    <div class="space-align-container">
      <AButton type="primary" shape="round" @click="onAdd" size="small">
        <PlusOutlined />新增
      </AButton>
    </div>

    <Table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      @change="pageChange"
    />
  </ACard>
</template>
<style scoped lang="scss">
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
<!-- <style >
.ant-modal-confirm-info {
  width: 900px !important;
}
</style> -->
