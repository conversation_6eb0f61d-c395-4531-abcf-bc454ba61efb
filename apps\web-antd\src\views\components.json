{"meta": {"title": "BasicLayout"}, "name": "BasicLayout", "path": "BasicLayout", "children": [{"meta": {"title": "page.basicSetting.title", "url": "/workbench"}, "path": "/basic-setting", "children": [{"component": "/basic-setting/car/index", "enable": true, "meta": {"title": "车辆管理", "icon": "lucide:car"}, "name": "CarManagement", "path": "car-management"}, {"component": "/basic-setting/org/index", "enable": true, "meta": {"title": "page.org.title", "icon": "lucide:users"}, "name": "CoderOrgList", "path": "/basic-setting/org"}, {"component": "/basic-setting/role-list/role-list", "enable": true, "meta": {"title": "page.role.title", "icon": "lucide:users"}, "name": "RoleSetting", "path": "role"}, {"component": "/basic-setting/user-list/user-list", "enable": true, "meta": {"title": "page.user.title", "icon": "lucide:user"}, "name": "UserList", "path": "user-list"}], "name": "BasicSetting", "enable": true}, {"meta": {"title": "合同台账"}, "path": "/contract", "children": [{"component": "/contracts/contract-groups/index", "enable": true, "meta": {"title": "合同组管理", "icon": "lucide:square-chart-gantt"}, "name": "ContractGroupList", "path": "/contract/group-list"}, {"component": "/contracts/contract-list/history", "enable": true, "meta": {"title": "合同日志", "icon": "lucide:folder-clock"}, "name": "ContractHistory", "path": "/contract/history"}, {"component": "/contracts/contract-list/manage-list", "enable": true, "meta": {"title": "合同管理", "icon": "lucide:book-check"}, "name": "ContractList", "path": "/contract/list"}, {"component": "/contracts/projects/index", "enable": true, "meta": {"title": "项目管理", "icon": "lucide:folder-kanban"}, "name": "ProjectList", "path": "/contract/project-list"}, {"component": "/contracts/report/index", "enable": true, "meta": {"title": "合同报表", "icon": "lucide:folder-kanban"}, "name": "ContractReport", "path": "/contract/report"}, {"component": "/contracts/suppliers/index", "enable": true, "meta": {"title": "合作企业", "icon": "lucide:building-2"}, "name": "Suppliers", "path": "/contract/suppliers"}], "name": "ContractManagement", "enable": true}, {"meta": {"title": "page.dashboard.title"}, "path": "/", "children": [{"component": "/dashboard/analytics/index", "enable": true, "meta": {"title": "page.dashboard.analytics", "icon": "carbon:workspace"}, "name": "DashboardAnalyticsIndex", "path": "/analytics"}, {"component": "/dashboard/workbench/index", "enable": true, "meta": {"title": "工作台", "icon": "carbon:workbench"}, "name": "DashboardWorkbenchIndex", "path": "workbench"}, {"component": "/dashboard/workspace/index", "enable": true, "meta": {"title": "工作台(vben)", "icon": "carbon:workspace"}, "name": "DashboardWorkspaceIndex", "path": "/workspace"}], "name": "Dashboard", "enable": true}, {"meta": {"title": "文档管理"}, "path": "/documents", "children": [{"component": "/document/list", "enable": true, "meta": {"title": "文档管理", "icon": "lucide:square-menu"}, "name": "DocumentList", "path": "list-document"}, {"component": "/document/search-list", "enable": true, "meta": {"title": "文档查询", "icon": "lucide:square-menu"}, "name": "DocumentSearch", "path": "document-search"}, {"meta": {"title": "文档下载"}, "path": "download", "children": [], "name": "DocumentMessage", "redirect": "/notify/list/文件", "enable": true}], "name": "Documents", "enable": true}, {"component": "/human-resource/list/index", "enable": true, "meta": {"title": "人力资源列表", "icon": "lucide:folder-kanban"}, "name": "HumanResourceList", "path": "/humanResource/list"}, {"meta": {"title": "我的通知"}, "path": "my-message", "children": [], "name": "MyMessage", "redirect": "/notify/list/通知", "enable": true}, {"meta": {"title": "通知"}, "path": "/notify", "children": [{"component": "/notify/list-by-type", "enable": true, "meta": {"title": "消息列表-类型", "icon": "lucide:message-square", "hideInMenu": true}, "name": "CoderNotifyListByType", "path": "list/:type"}, {"component": "/notify/message-list", "enable": true, "meta": {"title": "通知消息管理", "icon": "lucide:message-square"}, "name": "NotifyManageList", "path": "message/list"}, {"component": "/notify/pub", "enable": true, "meta": {"title": "发布消息", "icon": "lucide:message-square", "hideInMenu": true}, "name": "NotifyManagePub", "path": "pub/:typeName/:id?"}, {"component": "/notify/type-edit", "enable": true, "meta": {"title": "消息类型编辑", "icon": "lucide:edit", "hideInMenu": true}, "name": "CoderNotifyTypeEditor", "path": "type/:id?"}, {"component": "/notify/type-list", "enable": true, "meta": {"title": "消息类型", "icon": "lucide:message-square"}, "name": "CoderNotifyTypeList", "path": "type/list"}, {"component": "/notify/_detail", "enable": true, "meta": {"title": "page.myMessage.content", "hideInMenu": true}, "name": "CoderNotifyDetail", "path": "/notify/detail/:id"}], "name": "Notify", "enable": true}, {"meta": {"title": "页面创建"}, "path": "page-builder", "children": [{"component": "/page-builder/builder", "enable": true, "meta": {"title": "页面创建", "icon": "lucide:server"}, "name": "PageBuilder", "path": "page-designer/:id?"}, {"component": "/page-builder/list", "enable": true, "meta": {"title": "页面列表", "icon": "lucide:server"}, "name": "PageList", "path": "page-list"}, {"component": "/page-builder/view", "enable": true, "meta": {"title": "页面呈现", "icon": "lucide:server"}, "name": "PageDetail", "path": "detail/:id"}], "name": "PageBuilderParent", "enable": true}, {"component": "/pm/index", "enable": true, "meta": {"title": "项目管理", "icon": "ant-design:appstore-outlined"}, "name": "project", "path": "/project"}, {"component": "/preivew/index", "enable": true, "meta": {"title": "文件预览", "hideInMenu": true}, "name": "PreviewDocument", "path": "/preview"}, {"component": "/project-archive/project-archive-history", "enable": true, "meta": {"title": "项目档案日志", "icon": "lucide:folder-clock"}, "name": "ProjectArchiveHistory", "path": "/project-archive/history"}, {"meta": {"title": "工作流"}, "path": "swf", "children": [{"component": "/swf/designer/designer", "enable": true, "meta": {"title": "设计器", "icon": "lucide:code-square", "hideInMenu": true}, "name": "SwfEditor", "path": "designer/:id?"}, {"component": "/swf/designer/work-process-list", "enable": true, "meta": {"title": "工作流列表"}, "name": "WorkProcessList", "path": "work-process-list"}, {"component": "/swf/global-variable/global-variable", "enable": true, "meta": {"title": "全局变量", "icon": "lucide:code-square", "hideInMenu": true}, "name": "SwfGlobalVariableSetting", "path": "swf-global-variable-settting"}, {"component": "/swf/maintenance/index", "enable": true, "meta": {"title": "维护功能", "icon": ""}, "name": "SwfMaintenance", "path": "/maintenance"}, {"component": "/swf/processInstance/detail", "enable": true, "meta": {"title": "工单详情", "icon": "lucide:code-square", "hideInMenu": true}, "name": "SwfProcessInstanceDetail", "path": "order/:id"}, {"component": "/swf/processInstance/edit", "enable": true, "meta": {"title": "修改工单", "icon": "lucide:code-square", "hideInMenu": true}, "name": "SwfProcessInstanceEditor", "path": "order/edit/:id"}, {"component": "/swf/waitList/wait-list", "enable": true, "meta": {"title": "我的待办工单", "icon": "ant-design:align-right-outlined"}, "name": "SwfMyWaitList", "path": "/wait-list"}, {"component": "/swf/workActivity/dispose", "enable": true, "meta": {"title": "处理工作", "icon": "lucide:code-square", "hideInMenu": true}, "name": "SwfWorkActivityDispose", "path": "/works/dispose/:id"}, {"component": "/swf/workActivity/my-works", "enable": true, "meta": {"title": "我的工作"}, "name": "SwfMyWorks", "path": "/my-works"}], "name": "Swf", "enable": true}, {"meta": {"title": "page.sys.title"}, "path": "/sys", "children": [{"component": "/sys/client/client-list", "enable": true, "meta": {"title": "page.service-client.title", "icon": "lucide:server"}, "name": "MicroToken", "path": "micro-service-token"}, {"component": "/sys/file-list/file-list", "enable": true, "meta": {"title": "page.fileManagement.title", "icon": "lucide:folder-search-2"}, "name": "FileList", "path": "file-list", "icon": "lucide:folder-search-2"}, {"component": "/sys/menu/menu-list", "enable": true, "meta": {"title": "page.sys.menu", "icon": "lucide:square-menu"}, "name": "MenuList", "path": "menu"}], "name": "System", "enable": true}, {"component": "/task/list", "enable": true, "meta": {"title": "视频任务列表", "icon": "lucide:server", "hideInMenu": false}, "name": "CoderVideoDetect", "path": "coder-video-detect"}]}