export interface ContractGroupViewModel {
  code: string;
  createBy: string;
  createTime: Date;
  description?: string;
  id: number;
  isDeleted?: boolean;
  name: string;
}

export interface ContractGroupSearch {
  code?: string;
  isDeleted?: boolean;
  name?: string;
  page?: number;
  pageSize?: number;
}

export interface ContractGroupSubmit {
  code: string;
  description?: string;
  id?: number;
  name: string;
}
export interface ContractGroupListItem {
  completeWorkloadIn: number;
  completeWorkloadOut: number;
  contractsIn: ContractGroupViewModel[];
  contractsOut: ContractGroupViewModel[];
  contractTotalIn: number;
  contractTotalOut: number;
  createBy: string;
  createTime: string;
  id: number;
  isDeleted: boolean;
  name: string;
  payTotalIn: number;
  payTotalOut: number;
  updateBy: string;
  updateTime: string;
}
