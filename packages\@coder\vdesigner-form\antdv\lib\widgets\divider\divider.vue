<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';
import type { DividerOptions } from '@coder/vdesigner-form-render';

import { ref } from 'vue';

import { useWidget } from '@coder/vdesigner-core';
import { Divider } from 'ant-design-vue';

const props = defineProps<WidgetPropsType>();
const options = props.widget.options as DividerOptions;
useWidget(props.widget, props.renderId);
const selectorRef = ref();
const selectorClick = (e: Event) => {
  e.stopPropagation();
  e.preventDefault();
  selectorRef.value?.show();
};
</script>

<template>
  <Divider v-widget-menu="{ widgetProps: props }" @click="selectorClick">
    {{ options.text }}
  </Divider>
</template>
