<script setup lang="ts">
import { onMounted, reactive } from 'vue';

import { CheckOutlined } from '@ant-design/icons-vue';
import {
  createInvoiceApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as <PERSON>utton,
  Col as ACol,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Row as ARow,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: 0, type: Number },
});

const emit = defineEmits(['doCancel']);

const api = createInvoiceApi(options.request, options.path);

const submitForm = reactive({
  address: '',
  id: 0,
  name: '',
  owner: '',
  ownerPhone: '',
  remark: '',
});
const dayF = (val) => {
  return dayjs(val).format('YYYY-MM-DD HH:mm');
};
const doCancel = () => {
  emit('doCancel');
};
const realod = () => {
  if (props.id) {
    api.getBy(props.id).then((res) => {
      Object.assign(submitForm, res.data);
    });
  }
};
onMounted(() => {
  realod();
});
</script>
<template>
  <div style="padding: 10px; background-color: #ececec">
    <ADescriptions bordered size="small">
      <ADescriptionsItem label="发票号码">
        {{ submitForm.invoiceNo }}
      </ADescriptionsItem>
      <ADescriptionsItem label="发票时间">
        {{ dayF(submitForm.invoiceDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="开票申请人">
        {{ submitForm.applicant }}
      </ADescriptionsItem>
      <ADescriptionsItem label="开票申请日期">
        {{ dayF(submitForm.applicationDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="开票税率">
        {{ submitForm.taxRate }}
      </ADescriptionsItem>
      <ADescriptionsItem label="凭证号">
        {{ submitForm.voucherNo }}
      </ADescriptionsItem>
      <ADescriptionsItem label="金额">
        {{ submitForm.amount }}
      </ADescriptionsItem>
      <ADescriptionsItem label="支付日期">
        {{ dayF(submitForm.payDate) }}
      </ADescriptionsItem>

      <ADescriptionsItem label="所属工单">
        {{ submitForm.orderNo }}
      </ADescriptionsItem>

      <ADescriptionsItem label="创建人">
        {{ submitForm.createBy }}
      </ADescriptionsItem>

      <ADescriptionsItem label="创建日期">
        {{ dayF(submitForm.createTime) }}
      </ADescriptionsItem>
    </ADescriptions>
  </div>
  <div style="padding: 20px; background-color: #ececec">
    <ARow type="flex" justify="center">
      <ACol :span="3">
        <AButton type="primary" @click="doCancel">
          <CheckOutlined />确定
        </AButton>
      </ACol>
    </ARow>
  </div>
</template>
