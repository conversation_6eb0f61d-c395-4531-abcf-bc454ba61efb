import type { RequestClient } from '@vben/request';

import type {
  ContractHistorySearch,
  ContractHistoryViewModel,
} from './types/history';

export const createHistoryApi = (request: RequestClient, path: string) => {
  return {
    /**
     * Get contract history count
     */
    count(params: ContractHistorySearch): Promise<number> {
      return request.get(`${path}/ContractHistory/count`, { params });
    },

    getById(id: number) {
      return request.get(`${path}/ContractHistory/${id}`);
    },

    /**
     * Get contract history list
     */
    list(params: ContractHistorySearch): Promise<ContractHistoryViewModel[]> {
      return request.get(`${path}/ContractHistory/list`, { params });
    },
  };
};
