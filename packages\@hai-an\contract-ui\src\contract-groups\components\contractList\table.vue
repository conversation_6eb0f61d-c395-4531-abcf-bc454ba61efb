<!-- 一级仓table -->
<script setup lang="ts">
import { reactive } from 'vue';

import { Table } from 'ant-design-vue';

import columnDefined from './tableColumn';

const props = defineProps({
  datas: { default: () => [], type: Array },
  isEidtList: { type: Boolean },
  loading: { type: Boolean },
  pagination: { default: () => {}, type: Object },
});
const emit = defineEmits(['handleTableChange']);

const columns = reactive(columnDefined);

const handleTableChange = (pagination: any) => {
  emit('handleTableChange', pagination);
};
</script>
<template>
  <Table
    class="ant-table-striped"
    size="middle"
    sticky
    bordered
    :row-key="(data) => data.id"
    :pagination="pagination"
    :columns="columns"
    :loading="loading"
    @change="handleTableChange"
    :data-source="props.datas"
  />
</template>
