<script lang="ts" setup>
import type { WidgetPropsType } from '@coder/vdesigner-core';

import type { CarLabelOption } from './_options';

import { computed } from 'vue';

import {
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { LabelCar } from '@hai-an/car-ui';

const props = defineProps<WidgetPropsType>();
const { getComponent } = useWidgetRegistry();
const { widget, getWidgetOption } = useWidget(props.widget, props.renderId);
const renderStore = useRenderStore(props.renderId);
const options = widget.value.options as CarLabelOption;
const CoderVDesignFormItem = getComponent(
  'CoderVDesignFormItem',
  renderStore.implement,
);
const style = computed(() => {
  return {
    display: options.hidden ? 'none' : 'block',
  };
});
const carid = getWidgetOption<number | string>('carId');
</script>
<template>
  <CoderVDesignFormItem v-bind="props">
    <LabelCar :car-id="carid" :style="style" />
  </CoderVDesignFormItem>
</template>
