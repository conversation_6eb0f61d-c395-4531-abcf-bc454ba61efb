import type { SimpleSFileViewModel } from '@coder/notify-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import type { AttachmentPropsType } from './useAttachment';

import { getSizeInfo } from '@coder/common-api';
import { DownloadButton } from '@coder/file-download';
import { getMessageFileApi } from '@coder/notify-api';
import { Button, Modal } from 'ant-design-vue';

export const createColumn = (
  _props: AttachmentPropsType,
  _data: SimpleSFileViewModel[],
  _getList: () => void,
) => {
  const onDelete = (file: SimpleSFileViewModel) => {
    Modal.confirm({
      content: '删除后将无法恢复',
      onOk: async () => {
        await getMessageFileApi().delFile(file.id, _props.messageId ?? 0);
        if (_props.fileIds && _props.messageId === undefined)
          _props.fileIds.splice(_props.fileIds.indexOf(file.id), 1);
        _getList();
      },
      title: '确定要删除文件吗？',
    });
  };

  return [
    {
      align: 'center',
      customRender: ({ index }) => {
        return <span>{index + 1}</span>;
      },
      key: 'index',
      title: '序号',
      width: 100,
    },
    {
      dataIndex: 'name',
      title: '文件名',
    },
    {
      customRender(opt) {
        const t = getSizeInfo(opt.record.fileSize);
        return (
          <span>
            {t.size.toFixed(2)} {t.unit}
          </span>
        );
      },
      dataIndex: 'fileSize',
      title: '文件大小',
    },
    {
      align: 'center',

      customRender: ({ record }) => {
        const file = record as SimpleSFileViewModel;
        const url = getMessageFileApi().getDownloadFilePath(file.id, file.name);

        return [
          <DownloadButton
            btn-text={record.name}
            btnType="link"
            fileName={record.name}
            url={url}
          />,
          <Button danger onClick={() => onDelete(record)} type="link">
            删除
          </Button>,
        ];
      },
      dataIndex: 'action',
      key: 'action',
      title: '操作',
    },
  ] as Array<ColumnType>;
};
