import type { ColumnType } from 'ant-design-vue/es/table';

import { dayF } from '../../util';

export default [
  {
    align: 'center',
    dataIndex: 'action',
    title: '操作',
    width: 320,
    // slots: { customRender: "action" },
  },
  {
    dataIndex: 'invoiceNo',
    title: '发票号码',
    width: 200,
    // slots: { customRender: "invoiceNo" },
  },
  {
    customRender({ record }) {
      return dayF(record.invoiceDate);
    },
    dataIndex: 'invoiceDate',
    title: '发票时间',
  },
  {
    dataIndex: 'applicant',
    title: '开票申请人',
    // slots: { customRender: "applicant" },
  },
  {
    // slots: { customRender: "applicationDate" },
    customRender({ text }) {
      return <>{dayF(text)}</>;
    },
    dataIndex: 'applicationDate',
    title: '开票申请日期',
  },
  {
    dataIndex: 'taxRate',
    title: '开票税率',
    // slots: { customRender: "taxRate" },
  },
  {
    dataIndex: 'voucherNo',
    title: '凭证号',
    // slots: { customRender: "voucherNo" },
  },
  {
    dataIndex: 'amount',
    title: '金额',
    // slots: { customRender: "amount" },
  },
  {
    customRender({ text }) {
      return <>{dayF(text)}</>;
    },
    dataIndex: 'payDate',
    title: '支付日期',
  },
  {
    dataIndex: 'orderNo',
    title: '所属工单',
    // slots: { customRender: "orderNo" },
  },
  {
    dataIndex: 'isDeleted',
    title: '状态',
    // slots: { customRender: "isDeleted" },
  },
  {
    dataIndex: 'createBy',
    title: '创建人',
    // slots: { customRender: "createBy" },
  },
  {
    customRender({ text }) {
      return <>{dayF(text)}</>;
    },
    dataIndex: 'createTime',
    title: '创建日期',
  },
] as ColumnType[];
