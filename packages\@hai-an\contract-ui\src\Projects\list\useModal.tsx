import type { ProjectViewModel } from '@hai-an/contract-api';

import { ref } from 'vue';

import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  createProjectApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import { Button, message, Modal, Space } from 'ant-design-vue';

import Editor from './components/edit.vue';
import Detail from './components/info.vue';

export const showDetail = (project: ProjectViewModel) => {
  Modal.info({
    title: '项目详细信息',
    content: () => <Detail id={project.id} />,
    cancelText: false,
    style: { width: '900px' },
  });
};

export const showAdd = (reload: { (): void }) => {
  const editorRef = ref();
  const onSave = () => {
    editorRef.value.save().then(() => {
      modal.destroy();
      reload();
    });
  };
  const onReset = () => {
    editorRef.value.reset();
  };
  const modal = Modal.info({
    title: '新增合同项目信息',
    content: () => <Editor id={0} ref={editorRef} />,
    cancelText: false,
    style: { width: '900px' },
    footer: () => {
      return (
        <Space>
          <Button onClick={() => onReset}>重置</Button>

          <Button onClick={() => modal.destroy()}>取消</Button>

          <Button onClick={() => onSave()} type="primary">
            保存
          </Button>
        </Space>
      );
    },
  });
};

export const showEditor = (project: ProjectViewModel, reload: { (): void }) => {
  const editorRef = ref();
  const onSave = () => {
    editorRef.value.save().then(() => {
      modal.destroy();
      reload();
    });
  };
  const onReset = () => {
    editorRef.value.reset();
  };
  const modal = Modal.info({
    title: '修改合同项目信息',
    content: () => <Editor id={project.id} ref={editorRef} />,
    cancelText: false,
    style: { width: '900px' },
    footer: () => {
      return (
        <Space>
          <Button onClick={() => onReset}>重置</Button>

          <Button onClick={() => modal.destroy()}>取消</Button>

          <Button onClick={() => onSave()} type="primary">
            保存
          </Button>
        </Space>
      );
    },
  });
};

export const deleteProject = (val: ProjectViewModel, reload: { (): void }) => {
  const api = createProjectApi(options.request, options.path);
  Modal.confirm({
    cancelText: '取消',
    content: () => {
      return (
        <div style="color:red;">是否确定删除选定信息?删除后将无法恢复！</div>
      );
    },
    icon: () => <ExclamationCircleOutlined />,
    okText: '确认删除',
    onCancel() {
      message.info('取消删除！');
    },
    async onOk() {
      try {
        // 确保 api 初始化完成
        await Promise.resolve();
        const res = await api.delete(val.id);
        if (res.success) {
          message.success(res.message);
          reload();
        } else {
          message.error(res.message);
        }
      } catch (error) {
        message.error(`删除失败：${(error as Error).message}`);
      }
    },
    title: () => '删除确认',
  });
};
