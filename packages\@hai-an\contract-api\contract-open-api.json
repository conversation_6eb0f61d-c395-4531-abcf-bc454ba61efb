{"openapi": "3.0.1", "info": {"title": "oa.contract.service", "version": "v1"}, "paths": {"/Contract/list": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "LinkCode", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "BookType", "in": "query", "schema": {"$ref": "#/components/schemas/BookType"}}, {"name": "OppositeCode", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "PlanBookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "ServiceShip", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderCloseDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsMaster", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsWorkloadLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}}}}}}, "/Contract/count": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "LinkCode", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "BookType", "in": "query", "schema": {"$ref": "#/components/schemas/BookType"}}, {"name": "OppositeCode", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "PlanBookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "ServiceShip", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderCloseDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsMaster", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsWorkloadLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Contract/master-list": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "LinkCode", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "BookType", "in": "query", "schema": {"$ref": "#/components/schemas/BookType"}}, {"name": "OppositeCode", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "PlanBookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "ServiceShip", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderCloseDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsMaster", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsWorkloadLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}}}}}}, "/Contract/master-count": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "LinkCode", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "BookType", "in": "query", "schema": {"$ref": "#/components/schemas/BookType"}}, {"name": "OppositeCode", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "PlanBookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "ServiceShip", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderCloseDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsMaster", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsWorkloadLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Contract/{id}": {"get": {"tags": ["Contract"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractViewModelContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractViewModelContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractViewModelContractResultWithData"}}}}}}, "delete": {"tags": ["Contract"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Contract/save": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Contract/exist": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Contract/get-by-code/{code}": {"get": {"tags": ["Contract"], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}}}}}}, "/Contract/get-contract-code": {"get": {"tags": ["Contract"], "parameters": [{"name": "Year", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"type": "string"}}, {"name": "Org", "in": "query", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractCodeContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractCodeContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractCodeContractResultWithData"}}}}}}}, "/Contract/get-c-contract-code": {"get": {"tags": ["Contract"], "parameters": [{"name": "Year", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"type": "string"}}, {"name": "Org", "in": "query", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractCodeContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractCodeContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractCodeContractResultWithData"}}}}}}}, "/Contract/save-from-order": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractOrderSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractOrderSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractOrderSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractOrderSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/link-list": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "LinkCode", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "BookType", "in": "query", "schema": {"$ref": "#/components/schemas/BookType"}}, {"name": "OppositeCode", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "PlanBookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "ServiceShip", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderCloseDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsMaster", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsWorkloadLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractListItemIEnumerableContractResultWithData"}}}}}}}, "/Contract/link-count": {"get": {"tags": ["Contract"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "LinkCode", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "BookType", "in": "query", "schema": {"$ref": "#/components/schemas/BookType"}}, {"name": "OppositeCode", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "PlanBookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "ServiceShip", "in": "query", "schema": {"type": "string"}}, {"name": "ApplyDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "OrderCloseDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsMaster", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsWorkloadLock", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Contract/contract-lock": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/contract-workload-lock": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractLockSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/save-from-receive": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/save-from-pay": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/create-from-receive": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractReceiveSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/create-from-pay": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractPaySubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/save-from-group": {"post": {"tags": ["Contract"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/GroupSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/GroupSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GroupSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GroupSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResultContractResultWithData"}}}}}}}, "/Contract/get-last-contract-order-info/{code}": {"get": {"tags": ["Contract"], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "schema": {"$ref": "#/components/schemas/OrderType"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractOrderContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractOrderContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractOrderContractResultWithData"}}}}}}}, "/Contract/get-contract-order-summary/{code}": {"get": {"tags": ["Contract"], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "orderType", "in": "query", "schema": {"$ref": "#/components/schemas/OrderType"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrderSummaryContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrderSummaryContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderSummaryContractResultWithData"}}}}}}}, "/ContractGroup/list": {"get": {"tags": ["ContractGroup"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemIEnumerableContractResultWithData"}}}}}}}, "/ContractGroup/count": {"get": {"tags": ["ContractGroup"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/ContractGroup/{id}": {"get": {"tags": ["ContractGroup"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}, "delete": {"tags": ["ContractGroup"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemContractResultWithData"}}}}}}}, "/ContractGroup/get-info/{id}": {"get": {"tags": ["ContractGroup"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractGroupListItemContractResultWithData"}}}}}}}, "/ContractGroup/save": {"post": {"tags": ["ContractGroup"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ContractGroupSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractGroupSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractGroupSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ContractGroupSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/ContractHistory/list": {"get": {"tags": ["ContractHistory"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ContractNumber", "in": "query", "schema": {"type": "string"}}, {"name": "ContractNumberName", "in": "query", "schema": {"type": "string"}}, {"name": "ChangeTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ChangeUserName", "in": "query", "schema": {"type": "string"}}, {"name": "ChangeComment", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractHistoryViewModelIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractHistoryViewModelIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractHistoryViewModelIEnumerableContractResultWithData"}}}}}}}, "/ContractHistory/count": {"get": {"tags": ["ContractHistory"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ContractNumber", "in": "query", "schema": {"type": "string"}}, {"name": "ContractNumberName", "in": "query", "schema": {"type": "string"}}, {"name": "ChangeTime", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ChangeUserName", "in": "query", "schema": {"type": "string"}}, {"name": "ChangeComment", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/ContractHistory/{id}": {"get": {"tags": ["ContractHistory"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractHistoryViewModelContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractHistoryViewModelContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractHistoryViewModelContractResultWithData"}}}}}}}, "/File/update": {"put": {"tags": ["File"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SFileSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SFileSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SFileSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SFileSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/File/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SFileViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SFileViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SFileViewModel"}}}}}}, "delete": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeleteResponseResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeleteResponseResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteResponseResult"}}}}}}}, "/File/download/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/File/view/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/File/list": {"get": {"tags": ["File"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CreateUser", "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SFileViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SFileViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SFileViewModel"}}}}}}}}, "/File/count": {"get": {"tags": ["File"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CreateUser", "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SFileViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SFileViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SFileViewModel"}}}}}}}}, "/Invoice/list": {"get": {"tags": ["Invoice"], "parameters": [{"name": "InvoiceNo", "in": "query", "schema": {"type": "string"}}, {"name": "Applicant", "in": "query", "schema": {"type": "string"}}, {"name": "VoucherNo", "in": "query", "schema": {"type": "string"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "InvoiceType", "in": "query", "schema": {"$ref": "#/components/schemas/InvoiceType"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InvoiceListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InvoiceListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceListItemIEnumerableContractResultWithData"}}}}}}}, "/Invoice/count": {"get": {"tags": ["Invoice"], "parameters": [{"name": "InvoiceNo", "in": "query", "schema": {"type": "string"}}, {"name": "Applicant", "in": "query", "schema": {"type": "string"}}, {"name": "VoucherNo", "in": "query", "schema": {"type": "string"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "InvoiceType", "in": "query", "schema": {"$ref": "#/components/schemas/InvoiceType"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Invoice/{id}": {"get": {"tags": ["Invoice"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InvoiceViewModelContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InvoiceViewModelContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceViewModelContractResultWithData"}}}}}}, "delete": {"tags": ["Invoice"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageContractResultWithData"}}}}}}}, "/Invoice/save": {"post": {"tags": ["Invoice"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/InvoiceSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InvoiceSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InvoiceSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessageContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessageContractResultWithData"}}}}}}}, "/Project/list": {"get": {"tags": ["Project"], "parameters": [{"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Manager", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectListItemIEnumerableContractResultWithData"}}}}}}}, "/Project/count": {"get": {"tags": ["Project"], "parameters": [{"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Manager", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Project/{id}": {"get": {"tags": ["Project"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InvoiceViewModelContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InvoiceViewModelContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InvoiceViewModelContractResultWithData"}}}}}}, "delete": {"tags": ["Project"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/Project/save": {"post": {"tags": ["Project"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ProjectSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProjectSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Project/exist": {"get": {"tags": ["Project"], "parameters": [{"name": "Id", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Report/report-contract-pay": {"get": {"tags": ["Report"], "parameters": [{"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "ReportDateStar", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ReportDateEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "IsInRole", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrgName", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "DepartmentType", "in": "query", "schema": {"type": "string"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Report/report-contract-receive": {"get": {"tags": ["Report"], "parameters": [{"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "ReportDateStar", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ReportDateEnd", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ContractType", "in": "query", "schema": {"$ref": "#/components/schemas/ContractType"}}, {"name": "IsInRole", "in": "query", "schema": {"type": "boolean"}}, {"name": "OrgName", "in": "query", "schema": {"type": "string"}}, {"name": "OppositeName", "in": "query", "schema": {"type": "string"}}, {"name": "DepartmentType", "in": "query", "schema": {"type": "string"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Report/report-receive-info": {"get": {"tags": ["Report"], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Report/report-receive-pay": {"get": {"tags": ["Report"], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Supplier/list": {"get": {"tags": ["Supplier"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "BankName", "in": "query", "schema": {"type": "string"}}, {"name": "Address", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SupplierListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupplierListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupplierListItemIEnumerableContractResultWithData"}}}}}}}, "/Supplier/count": {"get": {"tags": ["Supplier"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Code", "in": "query", "schema": {"type": "string"}}, {"name": "BankName", "in": "query", "schema": {"type": "string"}}, {"name": "Address", "in": "query", "schema": {"type": "string"}}, {"name": "Phone", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON>g<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "UserName", "in": "query", "schema": {"type": "string"}}, {"name": "CreateBy", "in": "query", "schema": {"type": "string"}}, {"name": "IsAllData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsOrgData", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsDeleted", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Supplier/{id}": {"get": {"tags": ["Supplier"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SupplierViewModelContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupplierViewModelContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupplierViewModelContractResultWithData"}}}}}}, "delete": {"tags": ["Supplier"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Supplier/save": {"post": {"tags": ["Supplier"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/SupplierSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SupplierSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SupplierSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SupplierSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Workload/list": {"get": {"tags": ["Workload"], "parameters": [{"name": "ContractCode", "in": "query", "schema": {"type": "string"}}, {"name": "ContractName", "in": "query", "schema": {"type": "string"}}, {"name": "ProjectCode", "in": "query", "schema": {"type": "string"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkloadListItemIEnumerableContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkloadListItemIEnumerableContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkloadListItemIEnumerableContractResultWithData"}}}}}}}, "/Workload/count": {"get": {"tags": ["Workload"], "parameters": [{"name": "ContractCode", "in": "query", "schema": {"type": "string"}}, {"name": "ContractName", "in": "query", "schema": {"type": "string"}}, {"name": "ProjectCode", "in": "query", "schema": {"type": "string"}}, {"name": "ProjectName", "in": "query", "schema": {"type": "string"}}, {"name": "OrderNo", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32ContractResultWithData"}}}}}}}, "/Workload/{id}": {"get": {"tags": ["Workload"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkloadViewModelContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkloadViewModelContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkloadViewModelContractResultWithData"}}}}}}, "delete": {"tags": ["Workload"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}, "/Workload/save": {"post": {"tags": ["Workload"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/WorkloadSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkloadSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkloadSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkloadSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ContractResponseContractResultWithData"}}}}}}}}, "components": {"schemas": {"BookType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "ContractCode": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractCodeContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ContractCode"}}, "additionalProperties": false}, "ContractGroupListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "contractTotalIn": {"type": "number", "format": "double"}, "contractTotalOut": {"type": "number", "format": "double"}, "payTotalIn": {"type": "number", "format": "double"}, "payTotalOut": {"type": "number", "format": "double"}, "completeWorkloadIn": {"type": "number", "format": "double"}, "completeWorkloadOut": {"type": "number", "format": "double"}, "contractsIn": {"type": "array", "items": {"$ref": "#/components/schemas/ContractViewModel"}, "nullable": true}, "contractsOut": {"type": "array", "items": {"$ref": "#/components/schemas/ContractViewModel"}, "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractGroupListItemContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ContractGroupListItem"}}, "additionalProperties": false}, "ContractGroupListItemIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ContractGroupListItem"}, "nullable": true}}, "additionalProperties": false}, "ContractGroupSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractHistoryViewModel": {"type": "object", "properties": {"contractNumber": {"type": "string", "nullable": true}, "contractNumberName": {"type": "string", "nullable": true}, "contractType": {"$ref": "#/components/schemas/ContractType"}, "bookType": {"$ref": "#/components/schemas/BookType"}, "changeTime": {"type": "string", "format": "date-time"}, "changeUserName": {"type": "string", "nullable": true}, "changeComment": {"type": "string", "nullable": true}, "diff": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ContractHistoryViewModelContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ContractHistoryViewModel"}}, "additionalProperties": false}, "ContractHistoryViewModelIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ContractHistoryViewModel"}, "nullable": true}}, "additionalProperties": false}, "ContractListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contractType": {"$ref": "#/components/schemas/ContractType"}, "bookType": {"$ref": "#/components/schemas/BookType"}, "oppositeCode": {"type": "string", "nullable": true}, "oppositeName": {"type": "string", "nullable": true}, "planBookDate": {"type": "string", "format": "date-time"}, "bookDate": {"type": "string", "format": "date-time"}, "projectName": {"type": "string", "nullable": true}, "projectCode": {"type": "string", "nullable": true}, "serviceShip": {"type": "string", "nullable": true}, "contractPrice": {"type": "string", "nullable": true}, "contractPriceInfo": {"type": "string", "nullable": true}, "contractTotalInfo": {"type": "string", "nullable": true}, "contractTotal": {"type": "number", "format": "double"}, "applyDate": {"type": "string", "format": "date-time"}, "orderCloseDate": {"type": "string", "format": "date-time"}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "orgPath": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "startPayDate": {"type": "string", "format": "date-time"}, "workloadDate": {"type": "string", "format": "date-time"}, "payDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "orderNo": {"type": "string", "nullable": true}, "payTotal": {"type": "number", "format": "double"}, "requestTotal": {"type": "number", "format": "double"}, "payCount": {"type": "integer", "format": "int32"}, "lockInfo": {"type": "string", "nullable": true}, "workloadLockInfo": {"type": "string", "nullable": true}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/OrdersViewModel"}, "nullable": true}, "groupId": {"type": "integer", "format": "int32"}, "groupName": {"type": "string", "nullable": true}, "outline": {"type": "string", "nullable": true}, "serviceTerm": {"type": "string", "nullable": true}, "promiseServiceTerm": {"type": "string", "nullable": true}, "promiseDateType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractListItemIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ContractListItem"}, "nullable": true}}, "additionalProperties": false}, "ContractLockSubmit": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractOrder": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string", "nullable": true}, "orderType": {"$ref": "#/components/schemas/OrderType"}, "times": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "requestAmount": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "completeWorkload": {"type": "integer", "format": "int32"}, "requsetDate": {"type": "string", "format": "date-time"}, "payDate": {"type": "string", "format": "date-time"}, "invoicingTaxRate": {"type": "number", "format": "double"}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentInfo"}, "nullable": true}}, "additionalProperties": false}, "ContractOrderContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ContractOrder"}}, "additionalProperties": false}, "ContractOrderSubmit": {"type": "object", "properties": {"updateBy": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "linkCode": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contractType": {"$ref": "#/components/schemas/ContractType"}, "bookType": {"$ref": "#/components/schemas/BookType"}, "oppositeCode": {"type": "string", "nullable": true}, "oppositeName": {"type": "string", "nullable": true}, "planBookDate": {"type": "string", "format": "date-time"}, "projectName": {"type": "string", "nullable": true}, "projectCode": {"type": "string", "nullable": true}, "serviceTerm": {"type": "string", "nullable": true}, "promiseServiceTerm": {"type": "string", "nullable": true}, "promiseDateType": {"type": "string", "nullable": true}, "serviceContent": {"type": "string", "nullable": true}, "serviceShip": {"type": "string", "nullable": true}, "contractPrice": {"type": "string", "nullable": true}, "contractPriceInfo": {"type": "string", "nullable": true}, "contractTotalInfo": {"type": "string", "nullable": true}, "contractTotal": {"type": "number", "format": "double"}, "applyDate": {"type": "string", "format": "date-time"}, "orderCloseDate": {"type": "string", "format": "date-time"}, "orgPath": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractPaySubmit": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "supplierCode": {"type": "string", "nullable": true}, "orderUser": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "orderType": {"$ref": "#/components/schemas/OrderType"}, "applicationDate": {"type": "string", "format": "date-time"}, "payDateStart": {"type": "string", "format": "date-time"}, "payDateEnd": {"type": "string", "format": "date-time"}, "payCount": {"type": "integer", "format": "int32"}, "completeWorkload": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "startDate": {"type": "string", "format": "date-time"}, "startPayDate": {"type": "string", "format": "date-time"}, "orgUser": {"type": "string", "nullable": true}, "taxRate": {"type": "number", "format": "double"}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentInfo"}, "nullable": true}}, "additionalProperties": false}, "ContractReceiveSubmit": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "oldCode": {"type": "string", "nullable": true}, "projectCode": {"type": "string", "nullable": true}, "supplierCode": {"type": "string", "nullable": true}, "orderUser": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "orderType": {"$ref": "#/components/schemas/OrderType"}, "applicationDate": {"type": "string", "format": "date-time"}, "invoiceType": {"$ref": "#/components/schemas/InvoiceType"}, "payDateStart": {"type": "string", "format": "date-time"}, "payDateEnd": {"type": "string", "format": "date-time"}, "payCount": {"type": "integer", "format": "int32"}, "completeWorkload": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "remark": {"type": "string", "nullable": true}, "payContents": {"type": "string", "nullable": true}, "invoiceNo": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time"}, "taxRate": {"type": "number", "format": "double"}, "payAmount": {"type": "number", "format": "double"}, "payAmountDate": {"type": "string", "format": "date-time"}, "voucherNo": {"type": "string", "nullable": true}, "csRemark": {"type": "string", "nullable": true}, "zdRemark": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ContractResponseContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ContractResponse"}}, "additionalProperties": false}, "ContractSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contractType": {"$ref": "#/components/schemas/ContractType"}, "bookType": {"$ref": "#/components/schemas/BookType"}, "oppositeCode": {"type": "string", "nullable": true}, "oppositeName": {"type": "string", "nullable": true}, "planBookDate": {"type": "string", "format": "date-time"}, "bookDate": {"type": "string", "format": "date-time"}, "projectName": {"type": "string", "nullable": true}, "projectCode": {"type": "string", "nullable": true}, "serviceTerm": {"type": "string", "nullable": true}, "promiseServiceTerm": {"type": "string", "nullable": true}, "promiseDateType": {"type": "string", "nullable": true}, "serviceContent": {"type": "string", "nullable": true}, "serviceShip": {"type": "string", "nullable": true}, "contractPrice": {"type": "string", "nullable": true}, "contractPriceInfo": {"type": "string", "nullable": true}, "contractTotalInfo": {"type": "string", "nullable": true}, "contractTotal": {"type": "number", "format": "double"}, "applyDate": {"type": "string", "format": "date-time"}, "orderCloseDate": {"type": "string", "format": "date-time"}, "orgPath": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time"}, "startPayDate": {"type": "string", "format": "date-time"}, "workloadDate": {"type": "string", "format": "date-time"}, "payDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "orderNo": {"type": "string", "nullable": true}, "payTotal": {"type": "number", "format": "double"}, "requestTotal": {"type": "number", "format": "double"}, "payCount": {"type": "integer", "format": "int32"}, "lockInfo": {"type": "string", "nullable": true}, "workloadLockInfo": {"type": "string", "nullable": true}, "outline": {"type": "string", "nullable": true}, "taxRate": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ContractType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "ContractViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "contractType": {"$ref": "#/components/schemas/ContractType"}, "bookType": {"$ref": "#/components/schemas/BookType"}, "oppositeCode": {"type": "string", "nullable": true}, "oppositeName": {"type": "string", "nullable": true}, "planBookDate": {"type": "string", "format": "date-time"}, "bookDate": {"type": "string", "format": "date-time"}, "projectName": {"type": "string", "nullable": true}, "projectCode": {"type": "string", "nullable": true}, "serviceTerm": {"type": "string", "nullable": true}, "promiseServiceTerm": {"type": "string", "nullable": true}, "promiseDateType": {"type": "string", "nullable": true}, "serviceContent": {"type": "string", "nullable": true}, "serviceShip": {"type": "string", "nullable": true}, "contractPrice": {"type": "string", "nullable": true}, "contractPriceInfo": {"type": "string", "nullable": true}, "contractTotalInfo": {"type": "string", "nullable": true}, "contractTotal": {"type": "number", "format": "double"}, "applyDate": {"type": "string", "format": "date-time"}, "orderCloseDate": {"type": "string", "format": "date-time"}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "orgPath": {"type": "string", "nullable": true}, "startPayDate": {"type": "string", "format": "date-time"}, "workloadDate": {"type": "string", "format": "date-time"}, "payDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "orderNo": {"type": "string", "nullable": true}, "payTotal": {"type": "number", "format": "double"}, "requestTotal": {"type": "number", "format": "double"}, "payCount": {"type": "integer", "format": "int32"}, "lockInfo": {"type": "string", "nullable": true}, "workloadLockInfo": {"type": "string", "nullable": true}, "outline": {"type": "string", "nullable": true}, "orders": {"type": "array", "items": {"$ref": "#/components/schemas/OrdersViewModel"}, "nullable": true}}, "additionalProperties": false}, "ContractViewModelContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ContractViewModel"}}, "additionalProperties": false}, "DeleteResponseResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "model": {"$ref": "#/components/schemas/SFileViewModel"}}, "additionalProperties": false}, "GroupSubmit": {"type": "object", "properties": {"contractCode": {"type": "string", "nullable": true}, "contractGroupId": {"type": "integer", "format": "int32"}, "orderNo": {"type": "string", "nullable": true}, "orderUser": {"type": "string", "nullable": true}, "orderDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Int32ContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32UpdateResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Int32UpdateResultContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/Int32UpdateResult"}}, "additionalProperties": false}, "InvoiceListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "invoiceNo": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time"}, "applicant": {"type": "string", "nullable": true}, "applicationDate": {"type": "string", "format": "date-time"}, "taxRate": {"type": "number", "format": "double"}, "voucherNo": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "payAmount": {"type": "number", "format": "double"}, "payDate": {"type": "string", "format": "date-time"}, "orderNo": {"type": "string", "nullable": true}, "payContents": {"type": "string", "nullable": true}, "contractCode": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "invoiceType": {"$ref": "#/components/schemas/InvoiceType"}}, "additionalProperties": false}, "InvoiceListItemIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/InvoiceListItem"}, "nullable": true}}, "additionalProperties": false}, "InvoiceSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "invoiceNo": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time"}, "applicant": {"type": "string", "nullable": true}, "applicationDate": {"type": "string", "format": "date-time"}, "taxRate": {"type": "number", "format": "double"}, "voucherNo": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "payAmount": {"type": "number", "format": "double"}, "payDate": {"type": "string", "format": "date-time"}, "orderNo": {"type": "string", "nullable": true}, "contractCode": {"type": "string", "nullable": true}, "payContents": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "invoiceType": {"$ref": "#/components/schemas/InvoiceType"}}, "additionalProperties": false}, "InvoiceType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "InvoiceViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "invoiceNo": {"type": "string", "nullable": true}, "invoiceDate": {"type": "string", "format": "date-time"}, "applicant": {"type": "string", "nullable": true}, "applicationDate": {"type": "string", "format": "date-time"}, "taxRate": {"type": "number", "format": "double"}, "voucherNo": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "payAmount": {"type": "number", "format": "double"}, "payDate": {"type": "string", "format": "date-time"}, "orderNo": {"type": "string", "nullable": true}, "contractCode": {"type": "string", "nullable": true}, "payContents": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}, "invoiceType": {"$ref": "#/components/schemas/InvoiceType"}}, "additionalProperties": false}, "InvoiceViewModelContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/InvoiceViewModel"}}, "additionalProperties": false}, "OrderSummary": {"type": "object", "properties": {"lastAmount": {"type": "number", "format": "double"}, "lastUniPrice": {"type": "number", "format": "double"}, "lastPayDate": {"type": "string", "nullable": true}, "requestAmountTotal": {"type": "number", "format": "double"}, "lastCompleteWorkload": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "OrderSummaryContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/OrderSummary"}}, "additionalProperties": false}, "OrderType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "OrdersViewModel": {"type": "object", "properties": {"orderNo": {"type": "string", "nullable": true}, "orderType": {"$ref": "#/components/schemas/OrderType"}, "amount": {"type": "number", "format": "double"}, "unitPrice": {"type": "number", "format": "double"}, "completeWorkload": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PaymentInfo": {"type": "object", "properties": {"payAmount": {"type": "number", "format": "double"}, "voucherNo": {"type": "string", "nullable": true}, "payAmountDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProjectListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "manager": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "orgPath": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectListItemIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectListItem"}, "nullable": true}}, "additionalProperties": false}, "ProjectSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "manager": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResponseMessage": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResponseMessageContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ResponseMessage"}}, "additionalProperties": false}, "SFileStats": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "SFileSubmit": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "refId": {"type": "string", "nullable": true}, "system": {"type": "string", "nullable": true}, "createUser": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "height": {"type": "integer", "format": "int32", "nullable": true}, "comment": {"type": "string", "nullable": true}, "expireMinutes": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "SFileViewModel": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/SFileStats"}, "refId": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "createUser": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "comment": {"type": "string", "nullable": true}, "storePath": {"type": "string", "nullable": true}, "system": {"type": "string", "nullable": true}, "expireTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SupplierListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "num": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "orgPath": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SupplierListItemIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SupplierListItem"}, "nullable": true}}, "additionalProperties": false}, "SupplierSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "num": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SupplierViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "num": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "orgPath": {"type": "string", "nullable": true}, "isDeleted": {"type": "boolean"}, "createTime": {"type": "string", "format": "date-time"}, "createBy": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "updateBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SupplierViewModelContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/SupplierViewModel"}}, "additionalProperties": false}, "WorkloadListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "unitPrice": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "contractId": {"type": "integer", "format": "int32"}, "contractCode": {"type": "string", "nullable": true}, "contractName": {"type": "string", "nullable": true}, "projectId": {"type": "integer", "format": "int32"}, "projectCode": {"type": "string", "nullable": true}, "projectName": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "completeWorkload": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkloadListItemIEnumerableContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/WorkloadListItem"}, "nullable": true}}, "additionalProperties": false}, "WorkloadSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "unitPrice": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "contractCode": {"type": "string", "nullable": true}, "projectCode": {"type": "string", "nullable": true}, "updateUser": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "completeWorkload": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkloadViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "startDate": {"type": "string", "format": "date-time"}, "endDate": {"type": "string", "format": "date-time"}, "unitPrice": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "contractId": {"type": "integer", "format": "int32"}, "contractCode": {"type": "string", "nullable": true}, "contractName": {"type": "string", "nullable": true}, "projectId": {"type": "integer", "format": "int32"}, "projectCode": {"type": "string", "nullable": true}, "projectName": {"type": "string", "nullable": true}, "orderNo": {"type": "string", "nullable": true}, "completeWorkload": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "WorkloadViewModelContractResultWithData": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/WorkloadViewModel"}}, "additionalProperties": false}}}}