<script setup lang="ts">
import type { InstanceListItemViewModel } from '@coder/swf-api';
import type { Dayjs } from 'dayjs';

import { computed, onMounted, reactive, ref, useSlots, watch } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import { formatDateText } from '@coder/swf-api';
import { WorkProcessNameSelector } from '@coder/swf-designer';
import {
  Button as AButton,
  Col as ACol,
  DatePicker as ADatePicker,
  RangePicker as ARangePicker,
  Row as ARow,
  Switch as ASwitch,
  Card,
  Form,
  FormItem,
  Input,
  Table,
} from 'ant-design-vue';

import Status from '../list/_status.vue';
import { getColumns } from '../list/columns'; // TODO:导入column
import useList from './useList';

const props = defineProps<{
  status: number;
  tags?: Array<string>;
  workProcessName?: string;
}>();

const emits = defineEmits<{
  (
    e: 'debug' | 'detail' | 'dispose' | 'edit',
    pi: InstanceListItemViewModel,
  ): void;
}>();
const slots = useSlots();
const {
  dataSource,
  loading,
  pager,
  reload,
  searcherForm,
  onSearch,
  onPagerChanged,
} = useList(props, emits);

// const showWpNameSearcher = computed(() => props.workProcessName == null); // 提交不上代码
const showWpNameSearcher = computed(() => !props.workProcessName);
const workProcessName = computed(() => props.workProcessName);

const columnDefine = getColumns(workProcessName.value, emits, reload, slots);
const columns = reactive(columnDefine);

watch(
  () => props.workProcessName,
  (v) => {
    if (v) {
      if (!searcherForm.name) return;
      searcherForm.name.splice(0);
      searcherForm.name.push(v);
    }
    reload();
  },
);

watch(
  () => props.tags,
  (v) => {
    if (v) {
      if (!searcherForm.tags) return;
      searcherForm.tags.splice(0);
      searcherForm.tags.push(...v);
    }
  },
);
type RangeValue = [Dayjs, Dayjs];
const createTimetemp = ref<RangeValue>();

const createTimeChange = (val: any) => {
  searcherForm.createTimeStart = '';
  searcherForm.createTimeEnd = '';
  if (val) {
    searcherForm.createTimeStart = formatDateText(val[0]);
    searcherForm.createTimeEnd = formatDateText(val[1]);
  }
};

const year = ref(undefined);

const yearChange = (val: any) => {
  searcherForm.createTimeStart = '';
  searcherForm.createTimeEnd = '';
  if (val) {
    searcherForm.createTimeStart = formatDateText(`${val.year()}-01-01`);
    searcherForm.createTimeEnd = formatDateText(`${val.year()}-12-31`);
  }
};

onMounted(() => {
  if (workProcessName.value && searcherForm.name) {
    searcherForm.name.splice(0);
    searcherForm.name.push(workProcessName.value);
  }
  if (searcherForm.tags) {
    searcherForm.tags.splice(0);
    searcherForm.tags.push(...(props.tags ?? []));
  }

  reload();
});

defineExpose({
  reload,
});
</script>

<template>
  <Card :bordered="false">
    <template #extra>
      <Form layout="inline">
        <FormItem label="显示我管理的">
          <ASwitch v-model:checked="searcherForm.ownManage" />
        </FormItem>
      </Form>
    </template>
    <Form :label-col="{ span: 6 }">
      <ARow :gutter="16">
        <ACol :span="6">
          <FormItem label="工单号">
            <Input v-model:value="searcherForm.number" placeholder="工单号" />
          </FormItem>
        </ACol>
        <ACol :span="6" v-if="showWpNameSearcher">
          <FormItem label="流程名称">
            <WorkProcessNameSelector v-model="searcherForm.name" />
          </FormItem>
        </ACol>
        <ACol :span="6">
          <FormItem label="状态">
            <Status v-model="searcherForm.status" />
          </FormItem>
        </ACol>

        <ACol :span="6">
          <FormItem label="申请人">
            <Input v-model:value="searcherForm.creator" placeholder="申请人" />
          </FormItem>
        </ACol>
        <ACol :span="6">
          <FormItem label="主题">
            <Input v-model:value="searcherForm.subject" placeholder="主题" />
          </FormItem>
        </ACol>
        <ACol :span="6">
          <FormItem label="年度">
            <ADatePicker
              v-model:value="year"
              picker="year"
              style="width: 100%"
              @change="yearChange"
            />
          </FormItem>
        </ACol>
        <ACol :span="6">
          <FormItem label="创建时间">
            <ARangePicker
              v-model:value="createTimetemp"
              style="width: 100%"
              @change="createTimeChange"
            />
          </FormItem>
        </ACol>
        <slot :search-model="searcherForm" name="filter"></slot>
      </ARow>

      <ARow>
        <ACol :span="3">
          <FormItem>
            <AButton type="primary" @click="onSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </AButton>
          </FormItem>
        </ACol>
        <ACol :span="21">
          <!-- 、红色代表：需要处理 -->
          <div class="reminder">
            工单号绿色代表：工单已归档、黄色代表：审核中
          </div>
        </ACol>
      </ARow>
    </Form>
  </Card>
  <Card style="margin-top: 16px" :bordered="false">
    <Table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pager"
      row-key="id"
      @change="onPagerChanged"
    />
  </Card>
</template>

<style scoped>
.reminder {
  margin-top: 10px;
  color: #fd8d2c;
  text-align: right;
}
</style>
