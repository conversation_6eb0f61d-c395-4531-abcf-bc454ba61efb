import type { ContractHistoryViewModel } from '@hai-an/contract-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import { dayF } from '../../util';

export const makeColumns = (emits: any) => {
  return [
    {
      dataIndex: 'contractNumber',
      title: '合同编码',
      width: 200,
    },
    {
      dataIndex: 'contractNumberName',
      title: '合同名称',
      customRender: ({ record }) => {
        const historyViewModel = record as ContractHistoryViewModel;

        return (
          <a onClick={() => emits('toInfo', historyViewModel)}>
            {historyViewModel.contractNumberName}
          </a>
        );
      },
    },

    {
      dataIndex: 'changeUserName',
      title: '处理账号',
    },
    {
      dataIndex: 'changeComment',
      title: '操作内容',
    },
    {
      customRender: ({ text }) => {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'changeTime',
      title: '处理时间',
    },
    {
      customRender: ({ record }) => {
        const historyViewModel = record as ContractHistoryViewModel;
        return (
          <ul>
            {historyViewModel.diff.map((item: any) => {
              return <li>{item}</li>;
            })}
          </ul>
        );
      },
      dataIndex: 'diff',
      title: '改变',
    },
  ] as ColumnType[];
};
