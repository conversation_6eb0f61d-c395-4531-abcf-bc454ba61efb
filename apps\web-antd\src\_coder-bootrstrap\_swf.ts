import type { SwfOption } from '@coder/swf-render';

import { useAccessStore } from '@vben/stores';

import swfRender from '@coder/swf-render';

import { requestClient } from '#/api/request';

const install = (app: any) => {
  const option = {
    host: 'swf',
    // host: 'http://localhost:13200',
    request: requestClient,
    getToken() {
      return useAccessStore().accessToken;
    },
    previewHost: 'http://*************:8080/api/filePreview',
  } as SwfOption;
  app.use(swfRender, option);
};

export default install;
