import { useAccessStore } from '@vben/stores';

import coderDocments from '@hai-an/document-ui';

import { requestClient } from '#/api/request';

const install = (app: any) => {
  app.use(coderDocments, {
    path: '/documents',
    // path: 'http://localhost:5100',
    request: requestClient,
    // previewHost: 'http://localhost:801/api/filePreview',
    previewHost: 'http://*************:8080/api/filePreview',
    getPreviewFileHost: 'http://gateway/documents/file',
    getToken() {
      const store = useAccessStore();
      return `Bearer ${store.accessToken}`;
    },
  });
};
export default install;
