stages:
    - build-npm
    - build-docker-image
    - deploy

build-src:
    stage: build-npm
    image: zhcoder-docker-registry.com:8000/builder/node-builder:v22
    script:
        - 'npm install pnpm -g'
        - 'pnpm install'
        - 'pnpm build:antd' 
    tags:
        - docker
    artifacts:
        paths:
          - scripts/
          - apps/web-antd/dist/
          - package.json
        expire_in : 1 day

docker:
    stage: build-docker-image
    script:
       - sudo chmod +x ./scripts/deploy/build-antd.sh
       - sudo ./scripts/deploy/build-antd.sh
    tags:
        - shell

deployTestServer:
    stage: deploy
    image: zhcoder-docker-registry.com:8000/library/alpine:3.18
    before_script:
      - 'command -v sshpass >/dev/null || (apt-get update -y && apt-get install -y sshpass)'
    script:
      - |
        set +x  # 避免输出密码
        sshpass -p "${oa2_server_password}" \
        ssh -tt -o StrictHostKeyChecking=no ${oa2_server_user}@${oa2_server} "cd ~/oa_deploy_docker_v2 && docker pull zhcoder-docker-registry.com:8000/coder/hai-an-oa:2.0 &&./start.sh --debug"
    tags:
        - docker
