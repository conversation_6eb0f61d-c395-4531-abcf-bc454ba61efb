<script lang="ts" setup>
import type { MessageViewModel } from '@coder/notify-api';

import { ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { NotifyMessageDetail } from '@coder/notify-ui';

const { setTabTitle } = useTabs();
const router = useRoute();
const id = Number.parseInt(`${router.params.id}`);
const title = ref('');
const onLoaded = (message: MessageViewModel) => {
  title.value = `${(message as any).title}`;
  try {
    setTabTitle(`${title.value}`);
  } catch (error) {
    console.error(error);
  }
};
</script>
<template>
  <Page description="消息" :title="title">
    <!-- com:{
meta:{
 title :'page.myMessage.content',
 hideInMenu:true,
},
name:'CoderNotifyDetail',
path:'/notify/detail/:id'
    } -->
    <NotifyMessageDetail :message-id="id" @loaded="onLoaded" />
  </Page>
</template>
