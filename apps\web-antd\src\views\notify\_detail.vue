<script lang="ts" setup>
import type { MessageViewModel } from '@coder/notify-api';

import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';

import { NotifyMessageDetail } from '@coder/notify-ui';

const { setTabTitle } = useTabs();
const router = useRoute();
const id = Number.parseInt(`${router.params.id}`);
const title = ref('');
const onLoaded = (message: MessageViewModel) => {
  title.value = `${(message as any).title}`;
  try {
    setTabTitle(`${title.value}`);
  } catch (error) {
    console.error(error);
  }
};
const routerL = useRouter();
const handlePreviewFile = (url: string) => {
  routerL.push({
    name: 'PreviewDocument',
    query: {
      url,
    },
  });
};
</script>
<template>
  <Page :title="title">
    <!-- com:{
meta:{
 title :'page.myMessage.content',
 hideInMenu:true,
},
name:'CoderNotifyDetail',
path:'/notify/detail/:id'
    } -->
    <NotifyMessageDetail
      :message-id="id"
      @loaded="onLoaded"
      @preview-file="handlePreviewFile"
    />
  </Page>
</template>
