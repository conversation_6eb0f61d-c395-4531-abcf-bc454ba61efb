<script lang="ts" setup>
import type {
  InstanceListItemViewModel,
  ProcessInstanceSearcher,
} from '@coder/swf-api';

import type { RouteLocationRaw } from 'vue-router';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { MyOrder } from '@coder/swf-ui';
import { OrgSelector } from '@coder/system-ui';
import { Col, FormItem } from 'ant-design-vue';

const route = useRouter();
const onDispose = (wa: InstanceListItemViewModel) => {
  const routeInfo = {
    name: 'SwfWorkActivityDispose',
    params: {
      id: wa.workActivityId,
    },
  };

  route.push(routeInfo);
};

const swfRef = ref();
const workProcessName = ref(null);
const onDebug = (instance: InstanceListItemViewModel) => {
  const routerInfo = {
    name: 'SwfEditor',
    params: {
      id: instance.workProcessId,
    },
    query: {
      processInstanceId: instance.workProcessId,
    },
  } as RouteLocationRaw;
  route.push(routerInfo);
};
/**
 * 导航到编辑页面，直接更改formData
 * @param instance 导航
 */
const onEdit = (instance: InstanceListItemViewModel) => {
  const routerInfo = {
    name: 'SwfProcessInstanceEditor',
    params: {
      id: instance.workProcessId,
    },
    query: {
      workProcessId: instance.workProcessId,
    },
  } as RouteLocationRaw;
  route.push(routerInfo);
};
const onDetail = (instance: InstanceListItemViewModel) => {
  const routerInfo = {
    name: 'SwfProcessInstanceDetail',
    params: {
      id: instance.id,
    },
    query: {
      workProcessId: instance.workProcessId,
    },
  } as RouteLocationRaw;
  route.push(routerInfo);
};

const onOrgChange = (
  searcherForm: ProcessInstanceSearcher,
  orgValue: string,
) => {
  if (!searcherForm.tags) searcherForm.tags = [];
  const index = searcherForm.tags.findIndex((a) => a.startsWith('org:'));
  if (index === -1) {
    searcherForm.tags.push(`org:${orgValue}`);
  } else {
    searcherForm.tags[index] = `org:${orgValue}`;
  }
};
</script>
<template>
  <Page description="我的工作" title="">
    <!-- com:{
     meta:{
      title:'我的工作'
     },
    path:"/my-works",
    name:"SwfMyWorks"
}
-->
    <MyOrder
      ref="swfRef"
      :work-process-name="workProcessName"
      @dispose="onDispose"
      @debug="onDebug"
      @edit="onEdit"
      @detail="onDetail"
    >
      <template #filter="{ searchModel }">
        <Col :span="6">
          <FormItem label="部门/分公司">
            <OrgSelector
              @change="(value: string) => onOrgChange(searchModel, value)"
              placeholder="请选择组织架构"
            />
          </FormItem>
        </Col>
      </template>
    </MyOrder>
  </Page>
</template>
