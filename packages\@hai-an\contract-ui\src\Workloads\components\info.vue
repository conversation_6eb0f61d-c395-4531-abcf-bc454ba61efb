<script setup lang="ts">
import { onMounted, reactive } from 'vue';

import { CheckOutlined } from '@ant-design/icons-vue';
import {
  createWorkloadApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as <PERSON>utton,
  Col as ACol,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Row as ARow,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: 0, type: Number },
});

const emit = defineEmits(['doCancel']);

const api = createWorkloadApi(options.request, options.path);

const submitForm = reactive({
  address: '',
  id: 0,
  name: '',
  owner: '',
  ownerPhone: '',
  remark: '',
});
const dayF = (val) => {
  return dayjs(val).format('YYYY-MM-DD HH:mm');
};
const doCancel = () => {
  emit('doCancel');
};
const realod = () => {
  if (props.id) {
    api.Get(props.id).then((res) => {
      Object.assign(submitForm, res.data);
    });
  }
};
onMounted(() => {
  realod();
});
</script>
<template>
  <div style="padding: 10px; background-color: #ececec">
    <ADescriptions bordered size="small">
      <ADescriptionsItem label="合同编号">
        {{ submitForm.contractCode }}
      </ADescriptionsItem>
      <ADescriptionsItem label="项目编号">
        {{ submitForm.projectCode }}
      </ADescriptionsItem>
      <ADescriptionsItem label="开始日期">
        {{ dayF(submitForm.startDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="合同名称">
        {{ submitForm.contractName }}
      </ADescriptionsItem>
      <ADescriptionsItem label="项目名称">
        {{ submitForm.projectName }}
      </ADescriptionsItem>
      <ADescriptionsItem label="结束日期">
        {{ dayF(submitForm.endDate) }}
      </ADescriptionsItem>
      <ADescriptionsItem label="所属工单">
        {{ submitForm.orderNo }}
      </ADescriptionsItem>
      <ADescriptionsItem label="工作量">
        {{ submitForm.completeWorkload }}
      </ADescriptionsItem>
      <ADescriptionsItem label="单价">
        {{ submitForm.unitPrice }}
      </ADescriptionsItem>
      <ADescriptionsItem label="总价">
        {{ submitForm.amount }}
      </ADescriptionsItem>
    </ADescriptions>
  </div>
  <div style="padding: 20px; background-color: #ececec">
    <ARow type="flex" justify="center">
      <ACol :span="3">
        <AButton type="primary" @click="doCancel">
          <CheckOutlined />确定
        </AButton>
      </ACol>
    </ARow>
  </div>
</template>
