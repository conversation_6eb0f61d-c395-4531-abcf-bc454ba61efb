<script setup lang="ts">
import { computed, onUnmounted, ref, watch } from 'vue';

interface Props {
  height?: number | string;
  pdfBlob?: Blob | null; // New prop for Blob data
  pdfUrl?: string; // Made pdfUrl optional as blob might be primary
  width?: number | string;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '600px',
  pdfUrl: '',
  pdfBlob: null,
});

const objectUrl = ref<null | string>(null);

const formatSize = (size: number | string): string => {
  if (typeof size === 'number') {
    return `${size}px`;
  }
  return size;
};

const containerStyle = computed(() => ({
  width: formatSize(props.width),
  height: formatSize(props.height),
  overflow: 'hidden',
  border: '1px solid #ccc',
}));

const iframeStyle = computed(() => ({
  width: '100%',
  height: '100%',
  border: 'none',
}));

watch(
  () => props.pdfBlob,
  (newBlob, _) => {
    if (objectUrl.value) {
      URL.revokeObjectURL(objectUrl.value);
      objectUrl.value = null;
    }
    if (newBlob instanceof Blob) {
      objectUrl.value = URL.createObjectURL(newBlob);
    }
  },
  { immediate: true }, // Create URL immediately if blob is present on mount
);

const effectiveSrc = computed(() => {
  if (objectUrl.value) {
    return objectUrl.value;
  }
  if (props.pdfUrl) {
    return props.pdfUrl;
  }
  return null; // No source available
});

onUnmounted(() => {
  if (objectUrl.value) {
    URL.revokeObjectURL(objectUrl.value);
    objectUrl.value = null;
  }
});
</script>

<template>
  <div class="pdf-viewer-container" :style="containerStyle">
    <iframe
      v-if="effectiveSrc"
      :src="effectiveSrc"
      :style="iframeStyle"
      frameborder="0"
      aria-label="PDF Viewer"
    >
      <p>您的浏览器不支持 iframe，请升级浏览器或使用其他方式查看 PDF。</p>
    </iframe>
    <div v-else class="no-pdf">
      <p>未提供 PDF 文件地址或 Blob 数据。</p>
    </div>
  </div>
</template>

<style scoped>
.pdf-viewer-container {
  box-sizing: border-box;

  /* background-color: #f0f0f0; Optional: background for the container */
}

.no-pdf {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #777;
  background-color: #f9f9f9;
  border: 1px dashed #ccc; /* Visual cue when no PDF is loaded */
}

.no-pdf p {
  padding: 20px;
  text-align: center;
}
</style>
