<script setup lang="ts">
import { ref } from 'vue';

import { Pane, Splitpanes } from 'splitpanes';

import FileTable from './components/FileTable/FileTable.vue';
import TreeFolder from './components/TreeFolder/TreeFolder.vue';

import 'splitpanes/dist/splitpanes.css';

const emit = defineEmits(['previewFile']);
const folderId = ref(0);
const handleSelect = (id: number | string) => {
  folderId.value = typeof id === 'string' ? Number(id) : id;
};
const handlePreviewFile = (url: string) => {
  emit('previewFile', url);
};
</script>

<template>
  <div class="file-management-panel">
    <Splitpanes>
      <Pane :size="25">
        <div class="tree-container">
          <TreeFolder @select="handleSelect" :selected-id="folderId" />
        </div>
      </Pane>
      <Pane :size="75">
        <FileTable
          :folder-id="folderId"
          @preview-file="handlePreviewFile"
          @select-node="handleSelect"
        />
      </Pane>
    </Splitpanes>
  </div>
</template>

<style scoped>
.file-management-panel {
  width: 100%;
  height: 100%;
}

.tree-container {
  height: 100%;
  padding: 12px;
  overflow: auto;
}

:deep(.splitpanes) {
  height: 100%;
}

:deep(.splitpanes__pane) {
  margin: 4px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 0 5px rgb(0 0 0 / 10%);
}

:deep(.splitpanes__splitter) {
  position: relative;
  margin: 4px 0;
  background-color: #f0f2f5;
}

:deep(.splitpanes__splitter)::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 30px;
  content: '';
  background-color: #e8e8e8;
  border-radius: 2px;
  transform: translate(-50%, -50%);
}

:deep(.splitpanes__splitter):hover::before {
  background-color: #1890ff;
}
</style>
