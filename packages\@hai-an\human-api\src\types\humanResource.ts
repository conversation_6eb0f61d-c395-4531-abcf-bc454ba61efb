export interface HumanResourceListItem {
  bingJiaDays: number;
  chanJiaDays: number;
  chuChaiDays: number;
  createBy: null | string;
  createTime: null | string;
  delFlag: null | string;
  gongShangJiaDays: number;
  gongXiuRiJiaBanDays: number;
  huLiJiaDays: number;
  hunJiaDays: number;
  id: string;
  jieJiaRiJiaBanDays: number;
  name: string;
  nianXiuJiaDays: number;
  peiChanJiaDays: number;
  sangJiaDays: number;
  shiJiaDays: number;
  type: string;
  updateBy: null | string;
  updateTime: null | string;
  userId: string;
  userName: string;
  yearMonth: string;
  yuErJiaDays: number;
}

export class HumanResourceSearcher {
  name: string = '';
  page: number = 0;
  pageSize: number = 10;
  type: string = '1';
  yearMonth: string = '';
}

export interface ResponseMessage<T = any> {
  data: {
    rows: T[];
    total: number;
  };
  message: string;
  success: boolean;
}

export interface HumanResourceLogItem {
  delFlag: string;
  humanResourceDate: string;
  humanResourceId: string;
  humanResourceType: number;
  humanResourceuration: number;
  id: string;
  orderId: number;
  orderNo: string;
}
