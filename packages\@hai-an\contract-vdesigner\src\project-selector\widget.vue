<script lang="ts" setup>
import type { WidgetPropsType } from '@coder/vdesigner-core';

import type { ContractGroupViewModel } from '../../../contract-api/src';
import type { ProjectSelectOptions } from './_options';

import { computed } from 'vue';

import {
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { ProjectSelectorById as ProjectSelector } from '@hai-an/contract-ui';

const props = defineProps<WidgetPropsType>();
const { getComponent } = useWidgetRegistry();
const { widget, value, callJsCode } = useWidget(props.widget, props.renderId);
const renderStore = useRenderStore(props.renderId);
const options = widget.value.options as ProjectSelectOptions;
const CoderVDesignFormItem = getComponent(
  'CoderVDesignFormItem',
  renderStore.implement,
);
const style = computed(() => {
  return {
    display: options.hidden ? 'none' : 'block',
  };
});
const onChange = (val: ContractGroupViewModel) => {
  callJsCode(options.changeEvent, {
    project: val,
  });
};
</script>
<template>
  <CoderVDesignFormItem v-bind="props">
    <ProjectSelector v-model="value" :style="style" @change="onChange" />
  </CoderVDesignFormItem>
</template>
