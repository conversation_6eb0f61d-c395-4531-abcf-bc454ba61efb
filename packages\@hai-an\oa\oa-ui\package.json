{"name": "@hai-an/oa-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "main": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/system-api": "workspace:*", "@coder/system-ui": "workspace:*", "@coder/vdesigner-core": "workspace:*", "@hai-an/oa-api": "workspace:*", "@hai-an/human-api": "workspace:*", "@coder/swf-api": "workspace:^", "@coder/swf-render": "workspace:^", "@morev/vue-transitions": "catalog:coder", "@vben/locales": "workspace:*", "@vben/request": "workspace:*", "@vben/hooks": "workspace:*", "@vben/utils": "workspace:^", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash-es": "catalog:coder", "lru-cache": "catalog:coder", "vue": "catalog:", "vue-clipboard3": "catalog:coder", "vue-draggable-plus": "catalog:coder"}, "devDependencies": {"@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}