<script lang="ts" setup>
import { reactive } from 'vue';

import { SearchOutlined } from '@ant-design/icons-vue';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
} from 'ant-design-vue';

const emit = defineEmits(['onSearch']);
const searchForm = reactive({
  ContractId: null,
  WorkOrderNo: '',
  ContractName: '',
  ContractCode: '',
});

const doSearch = () => {
  emit('onSearch', searchForm);
};
</script>
<template>
  <div class="space-align-container">
    <AForm layout="inline" :model="searchForm">
      <AFormItem label="合同编码">
        <AInput v-model:value="searchForm.ContractCode" />
      </AFormItem>
      <AFormItem label="合同名称">
        <AInput v-model:value="searchForm.ContractName" />
      </AFormItem>
      <AFormItem label="工单编码">
        <AInput v-model:value="searchForm.WorkOrderNo" />
      </AFormItem>

      <AFormItem>
        <AButton type="primary" @click="doSearch">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </div>
</template>
<style scoped>
.space-align-container {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
