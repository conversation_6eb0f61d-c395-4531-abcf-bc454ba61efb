<script setup lang="ts">
import type {
  ContractGroupSearch,
  ContractGroupViewModel,
} from '@hai-an/contract-api/src/types/group';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  createGroupApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import { Select as ASelect } from 'ant-design-vue';

const props = defineProps({
  modelValue: { default: () => '', type: String },
});
const emit = defineEmits<{
  (
    e: 'change' | 'update:modelValue',
    val: ContractGroupViewModel | undefined,
  ): void;
  (e: 'update:modelValue', val: string): void;
}>();

const api = createGroupApi(options.request, options.path);

const loading = ref(false);
const searchForm = reactive<ContractGroupSearch>({
  page: 1,
  pageSize: 50,
});

const contractGroupInfo = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});
const datas = reactive<
  {
    contractGroup: ContractGroupViewModel;
    label: string;
    value: number;
  }[]
>([]);

const handleChange = (val: any) => {
  const result = datas.find((_) => _.value === val);

  emit('change', result?.contractGroup);
};
const toSearch = () => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    const contractGroups: any[] = [];

    res.forEach((element) => {
      contractGroups.push({
        contractGroup: element,
        label: element.name,
        value: element.id,
      });
    });
    datas.splice(0);
    datas.push(...contractGroups);
    loading.value = false;
  });
};
const handleSearch = (val: any) => {
  searchForm.code = val;
  toSearch();
};
onMounted(() => {
  toSearch();
});
</script>
<template>
  <ASelect
    v-model:value="contractGroupInfo"
    show-search
    placeholder="请输入合同组名称"
    style="width: 300px"
    :default-active-first-option="false"
    :loading="loading"
    :show-arrow="false"
    :filter-option="false"
    :not-found-content="null"
    :options="datas"
    @change="handleChange"
    @search="handleSearch"
  />
</template>
