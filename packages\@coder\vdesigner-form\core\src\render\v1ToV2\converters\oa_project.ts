import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

export class ProjectSelectConvert extends V1Convert {
  constructor() {
    super('select-project', 'select-project');
  }
  override SetOption(v1: any, v2: Widget, _cfg: Record<string, any>): void {
    const v1Option = v1.options;
    v2.options = {
      name: v1Option.name,
      label: v1Option.label,
      hidden: v1Option.hidden,
    };
  }
}
