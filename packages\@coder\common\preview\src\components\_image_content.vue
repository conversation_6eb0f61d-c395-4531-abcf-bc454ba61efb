<script setup lang="ts">
import { onUnmounted, ref } from 'vue';

interface Props {
  alt?: string;
  blob?: Blob | undefined;
  fileName?: string;
  headers?: Record<string, string>; // 保持兼容性，但实际不使用
  src: string; // 现在直接接收 blob URL
}

const props = withDefaults(defineProps<Props>(), {
  alt: '',
  fileName: 'image',
  headers: () => ({}),
  blob: undefined,
});

// Image transformation states
const scale = ref<number>(1);
const rotation = ref<number>(0);

// For drag functionality
const dragging = ref<boolean>(false);
const startX = ref<number>(0);
const startY = ref<number>(0);
const translateX = ref<number>(0);
const translateY = ref<number>(0);

// Zoom functionality
const handleWheel = (e: WheelEvent): void => {
  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.1, Math.min(5, scale.value + delta));
  scale.value = newScale;
};

// Rotation functionality
const rotateLeft = (): void => {
  rotation.value = (rotation.value - 90) % 360;
};

const rotateRight = (): void => {
  rotation.value = (rotation.value + 90) % 360;
};

// Reset/fit to window functionality
const resetImage = (): void => {
  scale.value = 1;
  rotation.value = 0;
  translateX.value = 0;
  translateY.value = 0;
};

// Download functionality
const downloadImage = async (): Promise<void> => {
  if (!props.src) return;

  const link = document.createElement('a');
  link.href = props.src;
  link.download = props.fileName || 'image';
  document.body.append(link);
  link.click();
  link.remove();
};

// Drag functionality
const startDrag = (e: MouseEvent): void => {
  e.preventDefault();
  dragging.value = true;
  startX.value = e.clientX - translateX.value;
  startY.value = e.clientY - translateY.value;

  // 添加全局事件监听器
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
};

const onDrag = (e: MouseEvent): void => {
  if (dragging.value) {
    translateX.value = e.clientX - startX.value;
    translateY.value = e.clientY - startY.value;
  }
};

const stopDrag = (): void => {
  dragging.value = false;
  // 移除全局事件监听器
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
});
</script>

<template>
  <div class="image-preview-container">
    <div class="image-content">
      <div class="image-wrapper" @wheel.prevent="handleWheel">
        <img
          :src="props.src"
          :style="{
            transform: `translate(${translateX}px, ${translateY}px) rotate(${rotation}deg) scale(${scale})`,
            transition: dragging ? 'none' : 'transform 0.3s',
          }"
          @mousedown="startDrag"
          :alt="props.alt || ''"
          class="preview-image"
        />
      </div>
      <div class="image-toolbar">
        <div class="toolbar-btn" @click="rotateLeft" title="向左旋转90°">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
            <path d="M3 3v5h5" />
          </svg>
        </div>
        <div class="toolbar-btn" @click="rotateRight" title="向右旋转90°">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 12a9 9 0 1 1-9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
            <path d="M21 3v5h-5" />
          </svg>
        </div>
        <div class="toolbar-btn" @click="resetImage" title="适应窗口">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <polyline points="3 6 5 6 21 6" />
            <path d="m5 6 14 0" />
            <path d="m6 3 0 6" />
            <path d="m18 3 0 6" />
            <polyline points="3 18 5 18 21 18" />
            <path d="m6 15 0 6" />
            <path d="m18 15 0 6" />
          </svg>
        </div>
        <div class="toolbar-btn" @click="downloadImage" title="下载图片">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
            <polyline points="7 10 12 15 17 10" />
            <line x1="12" y1="15" x2="12" y2="3" />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.image-preview-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

.image-wrapper {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-height: calc(100% - 60px);
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  cursor: grab;
  user-select: none;
  object-fit: contain;
}

.preview-image:active {
  cursor: grabbing;
}

.image-toolbar {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 10px 0;
  background-color: rgb(0 0 0 / 5%);
  border-top: 1px solid #e0e0e0;
}

.toolbar-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin: 0 8px;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s;
}

.toolbar-btn:hover {
  background-color: rgb(0 0 0 / 10%);
  transform: scale(1.1);
}

.toolbar-btn:active {
  transform: scale(0.95);
}
</style>
