export interface ProjectManagementSearch {
  code?: string;
  isDeleted?: string;
  manager?: string;
  name?: number;
  page?: number;
  pageSize?: number;
  phone?: string;
}

export interface ProjectManagementItem {
  address: string;
  bankName: string;
  code: string;
  createBy: string;
  createTime: string;
  id: number;
  inBlackList: number;
  isDeleted: boolean;
  name: string;
  num: string;
  orgPath: string;
  phone: string;
  updateBy: string;
  updateTime: string;
  userName: string;
}
