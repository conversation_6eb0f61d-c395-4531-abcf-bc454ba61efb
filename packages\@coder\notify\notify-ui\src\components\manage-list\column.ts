import { formatDate } from '../../formatDate';

export const columns = [
  {
    key: 'id',
    title: '操作',
    width: 250,
  },
  {
    dataIndex: 'typeName',
    key: 'typeName',
    title: '类型',
    width: 140,
  },

  {
    customRender: ({ record }: { record: any }) => {
      return record && record.createTime ? formatDate(record.createTime) : '';
    },
    dataIndex: 'createTime',
    key: 'createTime',
    title: '创建时间',
    width: 200,
  },
  {
    dataIndex: 'content',
    ellipsis: true,
    key: 'content',
    title: '内容',
  },
];
