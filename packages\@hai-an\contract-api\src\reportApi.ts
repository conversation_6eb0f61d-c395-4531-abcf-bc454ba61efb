import type { RequestClient } from '@vben/request';

import type { ReportContractSearcher } from './types/report';

export const createReportApi = (request: RequestClient, path: string) => {
  return {
    getContractInfo(): Promise<any> {
      return request.get(`${path}/Report/report-contract-info`);
    },

    /**
     * 收款合同报表
     * @param params
     * @returns
     */
    getContractReceive(params: ReportContractSearcher): Promise<any> {
      return request.get(`${path}/Report/report-contract-receive`, { params });
    },
    /**
     * 付款合同报表
     */
    getPayReport(params: ReportContractSearcher): Promise<any> {
      return request.get(`${path}/Report/report-contract-pay`, { params });
    },

    /** 应收账款明细表 */
    getReportReceiveInfo(): Promise<any> {
      return request.get(`${path}/Report/report-receive-info`);
    },
    /**
     *经营收付款合同对应表
     * @returns
     */
    getReportReceivePay(): Promise<any> {
      return request.get(`${path}/Report/report-receive-pay`);
    },
  };
};
