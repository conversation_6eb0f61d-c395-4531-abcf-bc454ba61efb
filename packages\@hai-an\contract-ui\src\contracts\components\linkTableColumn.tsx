import type { ColumnType } from 'ant-design-vue/es/table';

import { dayF } from '../../util';

export default [
  {
    dataIndex: 'code',
    title: '合同编号',
    width: 200,
  },
  {
    dataIndex: 'name',
    title: '合同名称',
  },
  {
    dataIndex: 'projectName',
    title: '项目名称',
  },
  {
    dataIndex: 'contractType',
    title: '合同类型',
  },
  {
    dataIndex: 'oppositeName',
    title: '对方名称',
  },
  {
    dataIndex: 'orgPath',
    title: '所属组织机构',
  },
  {
    customRender: ({ record }) => {
      return dayF(record.bookDate);
    },
    dataIndex: 'bookDate',
    title: '签订日期',
  },
  {
    dataIndex: 'contractTotal',
    title: '合同总价',
  },
  {
    customRender: ({ record }) => {
      return dayF(record.applyDate);
    },
    dataIndex: 'applyDate',
    title: '申请时间',
  },
  {
    dataIndex: 'createBy',
    title: '创建人',
  },
  {
    customRender: ({ record }) => {
      return `已完成${record.payCount}期`;
    },
    dataIndex: 'payCount',
    title: '合同进度',
  },
] as ColumnType[];
