import type { RequestClient } from '@vben/request';

import type {
  ContractWorkOrderSearcher,
  ContractWorkOrderViewModel,
} from './types/workorder';

export const createWorkOrderApi = (request: RequestClient, path: string) => {
  return {
    /**
     * Get contract workorder count
     */
    count(params: ContractWorkOrderSearcher): Promise<number> {
      return request.get(`${path}/ContractWorkOrder/count`, { params });
    },

    /**
     * Get contract workorder list
     */
    list(
      params: ContractWorkOrderSearcher,
    ): Promise<ContractWorkOrderViewModel[]> {
      return request.get(`${path}/ContractWorkOrder/list`, { params });
    },
  };
};
