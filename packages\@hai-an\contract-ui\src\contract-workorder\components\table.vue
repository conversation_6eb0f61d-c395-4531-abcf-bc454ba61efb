<!--  合同流程工单列表 table -->
<script setup lang="ts">
import type { ContractWorkOrderViewModel } from '@hai-an/contract-api';

import type { PropType } from 'vue';

import { computed, reactive } from 'vue';

import { Table as ATable } from 'ant-design-vue';

import { makeColumns } from './tableColumn';

const props = defineProps({
  datas: {
    default: () => [],
    type: Array as PropType<ContractWorkOrderViewModel[]>,
  },
  isEidtList: { type: Boolean },
  loading: { type: Boolean },
  pagination: { default: () => ({}), type: Object },
});
const emit = defineEmits(['handleTableChange', 'detail']);

const columns = reactive(makeColumns(emit));

const handleTableChange = (pagination: any) => {
  emit('handleTableChange', pagination);
};

const datas = computed(() => props.datas);
</script>
<template>
  <ATable
    class="ant-table-striped"
    size="middle"
    :row-class-name="(_, index) => (index % 2 === 1 ? 'table-striped' : '')"
    bordered
    :row-key="(data) => data.id"
    :pagination="pagination"
    :columns="columns"
    :loading="loading"
    @change="handleTableChange"
    :data-source="datas"
  />
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
