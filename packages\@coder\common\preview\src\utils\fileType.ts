import type { FileType } from '../components/usePreview';

const imageExtends = new Set(['bmp', 'gif', 'gif', 'jpeg', 'png', 'tiff']);
const text = new Set(['htm', 'html', 'json', 'md', 'txt']);
const audio = new Set(['acc', 'mp3', 'ogg', 'wav', 'wavm']);
const video = new Set(['mp4', 'ogg']);

/**
 * 根据文件内容判断文件类型
 * @param fileName 文件名称
 * @returns 文件类型
 */
export function getFileType(fileName: string): FileType {
  const mimeType = fileName.split('.').pop()?.toLowerCase() || '';
  if (imageExtends.has(mimeType)) return 'image';
  else if (text.has(mimeType)) return 'text';
  else if (audio.has(mimeType)) return 'video';
  else if (video.has(mimeType)) return 'audio';
  else
    switch (mimeType) {
      case 'docx': {
        return 'docx';
      }
      case 'pdf': {
        return 'pdf';
      }
      case 'pptx': {
        return 'pptx';
      }
      case 'xlsx': {
        return 'xlsx';
      }
      // No default
    }

  // 默认返回二进制类型
  return 'unknow';
}

/**
 *
 * @param contentType 文件内容类型
 * @return 文件类型
 */
export function getFileTypeByContentType(contentType: string): FileType {
  if (!contentType) {
    return 'unknow';
  }

  // 根据 Content-Type 判断文件类型
  if (contentType.includes('pdf')) {
    return 'pdf';
  } else if (contentType.includes('image')) {
    return 'image';
  } else if (contentType.includes('video')) {
    return 'video';
  } else if (contentType.includes('audio')) {
    return 'audio';
  } else if (
    contentType.includes('word') ||
    contentType.includes('msword') ||
    contentType.includes('officedocument.wordprocessingml')
  ) {
    return 'docx';
  } else if (
    contentType.includes('excel') ||
    contentType.includes('sheet') ||
    contentType.includes('officedocument.spreadsheetml')
  ) {
    return 'xlsx';
  } else if (
    contentType.includes('powerpoint') ||
    contentType.includes('presentation') ||
    contentType.includes('officedocument.presentationml')
  ) {
    return 'pptx';
  } else if (contentType.includes('text')) {
    return 'text';
  }

  // 默认返回二进制类型
  return 'unknow';
}
