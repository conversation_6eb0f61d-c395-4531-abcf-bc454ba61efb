<script lang="ts" setup>
/**
 * 选择文件，但是并不立刻上传
 */
import { nextTick, ref } from 'vue';

import { ToolbarButton } from '@coder/toolbar';

const emits = defineEmits<{ (e: 'select', file: File): void }>();
const fileUploadRef = ref();

const onSelectFile = () => {
  fileUploadRef.value.click();
};
const getFile = (event: any) => {
  if (event.target.files.length === 0) {
    return;
  }
  const file = event.target.files[0];
  emits('select', file);
  nextTick(() => {
    fileUploadRef.value.value = '';
  });
};
</script>

<template>
  <div>
    <ToolbarButton @click="onSelectFile"><slot>上传</slot></ToolbarButton>
    <input
      ref="fileUploadRef"
      multiple
      style="display: none"
      type="file"
      @change="getFile($event)"
    />
  </div>
</template>
