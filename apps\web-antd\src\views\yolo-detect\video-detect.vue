<script setup lang="ts">
// YOLO 视频检测页面
import { ref } from 'vue';

const loading = ref(false);
const videoFile = ref<File | null>(null);
const videoUrl = ref<string>('');

// 视频文件选择
const handleVideoSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    videoFile.value = target.files[0];
    videoUrl.value = URL.createObjectURL(target.files[0]);
  }
};

// 视频检测功能
const detectVideo = () => {
  if (!videoFile.value) {
    return;
  }
  loading.value = true;
  // TODO: 实现视频检测功能
  setTimeout(() => {
    loading.value = false;
  }, 3000);
};

// 清理视频URL
const clearVideo = () => {
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }
  videoFile.value = null;
  videoUrl.value = '';
};
</script>

<template>
  <div class="video-detect">
    <div class="page-header">
      <h2>视频检测</h2>
      <p>基于 YOLO 算法的视频目标检测与追踪</p>
    </div>

    <div class="detect-container">
      <div class="upload-section">
        <div class="upload-area">
          <input
            type="file"
            accept="video/*"
            @change="handleVideoSelect"
            style="display: none"
            id="video-input"
          />
          <label for="video-input" class="upload-button">选择视频文件</label>
          <p v-if="!videoFile">支持 MP4、AVI、MOV 等格式</p>
          <p v-else class="file-info">已选择: {{ videoFile.name }}</p>
        </div>

        <div class="control-area">
          <button
            @click="detectVideo"
            :disabled="loading || !videoFile"
            class="detect-button"
          >
            {{ loading ? '检测中...' : '开始检测' }}
          </button>
          <button @click="clearVideo" :disabled="loading" class="clear-button">
            清除视频
          </button>
        </div>
      </div>

      <div class="preview-section">
        <div class="video-preview" v-if="videoUrl">
          <video :src="videoUrl" controls preload="metadata">
            您的浏览器不支持视频播放
          </video>
        </div>
        <div class="placeholder" v-else>
          <p>视频预览区域</p>
        </div>
      </div>
    </div>

    <div class="result-section" v-if="loading">
      <div class="progress-area">
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
        <p>正在分析视频内容，请稍候...</p>
      </div>
    </div>

    <div class="result-section" v-else>
      <h3>检测结果</h3>
      <div class="result-content">
        <p>检测结果将在此显示</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.video-detect {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.detect-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-area {
  padding: 30px;
  text-align: center;
  border: 2px dashed #ddd;
  border-radius: 8px;
}

.upload-button {
  display: inline-block;
  padding: 12px 24px;
  color: white;
  cursor: pointer;
  background-color: #28a745;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.upload-button:hover {
  background-color: #218838;
}

.file-info {
  margin-top: 15px;
  font-weight: 500;
  color: #007bff;
}

.control-area {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.detect-button,
.clear-button {
  padding: 10px 20px;
  color: white;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.detect-button {
  background-color: #007bff;
}

.detect-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.clear-button {
  background-color: #6c757d;
}

.clear-button:hover:not(:disabled) {
  background-color: #545b62;
}

.detect-button:disabled,
.clear-button:disabled {
  cursor: not-allowed;
  background-color: #ccc;
}

.preview-section {
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-preview {
  width: 100%;
  max-width: 400px;
}

.video-preview video {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgb(0 0 0 / 10%);
}

.placeholder {
  padding: 60px 30px;
  color: #999;
  text-align: center;
  border: 2px dashed #ddd;
  border-radius: 8px;
}

.result-section {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.result-section h3 {
  margin: 0 0 15px;
  color: #333;
}

.progress-area {
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  margin-bottom: 15px;
  overflow: hidden;
  background-color: #e9ecef;
  border-radius: 4px;
}

.progress-fill {
  width: 100%;
  height: 100%;
  background-color: #007bff;
  border-radius: 4px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.result-content {
  padding: 20px;
  color: #666;
  text-align: center;
  background-color: white;
  border-radius: 4px;
}
</style>
