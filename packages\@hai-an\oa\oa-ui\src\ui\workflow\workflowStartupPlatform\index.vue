<script setup lang="ts">
import type { WorkflowListItem } from '@hai-an/oa-api';

import { onMounted, reactive, ref } from 'vue';

import {
  ApartmentOutlined,
  GoldOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons-vue';
import { swfOption } from '@coder/swf-render';
import { createWorkflowApi, haianOAOption as options } from '@hai-an/oa-api';
import {
  Button as AButton,
  Card as ACard,
  Col as ACol,
  Modal as antModal,
  Row as ARow,
} from 'ant-design-vue';

import { MakeAntDesignIcon } from '../../../../../../../icons/src/iconify';
import { createSwf } from './swfApi';

const emits = defineEmits(['dispose']);

// const api = createWorkflowApi(options.request, options.swfPath);
const api = createWorkflowApi(options.request, swfOption.host);

const params = reactive({
  page: 1,
  pageSize: 40,
});

const creatingLoading = ref(false);
const storageKey = '__swf_sort__';
const sortType = ref(Number.parseInt(localStorage.getItem(storageKey) || '0'));
const loading = ref(false);
const groupSource = reactive(new Map<string, Array<WorkflowListItem>>());
const otherList = reactive(new Array<WorkflowListItem>());
const allWp = reactive(new Array<string>());

const loadList = async () => {
  loading.value = true;
  const res: any = await api.list(params);
  buildWorkProcessList(res);
  loading.value = false;
};
onMounted(() => {
  loadList();
});

const buildWorkProcessList = (items: Array<WorkflowListItem>) => {
  items.forEach((el) => {
    if (el.group) {
      if (!groupSource.has(el.group)) {
        groupSource.set(el.group, new Array<WorkflowListItem>());
      }
      groupSource.get(el.group)?.push(el);
    } else {
      otherList.push(el);
    }
    allWp.push(el.name);
  });
};

const onSortChange = () => {
  sortType.value = sortType.value === 1 ? 0 : 1;
  localStorage.setItem(storageKey, `${sortType.value}`);
};

const CreateProcessInstance = (processsName: string) => {
  creatingLoading.value = true;
  createSwf(processsName).then((res) => {
    creatingLoading.value = false;
    if (
      res.success ||
      (res.startResult !== null && res.startResult.success === false)
    ) {
      antModal.info({
        title: () => '提示',
        content: () =>
          res.message ||
          (res.startResult === null ? '' : res.startResult.message),
      });
    }
    const userName = res.createUser;
    if (res.startResult.success) {
      const item = res.startResult.workActivities.find(
        (item) => item.disposeUser === userName,
      );
      item && emits('dispose', item.workActivityId);
    }
  });
};

/**
 * 创建流程实例
 */
const onCreateProcessInstance = (processsName: string) => {
  antModal.confirm({
    title: '提示',
    content: () => `你正准备创建流程${processsName}, 点击"确认"后立刻创建。`,
    onOk: () => {
      CreateProcessInstance(processsName);
    },
    okText: '创建',
    cancelText: '取消',
  });
};
const workflowIcon = (workflow: WorkflowListItem) => {
  return MakeAntDesignIcon(workflow.icon);
};
</script>

<template>
  <ACard v-loading="creatingLoading">
    <template #extra>
      <AButton @click="onSortChange" title="切换视图">
        <template #icon>
          <UnorderedListOutlined v-if="sortType === 1" />
          <GoldOutlined v-if="sortType === 0" />
        </template>
      </AButton>
    </template>
    <template v-if="sortType === 0">
      <template v-for="key in groupSource.keys()" :key="key">
        <h4 class="title">{{ key }}</h4>
        <ARow :gutter="16">
          <ACol :span="6" :key="item.name" v-for="item in groupSource.get(key)">
            <AButton
              type="link"
              @click="() => onCreateProcessInstance(item.name)"
            >
              <template #icon>
                <component :is="workflowIcon(item)" v-if="item.icon" />
                <ApartmentOutlined v-else />
                {{ item.name }}
              </template>
            </AButton>
          </ACol>
        </ARow>
      </template>

      <h4 v-if="sortType === 0">其他</h4>
      <ARow :gutter="16" v-if="sortType === 0">
        <ACol :span="6" v-for="(item, index) in otherList" :key="index">
          <AButton
            type="link"
            @click="() => onCreateProcessInstance(item.name)"
          >
            <template #icon> <ApartmentOutlined /> </template>{{ item.name }}
          </AButton>
        </ACol>
      </ARow>
    </template>

    <ARow :gutter="16" v-if="sortType === 1">
      <ACol :span="6" v-for="(item, index) in allWp" :key="index">
        <AButton type="link" @click="() => onCreateProcessInstance(item)">
          <template #icon> <ApartmentOutlined /> </template>{{ item }}
        </AButton>
      </ACol>
    </ARow>
  </ACard>
</template>

<style scoped>
.title {
  margin-top: 0;
  margin-bottom: 0.5em;
  font-weight: 500;
  color: rgb(0 0 0 / 85%);
}
</style>
