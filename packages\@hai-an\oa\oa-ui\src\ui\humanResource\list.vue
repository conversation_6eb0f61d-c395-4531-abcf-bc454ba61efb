<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';

// import { useLists } from '@vben/hooks';
import {
  DownloadOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue';
import {
  createHumanResourceApi,
  haianHumanOption as option,
} from '@hai-an/human-api';
import {
  Button as AButton,
  Card as ACard,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
  SelectOption as ASelectOption,
  Space as ASpace,
  Table as ATable,
  message,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import useLists from '../../useLists';
import { createColumns } from './columns';
import downloadFile from './downloadFile';
import DetailModal from './modal/detail-modal.vue';
import ImportModal from './modal/import-modal.vue';

const emit = defineEmits(['orderInfo']);

const API = createHumanResourceApi(option.request, option.path);

const currentYear = dayjs().format('YYYY');
const currentYearMonth = dayjs().format('YYYY-MM');
const itemId = ref<string>('');
const isVisible = ref<boolean>(false);
const importVisible = ref<boolean>(false);
const searchForm = reactive({
  name: '',
  type: '1',
  yearMonth: '2025-06',
});
const {
  loadList,
  reload,
  tableData,
  loading,
  pagination,
  changeTable,
  formData,
} = useLists({ listApi: API.list, searchForm });

const onPreview = (id: string) => {
  itemId.value = id;
  isVisible.value = true;
};
const columns = computed(() =>
  createColumns(onPreview, searchForm.type),
) as any;

const handleTypeChange = () => {
  searchForm.yearMonth =
    searchForm.type === '2' ? currentYear : currentYearMonth;
};

const handleExport = async (type: string) => {
  // type  1:导出考前汇总  2:导出考勤明显
  if (!searchForm.yearMonth) return message.error('请选择年月');
  try {
    const params = {
      ...formData,
      ...searchForm,
    };

    let res: any = null;
    let name = `人力资源数据${searchForm.yearMonth}`;
    if (type === '1') {
      res = await API.export(params);
    } else {
      res = await API.exportDetail(params);
      name = `人力资源数据明细${searchForm.yearMonth}`;
    }
    downloadFile(res.data, name);
  } catch {}
};

const handleImportAnnualLeave = () => {
  importVisible.value = true;
};
// 处理显示工单信息
const handleOrderInfo = (orderId: number) => {
  emit('orderInfo', orderId);
};
onMounted(() => {
  loadList();
});
</script>

<template>
  <ACard>
    <AForm layout="inline" class="search-form">
      <AFormItem label="姓名">
        <AInput
          v-model:value="searchForm.name"
          placeholder="请输入姓名"
          allow-clear
        />
      </AFormItem>
      <AFormItem label="统计类型">
        <ASelect
          v-model:value="searchForm.type"
          style="width: 120px"
          @change="handleTypeChange"
        >
          <ASelectOption value="1">按月统计</ASelectOption>
          <ASelectOption value="2">按年统计</ASelectOption>
        </ASelect>
      </AFormItem>
      <AFormItem :label="searchForm.type === '2' ? '年' : '年月'">
        <ADatePicker
          v-model:value="searchForm.yearMonth"
          :picker="searchForm.type === '2' ? 'year' : 'month'"
          :placeholder="searchForm.type === '2' ? '选择年' : '选择年月'"
          :format="searchForm.type === '2' ? 'YYYY' : 'YYYY-MM'"
          :value-format="searchForm.type === '2' ? 'YYYY' : 'YYYY-MM'"
          :allow-clear="false"
        />
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="reload">
            <template #icon><SearchOutlined /></template>
            查询
          </AButton>
          <AButton type="primary" @click="handleExport('1')">
            <template #icon><DownloadOutlined /></template>
            导出考勤汇总
          </AButton>
          <AButton type="primary" @click="handleExport('2')">
            <template #icon><DownloadOutlined /></template>
            导出考勤明细
          </AButton>
          <AButton
            type="primary"
            @click="handleImportAnnualLeave"
            style="background-color: #9c0; border-color: #9c0"
          >
            <template #icon><UploadOutlined /></template>
            导入年假
          </AButton>
        </ASpace>
      </AFormItem>
    </AForm>
  </ACard>
  <ATable
    style="margin-top: 6px"
    :data-source="tableData"
    :columns="columns"
    :loading="loading"
    :pagination="pagination"
    @change="changeTable"
    bordered
  />
  <ImportModal v-model:visible="importVisible" @reload="reload" />
  <DetailModal
    v-model:visible="isVisible"
    :item-id="itemId"
    @order-info="handleOrderInfo"
  />
</template>

<style scoped></style>
