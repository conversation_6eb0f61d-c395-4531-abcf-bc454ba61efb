<script setup lang="ts">
import type {
  ContractGroupListItem,
  ContractGroupViewModel,
} from '@hai-an/contract-api';

import { onMounted, reactive } from 'vue';

import { CheckOutlined } from '@ant-design/icons-vue';
import {
  createGroupApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  Card as ACard,
  Col as ACol,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Row as ARow,
} from 'ant-design-vue';

import { dayF } from '../../util';
import CList from './contractList/table.vue';

const props = defineProps({
  id: { default: 0, type: Number },
});
const emit = defineEmits(['doCancel']);

const api = createGroupApi(options.request, options.path);
const submitForm = reactive<ContractGroupListItem>({
  createBy: '',
  createTime: '',
  id: 0,
  name: '',
  completeWorkloadIn: 0,
  completeWorkloadOut: 0,
  contractsIn: [],
  contractsOut: [],
  contractTotalIn: 0,
  contractTotalOut: 0,
  isDeleted: false,
  payTotalIn: 0,
  payTotalOut: 0,
  updateBy: '',
  updateTime: '',
});
const contractsIn = reactive<ContractGroupViewModel[]>([]);
const contractsOut = reactive<ContractGroupViewModel[]>([]);

const doCancel = () => {
  emit('doCancel');
};
const reload = () => {
  if (props.id) {
    api.getInfo(props.id).then((res) => {
      Object.assign(submitForm, res);
      contractsIn.splice(0);
      contractsIn.push(...res.contractsIn);
      contractsOut.splice(0);
      contractsOut.push(...res.contractsOut);
    });
  }
};
onMounted(() => {
  reload();
});
</script>
<template>
  <div style="padding: 10px; background-color: #ececec">
    <ADescriptions bordered size="small">
      <ADescriptionsItem label="合同组名称">
        {{ submitForm.name }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建人">
        {{ submitForm.createBy }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建日期">
        {{ dayF(submitForm.createTime) }}
      </ADescriptionsItem>
    </ADescriptions>
  </div>
  <ACard title="收款合同">
    <CList :datas="contractsIn" />
  </ACard>
  <ACard title="付款合同">
    <CList :datas="contractsOut" />
  </ACard>
  <div style="padding: 20px; background-color: #ececec">
    <ARow type="flex" justify="center">
      <ACol :span="3">
        <AButton type="primary" @click="doCancel">
          <CheckOutlined />确定
        </AButton>
      </ACol>
    </ARow>
  </div>
</template>
