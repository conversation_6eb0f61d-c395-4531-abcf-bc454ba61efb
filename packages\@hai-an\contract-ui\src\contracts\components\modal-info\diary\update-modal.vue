<script setup lang="ts">
import type { DiaryFormData } from '@hai-an/contract-api';

import { computed, ref, watch } from 'vue';

import {
  DeleteOutlined,
  DownloadOutlined,
  EyeOutlined,
  LoadingOutlined,
  PaperClipOutlined,
  UploadOutlined,
  WarningOutlined,
} from '@ant-design/icons-vue';
import { PreviewContent } from '@coder/preview';
import { createDiaryApi, haianDiaryOption } from '@hai-an/diary-api';
import {
  Alert as <PERSON><PERSON>t,
  Button as AButton,
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Modal as AModal,
  Row as ARow,
  Space as ASpace,
  Textarea as ATextarea,
  Upload as AUpload,
  message,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';

import 'dayjs/locale/zh-cn';

const props = defineProps<{
  businessId?: number | string;
  businessName?: string;
  defaultDiaryTime?: string;
  diaryType?: string;
  editId?: string;
  visible: boolean;
}>();
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}>();
const API = createDiaryApi(haianDiaryOption.request, haianDiaryOption.path);
// 设置 dayjs 语言为中文
dayjs.locale('zh-cn');
const dateLocale = locale;

const formRef = ref();
const loading = ref(false);
const formData = ref<DiaryFormData>({});
const fileList = ref<any[]>([]);
const uploadLoading = ref(false);

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const isEdit = computed(() => !!props.editId || !!formData.value.id);
// 初始化表单数据
const initFormData = () => {
  formData.value = {} as DiaryFormData;
  // 设置默认值
  if (props.businessId) {
    formData.value.businessId = props.businessId as string;
  }
  if (props.businessName) {
    formData.value.businessName = props.businessName;
  }
  // 处理日记类型，同时兼容新旧接口
  if (props.diaryType) {
    formData.value.diaryType = props.diaryType;
  }
  // 处理日记时间，同时兼容新旧接口
  if (props.defaultDiaryTime) {
    formData.value.diaryTime = props.defaultDiaryTime;
  }
};

watch(
  () => visible.value,
  (newVal) => {
    if (newVal) {
      if (props.editId) {
        loadData();
      } else {
        initFormData();
        // 清空文件列表
        fileList.value = [];
      }
    } else {
      formRef.value?.resetFields();
    }
  },
);

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;
    const params: DiaryFormData = { ...formData.value };
    if (isEdit.value) {
      await API.update(params);
      message.success('更新成功');
      visible.value = false;
      emit('success');
    } else {
      const res = (await API.add(params)) as any;
      formData.value.id = res.data.id;
      message.success('日记保存成功，现在可以上传附件');
    }
  } catch (error) {
    console.error('提交表单失败:', error);
  } finally {
    loading.value = false;
  }
};
const handleCancel = () => {
  visible.value = false;
};

const loadData = async () => {
  const currentId = props.editId || formData.value.id;
  loading.value = true;
  try {
    const res: any = (await API.getBayId(currentId as string)) as DiaryFormData;
    const data = res.data;
    formData.value = {
      id: data.id,
      title: data.title,
      diaryType: data.diaryType || '1',
      businessId: data.businessId,
      businessName: data.businessName || '',
      diaryTime: data.diaryTime
        ? dayjs(data.diaryTime).format('YYYY-MM-DD')
        : '',
      content: data.content,
    };
    fileList.value = data?.fileList?.map((file: any) => {
      return {
        uid: file.id || file.fileId || `file_${Date.now()}_${Math.random()}`,
        name: file.fileName || file.name || '未知文件',
        status: 'done',
        url: '#', // 如果有文件预览URL可以添加
        fileId: file.fileId, // | file.id
        id: file.id,
      };
    });
    loading.value = false;
  } catch {
    loading.value = false;
  }
};

// 文件上传前的检查
const beforeUpload = (file: File) => {
  // 验证文件大小（50MB以下）
  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('文件必须小于50MB!');
    return false;
  }
  return true;
};

// 自定义上传方法
const customUpload = async ({ file, onSuccess, onError }: any) => {
  if (!formData.value.id) {
    message.error('请先保存日记再上传附件');
    onError('请先保存日记再上传附件');
    return;
  }
  try {
    uploadLoading.value = true;
    // 创建进度回调
    // const updateProgress = (e: ProgressEvent) => {
    //   if (e.lengthComputable) {
    //     const percent = Math.round((e.loaded / e.total) * 100)
    //     onProgress({ percent })
    //   }
    // }
    // 手动添加文件到列表，使其可见 (关键修复)
    const uploadingFile = {
      uid: `upload_${Date.now()}`,
      name: file.name,
      status: 'uploading',
      percent: 0,
    };
    fileList.value = [...fileList.value, uploadingFile];
    // 使用API上传文件
    const response = await API.uploadFile(
      file,
      file.name,
      formData.value.id as string,
      undefined, // refId 可选
      undefined, // comment 可选
    );
    if (response.code === 200) {
      message.success(`${file.name} 上传成功`);
      onSuccess(response, file);
      // 上传成功后重新加载文件列表
      if (formData.value.id) {
        try {
          await loadData(); // 重新从服务器获取文件列表
        } catch (error) {
          console.error('刷新文件列表失败:', error);
          // 如果加载失败，至少更新当前文件的状态
          const fileIndex = fileList.value.findIndex(
            (item) => item.uid === uploadingFile.uid,
          );
          if (fileIndex !== -1) {
            fileList.value[fileIndex].status = 'done';
            fileList.value[fileIndex].percent = 100;
          }
        }
      }
    } else {
      message.error(`${file.name} 上传失败: ${response.data.message}`);
      // 在文件列表中标记为失败
      const fileIndex = fileList.value.findIndex(
        (item) => item.uid === uploadingFile.uid,
      );
      if (fileIndex !== -1) {
        fileList.value[fileIndex].status = 'error';
      }
      onError(response);
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    message.error(`${file.name} 上传失败`);
    onError(error);
  } finally {
    uploadLoading.value = false;
  }
};

// 删除文件
const handleRemove = async (file: any) => {
  // 防止重复点击删除按钮
  if (file.deleting) {
    return false;
  }
  // 如果文件已经上传成功，则调用API从服务器删除
  if (file.status === 'done' && (file.uid || file.fileId)) {
    try {
      // 设置删除中状态
      file.deleting = true;
      const fileId = file.id || file.uid;
      const response = await API.deleteFile(fileId);
      if (response.code === 200) {
        // 从UI中移除文件
        const index = fileList.value.indexOf(file);
        const newFileList = [...fileList.value];
        newFileList.splice(index, 1);
        fileList.value = newFileList;

        message.success(`${file.name} 已删除`);
      } else {
        message.error(`删除失败: ${response.data.message}`);
        // 移除删除状态
        file.deleting = false;
        return false;
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      message.error(`删除失败: 服务器错误`);
      // 移除删除状态
      file.deleting = false;
      return false;
    }
  } else {
    // 如果文件还未上传成功，直接从UI中移除
    const index = fileList.value.indexOf(file);
    const newFileList = [...fileList.value];
    newFileList.splice(index, 1);
    fileList.value = newFileList;

    message.success(`${file.name} 已移除`);
  }

  return true;
};

// 下载文件
const handleDownload = async (file: any) => {
  // 防止重复点击下载按钮
  if (file.downloading) return;
  if (!file.uid) {
    return message.error('找不到文件ID，无法下载');
  }
  try {
    // 设置下载中状态
    file.downloading = true;
    const fileId = file.uid;
    const response = await API.downloadFile(fileId);
    // 创建Blob对象
    const blob = new Blob([response.data]);
    // 创建下载链接
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = file.name;

    // 模拟点击下载
    document.body.append(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(link.href);

    message.success(`${file.name} 下载成功`);
  } catch (error) {
    console.error('下载文件失败:', error);
    message.error(`下载失败: 服务器错误`);
  } finally {
    // 无论成功失败，都移除下载状态
    file.downloading = false;
  }
};
const previewUrl = ref<string>('');
const headers = computed(() => {
  // 使用 diary API 配置中的 getToken 方法
  const token = haianDiaryOption.getToken ? haianDiaryOption.getToken() : '';

  const result: Record<string, string> = {};
  if (token) {
    result.Authorization = token; // 已经包含 "Bearer " 前缀
  }
  return result;
});
// const previewFiles = ref()
const onPreview = (file: any) => {
  previewUrl.value = API.getPreviewFileUrl(file.fileId);
};
</script>

<template>
  <div>
    <AModal
      :open="visible"
      :title="isEdit ? '编辑OA日记' : '新增OA日记'"
      :width="1100"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="loading"
      :ok-text="isEdit ? '保存' : '保存'"
      cancel-text="取消"
    >
      <AForm
        ref="formRef"
        :model="formData"
        layout="vertical"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
      >
        <ARow :gutter="16">
          <ACol :span="12">
            <AFormItem label="标题" name="title">
              <AInput
                v-model:value="formData.title"
                placeholder="请输入日记标题"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem label="日记时间" name="diaryTime">
              <ADatePicker
                v-model:value="formData.diaryTime"
                placeholder="选择日记时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :locale="dateLocale"
                style="width: 100%"
              />
            </AFormItem>
          </ACol>
        </ARow>
        <AFormItem label="日记内容" name="content">
          <ATextarea
            v-model:value="formData.content"
            placeholder="请输入日记内容"
            :rows="6"
            :max-length="2000"
            show-count
          />
        </AFormItem>
        <AFormItem label="附件上传" name="files">
          <AUpload
            v-if="formData.id"
            :file-list="fileList"
            @remove="handleRemove"
            :before-upload="beforeUpload"
            :custom-request="customUpload"
            :multiple="true"
          >
            <AButton type="primary">
              <template #icon><UploadOutlined /></template>
              上传附件
            </AButton>
            <template #itemRender="{ file }">
              <div class="file-item">
                <ASpace>
                  <PaperClipOutlined
                    v-if="!file.status || file.status === 'done'"
                  />
                  <LoadingOutlined
                    v-else-if="file.status === 'uploading'"
                    spin
                  />
                  <WarningOutlined
                    v-else-if="file.status === 'error'"
                    style="color: #ff4d4f"
                  />
                  <span class="file-name" :title="file.name">{{
                    file.name
                  }}</span>
                  <span v-if="file.status === 'uploading'" class="file-status">
                    上传中...
                    {{ file.percent ? `${Math.round(file.percent)}%` : '' }}
                  </span>
                  <span
                    v-else-if="file.status === 'error'"
                    class="file-status"
                    style="color: #ff4d4f"
                  >
                    上传失败
                  </span>
                </ASpace>

                <ASpace>
                  <!-- <PreviewButton :get-file="() => getFile(file)" :previewHost="previewHost"></PreviewButton> -->
                  <AButton
                    v-if="file.status === 'done'"
                    type="link"
                    size="small"
                    @click="() => onPreview(file)"
                    :loading="file.downloading"
                  >
                    <template #icon>
                      <EyeOutlined v-if="!file.downloading" />
                    </template>
                    预览
                  </AButton>
                  <AButton
                    v-if="file.status === 'done'"
                    type="link"
                    size="small"
                    @click="() => handleDownload(file)"
                    :loading="file.downloading"
                  >
                    <template #icon>
                      <DownloadOutlined v-if="!file.downloading" />
                    </template>
                    下载
                  </AButton>
                  <AButton
                    type="link"
                    danger
                    size="small"
                    @click="() => handleRemove(file)"
                    :loading="file.deleting"
                  >
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </AButton>
                </ASpace>
              </div>
            </template>
          </AUpload>
          <AAlert v-else style="margin-bottom: 16px" type="warning" show-icon>
            <template #message>请先保存日记后才能上传附件</template>
          </AAlert>
        </AFormItem>
      </AForm>
    </AModal>
    <AModal
      :open="previewUrl ? true : false"
      title="预览"
      :width="1300"
      @cancel="() => (previewUrl = '')"
      footer=""
      :confirm-loading="loading"
      cancel-text="取消"
    >
      <PreviewContent v-if="previewUrl" :url="previewUrl" :headers="headers" />
    </AModal>
  </div>
</template>

<style scoped>
:deep(.ant-form-item-label) {
  font-size: 14px; /* 增加标签字体大小 */
  font-weight: 600;
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  height: 36px; /* 增加输入框高度 */
  font-size: 14px; /* 增加输入框字体大小 */
  border-radius: 6px;
}

:deep(.ant-select-selection-item),
:deep(.ant-picker-input > input) {
  line-height: 36px; /* 行高调整 */
}

:deep(.ant-textarea) {
  border-radius: 6px;
}

:deep(.ant-upload-wrapper) {
  width: 100%;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 0;
  font-size: 14px; /* 增加文件项的字体大小 */
  border-bottom: 1px dashed #e8e8e8;
}

.file-name {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500; /* 增加文件名的字体权重 */
  white-space: nowrap;
}

.file-status {
  margin-left: 8px;
  font-size: 13px;
  color: #666;
}

:deep(.ant-upload-list) {
  margin-top: 10px;
}

:deep(.ant-upload-list-item) {
  padding: 8px 0;
  margin: 0;
}

:deep(.ant-btn-link) {
  height: 28px; /* 按钮高度 */
  padding: 0 8px;
  font-size: 14px; /* 按钮文字大小 */
}
</style>
