import type { ContractListItem, ContractSearch } from '@hai-an/contract-api';
import type { TablePaginationConfig } from 'ant-design-vue';

import { computed, reactive, ref } from 'vue';

import { createContractApi, haianContractOption } from '@hai-an/contract-api';

export const useList = (props: any) => {
  const api = createContractApi(
    haianContractOption.request,
    haianContractOption.path,
  );
  const searchForm = reactive<ContractSearch>({
    page: 1,
    pageSize: 10,
    isDeleted: false,
    projectArchiveCode: props.projectArchiveCode,
    contractType: props.contractType,
  });
  const loading = ref(false);
  const total = ref(0);
  const dataSource = reactive<ContractListItem[]>([]);
  const filter = () => {
    loading.value = true;
    api.list(searchForm).then((res) => {
      dataSource.splice(0);
      dataSource.push(...res);
      loading.value = false;
    });
    api.count(searchForm).then((res) => {
      pagination.value.total = res;
      total.value = res;
    });
  };
  const search = () => {
    searchForm.page = 1;
    filter();
  };

  const pagination = computed(() => {
    return {
      pageSize: searchForm.pageSize, // 每页中显示10条数据
      pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
      total: total.value, // 总数据
    } as TablePaginationConfig;
  });

  return {
    pagination,
    dataSource,
    search,
    filter,
    loading,
    searchForm,
    pageChange: (page: TablePaginationConfig) => {
      searchForm.page = page.current;
      searchForm.pageSize = page.pageSize;
      filter();
    },
  };
};
