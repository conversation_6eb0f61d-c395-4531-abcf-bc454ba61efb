import type { RequestClient } from '@vben/request';

import type {
  FileListItem,
  FileSearcher,
  PackageMultiFiles,
  ResponseMessage,
} from '../types';

export const createFileApi = (request: RequestClient, basePath: string) => {
  const path = `${basePath}/File`;

  const changeIsCommunal = async (
    id: number,
    isCommunal: boolean,
  ): Promise<void> => {
    const data = await request.put(
      `${path}/change_isCommunal/${id}/${isCommunal}`,
    );
    return data;
  };

  const count = async (searcher: FileSearcher): Promise<number> => {
    const data = await request.get(`${path}/count`, { params: searcher });
    return data;
  };

  const deleteFile = async (id: number): Promise<ResponseMessage> => {
    const data = await request.delete(`${path}/deleteFile/${id}`);
    return data;
  };

  const getDownloadUrl = (id: number): string => {
    const result = `${path}/get-file-by-id/${id}`;
    if (result.startsWith('http')) {
      return result;
    }
    return `api${result}`;
  };

  const getPreviewTicket = async (id: number): Promise<{ ticket: string }> => {
    const data = await request.get(`${path}/get-preview-ticket/${id}`);
    return data;
  };

  const list = async (searcher: FileSearcher): Promise<FileListItem[]> => {
    const data = await request.get(`${path}/list`, { params: searcher });
    return data;
  };

  const packFiles = async (
    multiFiles: PackageMultiFiles,
  ): Promise<ResponseMessage> => {
    const data = await request.post(`${path}/pack-files`, multiFiles);
    return data;
  };

  const getQuickUploadUrl = (folderId: number): string => {
    return `${path}/quick-upload/${folderId}`;
  };

  const getUploadUrl = (folderId: number): string => {
    return `${path}/upload/${folderId}`;
  };

  const getFileInfo = (id: number): Promise<FileListItem> => {
    return request.get(`${path}/${id}`);
  };
  /**
   * 文档查询模块api
   */
  const listSearch = async (
    searcher: FileSearcher,
  ): Promise<FileListItem[]> => {
    return request.get(`${path}/list-search`, { params: searcher });
  };
  const liseCount = async (searcher: FileSearcher): Promise<number> => {
    return request.get(`${path}/list-search-count`, { params: searcher });
  };
  // const packageFiles = async(multiFiles:PackageMultiFiles) => {
  //   return request.post(`${path}/pack-files/`, multiFiles)
  // }
  const getTicket = (id: number) => {
    return request.get(`${path}/get-preview-ticket/${id}`);
  };
  const downloadUrl = (id: number) => {
    return `${path}/get-file-by-id/${id}`;
  };
  return {
    changeIsCommunal,
    count,
    deleteFile,
    getDownloadUrl,
    getFileInfo,
    getPreviewTicket,
    getQuickUploadUrl,
    getUploadUrl,
    packFiles,
    list,
    // 文档查询模块api
    listSearch,
    liseCount,
    getTicket,
    downloadUrl,
  };
};
