<!-- 关联合同列表 -->
<script setup lang="ts">
import type { ContractListItem, ContractSearch } from '@hai-an/contract-api';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  createContractApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import { Card as ACard, Modal as AModal } from 'ant-design-vue';

import ContractInfo from './components/info.vue';
import ContractTable from './components/linkTable.vue';

const props = defineProps({
  code: { default: () => '', type: String },
});
const api = createContractApi(options.request, options.path);
const code = computed(() => {
  return props.code;
});
const infoTitle = ref('');
const isInfo = ref(false);
const isEidtList = ref(false);
const loading = ref(false);
const infoId = ref(0);

const searchForm = reactive({
  page: 1,
  pageSize: 10,
} as ContractSearch);
const total = ref(0);
const datas = reactive<ContractListItem[]>([]);
const pagination = computed(() => {
  return {
    pageSize: searchForm.pageSize, // 每页中显示10条数据
    pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
    showQuickJumper: true,
    showSizeChanger: true,
    showTotal: (total: any) => `共有 ${total} 条数据`, // 分页中显示总的数据
    total: total.value, // 总数据
    current: searchForm.page,
  };
});

const filter = () => {
  loading.value = true;
  const linkCode = code.value.split('-')[0];
  searchForm.linkCode = linkCode;
  api.linkList(searchForm).then((res) => {
    datas.splice(0);
    datas.push(...res);
    loading.value = false;
  });

  api.linkCount(searchForm).then((res) => {
    total.value = res;
  });
};
const search = () => {
  searchForm.page = 1;
  filter();
};
const onInfo = (val: ContractListItem) => {
  infoTitle.value = '关联合同列表';
  isInfo.value = true;
  infoId.value = val.id;
};

const canceled = () => {
  isInfo.value = false;
};

// 分页按钮调用
const handleTableChange = (val: any) => {
  searchForm.page = val.current;
  searchForm.pageSize = val.pageSize;
  filter();
};

onMounted(() => {
  search();
});
</script>
<template>
  <ACard>
    <ContractTable
      :datas="datas"
      :pagination="pagination"
      :loading="loading"
      :is-eidt-list="isEidtList"
      @to-info="onInfo"
      @handle-table-change="handleTableChange"
    />
  </ACard>
  <AModal
    :title="infoTitle"
    :width="1200"
    :open="isInfo"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <ContractInfo :id="infoId" @do-cancel="canceled" />
  </AModal>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
