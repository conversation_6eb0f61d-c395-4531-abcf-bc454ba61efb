<script setup lang="ts">
import { Page } from '@vben/common-ui';

import { ContractList as ContractListManager } from '@hai-an/contract-ui';

// const previewFile = (path:string) => {
//   // const testUrl = 'api/notify/messageFile/get-file-by-id/cbc4581b-2916-41f3-a97a-8b080bbb58bd?fileName=1000全国.jpg'
//   // 方法1
//   // const origin = window.location.origin
//   // const openUrl = origin + '/#/preview-file?url=' + testUrl
//   // window.open(openUrl, '_blank')
//   // 方法2
//   // router.push({
//   //   name: 'PreivewFile',
//   //   query: {
//   //     url: testUrl
//   //   }
//   // })

//   router.push({
//     name: 'PreviewDocument',
//     query: {
//       url: path,
//     },
//   });
// }
</script>

<template>
  <Page>
    <!-- com:{
       meta:{
        title:'合同管理',
        icon:'lucide:book-check'
     },
     name:'ContractList',
     path:'/contract/list',
    } -->
    <ContractListManager />
  </Page>
</template>
