<script setup lang="ts">
import type { WorkloadSubmit } from '@hai-an/contract-api';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import {
  createWorkloadApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  Card as ACard,
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Row as ARow,
  message,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: 0, type: Number },
});
const emit = defineEmits(['doSave', 'doCancel']);

const api = createWorkloadApi(options.request, options.path);

const submitForm = reactive<WorkloadSubmit>({
  amount: 0,
  completeWorkload: 0,
  contractCode: '',
  endDate: undefined,
  id: 0,
  orderNo: '',
  projectCode: '',
  startDate: undefined,
  unitPrice: 0,
  updateUser: '',
});
const dateFormat = 'YYYY-MM-DD';

const submitFormRef = ref();
const rules = ref({
  contractCode: [{ message: '请选择合同!', required: true }],
  endDate: [{ message: '请选择结束日期!', required: true }],
  projectCode: [{ message: '请选择项目!', required: true }],
  startDate: [{ message: '请选择开始日期!', required: true }],
});

const toSave = () => {
  submitFormRef.value.validate().then(() => {
    api.save(submitForm).then((res) => {
      if (res.success) {
        message.success(res.message);
        emit('doSave');
      } else {
        message.error(res.message);
      }
    });
  });
};
const realod = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};

const startDateForm = computed({
  get: () => {
    const startDate = dayjs(submitForm.startDate);
    return dayjs(startDate);
  },
  set: (v) => {
    submitForm.startDate = v.format(dateFormat);
  },
});
const endDateForm = computed({
  get: () => {
    const endDate = dayjs(submitForm.endDate);
    return dayjs(endDate);
  },
  set: (v) => {
    submitForm.endDate = v.format(dateFormat);
  },
});
const doReset = () => {
  if (submitForm.id === 0) {
    const tempForm = {
      amount: 0,
      contractCode: '',
      endDate: undefined,
      projectCode: '',
      startDate: undefined,
      unitPrice: 0,
    };
    Object.assign(submitForm, tempForm);
  } else {
    realod();
  }
};
const doCancel = () => {
  emit('doCancel');
};

onMounted(() => {
  realod();
});

const labelCol = { style: { width: '150px' } };
</script>
<template>
  <ACard>
    <AForm
      ref="submitFormRef"
      layout="horizontal"
      :model="submitForm"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 14 }"
    >
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="选择合同"
            name="contractCode"
          >
            <SelectContract
              v-model:value="submitForm.contractCode"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="选择项目"
            name="projectCode"
          >
            <SelectProject
              v-model:value="submitForm.projectCode"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="开始日期"
            name="startDate"
          >
            <ADatePicker
              style="width: 265px"
              v-model:value="startDateForm"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="结束日期"
            name="endDate"
          >
            <ADatePicker
              style="width: 265px"
              v-model:value="endDateForm"
              autocomplete="off"
            />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="工作量"
            name="completeWorkload"
          >
            <AInputNumber
              v-model:value="submitForm.completeWorkload"
              style="width: 100%"
              :min="0"
              :step="1"
            />
          </AFormItem>
        </ACol>
        <ACol :span="12">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="单价"
            name="unitPrice"
          >
            <AInputNumber
              v-model:value="submitForm.unitPrice"
              style="width: 100%"
              :min="0"
              :step="0.01"
              :formatter="
                (value) => `￥${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              "
              :parser="
                (value) => value.replace(/\$\s?|(,*)/g, '').replace(/￥/g, '')
              "
            />
          </AFormItem>
        </ACol>
      </ARow>
      <ARow>
        <ACol :span="24">
          <AFormItem
            :label-col="labelCol"
            has-feedback
            label="总价"
            name="amount"
          >
            <AInput v-model:value="submitForm.amount" autocomplete="off" />
          </AFormItem>
        </ACol>
      </ARow>
    </AForm>
    <ARow type="flex" justify="center">
      <ACol :span="4">
        <AButton @click="doCancel"><CloseOutlined />取消 </AButton>
      </ACol>
      <ACol :span="4">
        <AButton @click="doReset"><RedoOutlined />重置 </AButton>
      </ACol>
      <ACol :span="4">
        <AButton type="primary" @click="toSave">
          <CheckOutlined />保存
        </AButton>
      </ACol>
    </ARow>
  </ACard>
</template>
