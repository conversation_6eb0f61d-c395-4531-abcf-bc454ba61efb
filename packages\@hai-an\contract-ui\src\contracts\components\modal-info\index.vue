<script setup lang="ts">
import type { PropType } from 'vue';

import { ref } from 'vue';

import { TabPane as ATabPane, Tabs as ATabs } from 'ant-design-vue';

import WorkOrderList from '../../../contract-workorder/index.vue';
import ContractInfo from '../info.vue';
import DiaryList from './diary/diary-list.vue';
import JournalList from './journal/index.vue';

const props = defineProps({
  itemId: {
    type: Number as PropType<number>,
    default: 0,
  },
  name: {
    type: String as PropType<string>,
    default: '',
  },
  contractNumber: {
    type: String as PropType<string>,
    default: '1',
  },
});

const activeKey = ref('1');

const diaryType = ref('2');
// const businessId = ref('xm0001')
// const businessName = ref('项目日记')
</script>

<template>
  <div>
    <ATabs v-model:active-key="activeKey">
      <ATabPane key="1" tab="合同信息">
        <ContractInfo :id="itemId" />
      </ATabPane>
      <ATabPane key="2" tab="文档">
        <div>文档</div>
      </ATabPane>
      <ATabPane key="3" tab="日记">
        <DiaryList
          :diary-type="diaryType"
          :business-id="itemId"
          :business-name="name"
        />
      </ATabPane>
      <ATabPane key="4" tab="日志">
        <JournalList :contract-number="props.contractNumber" />
      </ATabPane>
      <ATabPane key="5" tab="流程">
        <WorkOrderList :is-edit="false" :contract-code="props.contractNumber" />
      </ATabPane>
    </ATabs>
  </div>
</template>

<style scoped></style>
