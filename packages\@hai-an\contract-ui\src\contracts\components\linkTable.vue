<!-- 一级仓table -->
<script setup lang="ts">
import { computed, reactive } from 'vue';

import { Table as ATable, Tag as ATag } from 'ant-design-vue';

import columnDefined from './linkTableColumn';

const props = defineProps({
  datas: { default: () => [], type: Array },
  isEidtList: { type: Boolean },
  loading: { type: Boolean },
  pagination: { default: () => ({}), type: Object },
});
const emit = defineEmits(['toEdit', 'toDel', 'handleTableChange', 'toInfo']);

const columns = reactive(columnDefined);

const toInfo = (val: any) => {
  emit('toInfo', val);
};
const handleTableChange = (pagination: any) => {
  emit('handleTableChange', pagination);
};
const datas = computed(() => props.datas);
</script>
<template>
  <ATable
    class="ant-table-striped"
    size="middle"
    :scroll="{ x: 1200 }"
    bordered
    :row-key="(data) => data.id"
    :pagination="pagination"
    :columns="columns"
    :loading="loading"
    @change="handleTableChange"
    :data-source="datas"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'code'">
        <a @click="toInfo({ record })">{{ record.code }}</a>
      </template>
      <template v-if="column.dataIndex === 'contractType'">
        <ATag :color="record.contractType ? 'green' : 'geekblue'">
          {{ record.contractType ? '付款合同' : '收款合同' }}
        </ATag>
      </template>
    </template>
  </ATable>
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
