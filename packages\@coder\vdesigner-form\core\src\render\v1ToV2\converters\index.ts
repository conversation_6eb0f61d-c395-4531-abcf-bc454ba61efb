import type { V1Convert } from './type';

import { DatePickerFieldConvert, DatePickerRangerConvert } from './datePicker';
import { DividerConvert } from './divider';
import { SwfAttachmentListConvert } from './fileInfo';
import { RowGridConvert } from './gird';
import { InputConvert } from './input';
import { LabelFromConvert } from './labelFrom';
import { NotfoundConvert } from './not-found-convert';
import { PrintPdfButtonConvert } from './printPdf';
import { RadioConvert } from './radio';
import { SwfTextPreview, SwfTextUploador } from './swf-textUploador';
import { SwfUploadAttachConvert } from './swf-upload-attach';
import { TextareaConvert } from './textarea';
import { UserLabelConvert } from './userLabel';
import { UserSelectConvert } from './userSelect';

const NotFound = new NotfoundConvert();
const _convertMap = [
  new DatePickerFieldConvert(),
  new DatePickerRangerConvert(),
  new InputConvert(),
  new LabelFromConvert(),
  // new DragGridConvert(),
  new RowGridConvert(),
  new SwfUploadAttachConvert(),
  new TextareaConvert(),
  new UserSelectConvert(),
  new RadioConvert(),
  new SwfTextUploador(),
  new SwfTextPreview(),
  new PrintPdfButtonConvert(),
  new SwfAttachmentListConvert(),
  new UserLabelConvert(),

  new DividerConvert(),
];

export const getConverter = (v1Type: string): V1Convert => {
  const r = _convertMap.find((_) => _.v1Type === v1Type);
  if (r === undefined) {
    console.error(`${v1Type}不存在转换器`);
    return NotFound;
  }
  return r;
};

export const addConverter = (convert: V1Convert) => {
  _convertMap.push(convert);
};
