import type { Widget } from '../../../widgets';

import { V1Convert } from './type';

export class SwitchConvert extends V1Convert {
  constructor() {
    super('switch', 'CoderVDesignSwitch');
  }
  override SetOption(
    v1Widget: any,
    v2: Widget,
    _cfg: Record<string, any>,
  ): void {
    const v1 = v1Widget.options;
    // eslint-disable-next-line no-console
    console.log('switch', v1);
    v2.options = {
      changeEvent: undefined,
      defaultValue: 'none',
      disabled: v1.disabled,
      hidden: v1.hidden,
      label: v1.labelHidden ? '' : v1.label,
      labelHidden: false,
      name: v1.name,
    };
  }
}
