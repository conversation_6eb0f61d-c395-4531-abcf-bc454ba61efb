<script setup lang="ts">
import type { PropType } from 'vue';

import { computed, ref, unref } from 'vue';

import {
  List as AList,
  ListItem as AListItem,
  ListItemMeta as AListItemMeta,
  Tag as ATag,
  TypographyParagraph as ATypographyParagraph,
} from 'ant-design-vue';

import { isNumber, textToCircularSvg } from './utils';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: [Number, Boolean] as PropType<boolean | number>,
    default: 5,
  },
  titleRows: {
    type: Number,
    default: 1,
  },
  descRows: {
    type: Number,
    default: 2,
  },
  onTitleClick: {
    type: Function as PropType<(Recordable: any) => void>,
    default: null,
  },
});
const emit = defineEmits(['update:currentPage']);
const current = ref(props.currentPage || 1);

const getPagination = computed(() => {
  const { list, pageSize } = props as any;
  /* eslint-disable unicorn/prefer-ternary */
  if (pageSize > 0 && list && list.length > pageSize) {
    return {
      total: list.length,
      pageSize,
      // size: 'small',
      current: unref(current),
      onChange(page: number) {
        current.value = page;
        emit('update:currentPage', page);
      },
    };
  } else {
    return false;
  }
  /* eslint-enable unicorn/prefer-ternary */
});
const isTitleClickable = computed(() => !!props.onTitleClick);
const renderList = computed(() => {
  const { pageSize, list } = props;
  if (pageSize === false) return [];
  const size = isNumber(pageSize) ? pageSize : 5;
  return list.slice(size * (unref(current) - 1), size * unref(current));
}) as any;

const handleTitleClick = (item: any) => {
  item.titleDelete = !item.titleDelete;
  props.onTitleClick && props.onTitleClick(item);
};
</script>

<template>
  <AList bordered :pagination="getPagination">
    <template v-for="item in renderList" :key="item.id">
      <AListItem>
        <AListItemMeta>
          <template #title>
            <div class="title">
              <ATypographyParagraph
                @click="handleTitleClick(item)"
                style="width: 100%; margin-bottom: 0 !important"
                :style="{ cursor: isTitleClickable ? 'pointer' : '' }"
                :delete="!!item.titleDelete"
                :ellipsis="
                  $props.titleRows && $props.titleRows > 0
                    ? { rows: $props.titleRows, tooltip: !!item.title }
                    : false
                "
                :content="item.title"
              />
              <div class="extra" v-if="item.extra">
                <ATag class="tag" :color="item.color">{{ item.extra }}</ATag>
              </div>
            </div>
          </template>
          <template #avatar>
            <img
              v-if="item.avatar"
              :src="item.avatar"
              class="aspect-square h-full w-full object-cover"
              role="img"
            />
            <img
              v-else
              :src="
                textToCircularSvg(
                  item.title?.length > 1 ? item.title.substring(0, 1) : '消',
                )
              "
              class="aspect-square h-full w-full object-cover"
              role="img"
            />
          </template>
          <template #description>
            <div>
              <div class="description" v-if="item.description">
                <ATypographyParagraph
                  style="width: 100%; margin-bottom: 0 !important"
                  :ellipsis="
                    $props.descRows && $props.descRows > 0
                      ? { rows: $props.descRows, tooltip: !!item.description }
                      : false
                  "
                  :content="item.description"
                />
              </div>
              <div class="datetime">
                {{ item.datetime }}
              </div>
            </div>
          </template>
        </AListItemMeta>
      </AListItem>
    </template>
  </AList>
</template>

<style scoped lang="scss">
.title {
  font-weight: normal;
  cursor: pointer;
  // text-decoration:line-through;
  .extra {
    // float: right;
    margin-top: -1.5px;
    margin-right: 0;
    font-weight: normal;

    .tag {
      margin-right: 0;
    }
  }
}

.description {
  font-size: 12px;
  line-height: 18px;
}

.datetime {
  margin-top: 4px;
  font-size: 12px;
  line-height: 18px;
}
</style>
