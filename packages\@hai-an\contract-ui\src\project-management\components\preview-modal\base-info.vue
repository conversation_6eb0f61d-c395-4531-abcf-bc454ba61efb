<script setup lang="ts">
import type { ECharts } from 'echarts/core';

import { computed, onBeforeUnmount, onMounted, reactive, ref } from 'vue';

import {
  haianContractOption as apiOptions,
  createProjectManagementApi,
} from '@hai-an/contract-api';
import {
  Col as ACol,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Row as ARow,
  Statistic as AStatistic,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { Bar<PERSON>hart, GaugeChart, PieChart } from 'echarts/charts';
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';

const props = defineProps({
  itemId: { type: Number, default: 0 },
});

// 注册必需的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
]);

const api = createProjectManagementApi(apiOptions.request, apiOptions.path);
// 图表DOM引用
const pieChartRef = ref(null);
const barChartRef = ref(null);
const gaugeChartRef = ref(null);
let pieChart: ECharts | null = null;
let barChart: ECharts | null = null;
let gaugeChart: ECharts | null = null;

// 表单数据
const projectArchiveInfo = reactive({
  id: 0,
  name: '',
  manager: '',
  phone: '',
  code: '',
  createBy: '',
  createTime: null,
  // 添加财务数据字段
  projectAmount: 0, // 项目金额
  collectedAmount: 0, // 收款金额
  receivedAmount: 0, // 已收金额
  unCollectedAmount: 0, // 未收金额
  paymentAmount: 0, // 付款金额
  paidAmount: 0, // 已付金额
  unPaymentAmount: 0, // 未付金额
  totalProfit: 0, // 总利润
});

// 收款率计算
const calcCollectionRate = computed(() => {
  if (!projectArchiveInfo.collectedAmount) return 0;
  return (
    (projectArchiveInfo.receivedAmount / projectArchiveInfo.collectedAmount) *
    100
  );
});

// 支付率计算
const calcPaymentRate = computed(() => {
  if (!projectArchiveInfo.paymentAmount) return 0;
  return (
    (projectArchiveInfo.paidAmount / projectArchiveInfo.paymentAmount) * 100
  );
});

// 格式化货币
const formatCurrency = (value: number | string) => {
  if (!value) return '0';
  return Number(value).toFixed(2);
};

const dayF = (val: Date | number | string) => {
  return dayjs(val).format('YYYY-MM-DD HH:mm');
};

// 初始化收款饼图
const initPieChart = () => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value);
    const option = {
      title: {
        text: '项目收款情况',
        left: 'center',
        textStyle: {
          color: '#1890ff',
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} 元 ({d}%)',
        backgroundColor: 'rgba(255,255,255,0.85)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#333',
        },
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['已收金额', '未收金额'],
        textStyle: {
          color: '#666',
        },
      },
      series: [
        {
          name: '收款情况',
          type: 'pie',
          radius: '60%',
          center: ['50%', '50%'],
          data: [
            { value: projectArchiveInfo.receivedAmount, name: '已收金额' },
            { value: projectArchiveInfo.unCollectedAmount, name: '未收金额' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          itemStyle: {
            color(params: any) {
              const colorList = ['#91cc75', '#ee6666'];
              return colorList[params.dataIndex];
            },
          },
        },
      ],
    };
    pieChart.setOption(option);
  }
};

// 初始化付款柱状图
const initBarChart = () => {
  if (barChartRef.value) {
    barChart = echarts.init(barChartRef.value);
    const option = {
      title: {
        text: '项目付款情况',
        left: 'center',
        textStyle: {
          color: '#1890ff',
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(255,255,255,0.85)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#333',
        },
        formatter: '{b}: {c} 元',
      },
      legend: {
        data: ['付款金额', '已付金额', '未付金额'],
        bottom: '0%',
        textStyle: {
          color: '#666',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['付款总额', '已付金额', '未付金额'],
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value} 元',
        },
      },
      series: [
        {
          name: '金额',
          type: 'bar',
          barWidth: '40%',
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          data: [
            {
              value: projectArchiveInfo.paymentAmount,
              itemStyle: { color: '#5470c6' },
              name: '付款总额',
            },
            {
              value: projectArchiveInfo.paidAmount,
              itemStyle: { color: '#91cc75' },
              name: '已付金额',
            },
            {
              value: projectArchiveInfo.unPaymentAmount,
              itemStyle: { color: '#fac858' },
              name: '未付金额',
            },
          ],
        },
      ],
    };
    barChart.setOption(option);
  }
};

// 初始化项目总利润仪表盘
const initGaugeChart = () => {
  if (gaugeChartRef.value) {
    gaugeChart = echarts.init(gaugeChartRef.value);
    // 计算利润率
    let profitRate = 0;
    if (projectArchiveInfo.projectAmount > 0) {
      profitRate =
        (projectArchiveInfo.totalProfit / projectArchiveInfo.projectAmount) *
        100;
    }

    const option = {
      title: {
        text: '项目利润率',
        left: 'center',
        textStyle: {
          color: '#1890ff',
          fontSize: 16,
        },
      },
      tooltip: {
        formatter: '{b}: {c}%',
        backgroundColor: 'rgba(255,255,255,0.85)',
        borderWidth: 1,
        borderColor: '#ccc',
        padding: 10,
        textStyle: {
          color: '#333',
        },
      },
      series: [
        {
          name: '利润率',
          type: 'gauge',
          detail: {
            formatter: '{value}%',
            fontSize: 18,
            fontWeight: 'bold',
            color(value: number) {
              if (value < 30) return '#ee6666';
              else if (value < 60) return '#fac858';
              else return '#91cc75';
            },
          },
          data: [
            {
              value: profitRate.toFixed(2),
              name: '利润率',
              title: {
                offsetCenter: [0, '80%'],
                fontSize: 14,
                color: '#666',
              },
              detail: {
                offsetCenter: [0, '110%'],
              },
            },
          ],
          axisLine: {
            lineStyle: {
              width: 30,
              color: [
                [0.3, '#ee6666'],
                [0.6, '#fac858'],
                [1, '#91cc75'],
              ],
            },
          },
          title: {
            fontSize: 14,
          },
        },
      ],
    };

    gaugeChart.setOption(option);
  }
};

// 初始化所有图表
const initCharts = () => {
  setTimeout(() => {
    initPieChart();
    initBarChart();
    initGaugeChart();
  }, 100);
};

// 在窗口大小变化时调整图表大小
const resizeCharts = () => {
  pieChart && pieChart.resize();
  barChart && barChart.resize();
  gaugeChart && gaugeChart.resize();
};

window.addEventListener('resize', resizeCharts);

// 组件卸载前销毁图表实例
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeCharts);
  pieChart && pieChart.dispose();
  barChart && barChart.dispose();
  gaugeChart && gaugeChart.dispose();
});

// 加载数据并初始化图表
const reload = () => {
  if (props.itemId) {
    // 尝试获取详细的数据
    api.getData(props.itemId).then((res) => {
      Object.assign(projectArchiveInfo, res);
      initCharts();
    });
  }
};

onMounted(() => {
  reload();
});
</script>
<template>
  <!-- 项目基础信息 -->
  <div class="project-info-container">
    <h3 class="section-title">项目基本信息</h3>
    <ADescriptions
      bordered
      :column="{ xs: 1, sm: 2, md: 3 }"
      size="middle"
      class="custom-descriptions"
    >
      <ADescriptionsItem label="项目编号" class="description-item">
        {{ projectArchiveInfo.code || '- -' }}
      </ADescriptionsItem>
      <ADescriptionsItem label="项目名称" class="description-item">
        {{ projectArchiveInfo.name || '- -' }}
      </ADescriptionsItem>
      <ADescriptionsItem label="负责人" class="description-item">
        {{ projectArchiveInfo.manager || '- -' }}
      </ADescriptionsItem>
      <ADescriptionsItem label="负责人电话" class="description-item">
        {{ projectArchiveInfo.phone || '- -' }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建人" class="description-item">
        {{ projectArchiveInfo.createBy || '- -' }}
      </ADescriptionsItem>
      <ADescriptionsItem label="创建日期" class="description-item">
        {{
          projectArchiveInfo.createTime
            ? dayF(projectArchiveInfo.createTime)
            : '- -'
        }}
      </ADescriptionsItem>
    </ADescriptions>
    <!-- 添加项目财务数据可视化图表 -->
    <div class="project-info-container">
      <h3 class="section-title">项目财务数据概览</h3>
      <!-- 财务数据总览 -->
      <ARow :gutter="[16, 16]" style="margin-bottom: 20px">
        <ACol :span="6">
          <AStatistic
            title="项目金额"
            :value="formatCurrency(projectArchiveInfo.projectAmount)"
            :precision="2"
            suffix="元"
            :value-style="{ color: '#3f8600', fontWeight: 'bold' }"
          />
        </ACol>
        <ACol :span="6">
          <AStatistic
            title="总利润"
            :value="formatCurrency(projectArchiveInfo.totalProfit)"
            :precision="2"
            suffix="元"
            :value-style="{
              color:
                projectArchiveInfo.totalProfit >= 0 ? '#3f8600' : '#cf1322',
            }"
          />
        </ACol>
        <ACol :span="6">
          <AStatistic
            title="收款率"
            :value="calcCollectionRate"
            :precision="2"
            suffix="%"
            :value-style="{ color: '#1890ff' }"
          />
        </ACol>
        <ACol :span="6">
          <AStatistic
            title="支付率"
            :value="calcPaymentRate"
            :precision="2"
            suffix="%"
            :value-style="{ color: '#722ed1' }"
          />
        </ACol>
      </ARow>
      <!-- 收款情况图表 -->
      <div class="chart-container" style="height: 300px; margin-bottom: 20px">
        <div ref="pieChartRef" style="width: 100%; height: 100%"></div>
      </div>

      <!-- 付款情况与总览图表 -->
      <ARow :gutter="[16, 16]">
        <ACol :span="12">
          <!-- 项目付款情况 -->
          <div
            class="chart-container"
            style="height: 300px; margin-bottom: 20px"
          >
            <div ref="barChartRef" style="width: 100%; height: 100%"></div>
          </div>
        </ACol>
        <ACol :span="12">
          <!-- 项目利润率 -->
          <div
            class="chart-container"
            style="height: 300px; margin-bottom: 20px"
          >
            <div ref="gaugeChartRef" style="width: 100%; height: 100%"></div>
          </div>
        </ACol>
      </ARow>
    </div>
  </div>
  <!-- 项目基础信息显示无需底部按钮 -->
</template>
<style scoped>
.chart-container {
  padding: 10px;
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.project-info-container {
  padding: 16px;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.section-title {
  padding-left: 12px;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 500;
  color: #1890ff;
  border-left: 4px solid #1890ff;
}

:deep(.custom-descriptions .ant-descriptions-header) {
  margin-bottom: 16px;
}

:deep(.custom-descriptions .ant-descriptions-item-label) {
  width: 120px;
  font-weight: 500;
  color: #666;
  text-align: center;
  background-color: #fafafa;
}

:deep(.custom-descriptions .ant-descriptions-item-content) {
  padding: 12px;
  color: #333;
}

:deep(.custom-descriptions .description-item) {
  padding: 8px 0;
}
</style>
