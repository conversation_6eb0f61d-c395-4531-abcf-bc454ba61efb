<script setup lang="ts">
import type {
  ProjectSearch,
  ProjectViewModel,
} from '@hai-an/contract-api/src/types/project';

import { computed, onMounted, reactive, ref } from 'vue';

import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { createProjectApi, haianContractOption } from '@hai-an/contract-api';
import { Button, InputGroup, Select, Tooltip } from 'ant-design-vue';

import { showAdd } from './list/useModal';

type ProjectSelectOption = {
  key: number;
  label: string;
  project: ProjectViewModel;
  value: string;
};
const props = defineProps({
  modelValue: { default: '', type: String },
});
const emit = defineEmits(['update:modelValue', 'change']);

const api = createProjectApi(
  haianContractOption.request,
  haianContractOption.path,
);

const loading = ref(false);

const searchForm = reactive({
  isDeleted: false,
  page: 1,
  pageSize: 50,
} as ProjectSearch);

const projectInfo = computed({
  get: () => {
    return props.modelValue;
  },
  set: (v) => {
    emit('update:modelValue', v);
  },
});
const projectDataSource = ref<ProjectSelectOption[]>(Array.from({ length: 0 }));

const search = (code = '') => {
  loading.value = true;
  api.list(searchForm).then((res) => {
    const projects: ProjectSelectOption[] = [];

    res.forEach((element) => {
      if (code && code === element.code) {
        emit('update:modelValue', element.code + element.id);
        emit('change', { ...element });
      }
      projects.push({
        key: element.id,
        label: `${element.name}(${element.code})`,
        project: element,
        value: element.code + element.id,
      });
    });
    projectDataSource.value.splice(0);
    projectDataSource.value.push(...projects);
    loading.value = false;
  });
};
const handleSearch = (val: any) => {
  searchForm.name = val;
  search();
};
const handleChange = (val: any) => {
  for (let i = 0; i < projectDataSource.value.length; i++) {
    const data = projectDataSource.value[i];
    if (data === val) {
      emit('change', data?.project);
      break;
    }
  }
};

const toNew = () => {
  showAdd(() => {
    search();
  });
};

onMounted(() => {
  projectInfo.value = props.modelValue;
  search();
});
</script>
<template>
  <InputGroup compact>
    <Select
      v-model:value="projectInfo"
      :default-active-first-option="false"
      :filter-option="false"
      :loading="loading"
      :not-found-content="null"
      :options="projectDataSource"
      placeholder="请输入项目名称"
      show-search
      style="width: 210px"
      @change="handleChange"
      @search="handleSearch"
      allow-clear
    >
      <template #suffixIcon>
        <SearchOutlined />
      </template>
    </Select>
    <Tooltip title="新增项目">
      <Button type="link" @click="toNew">
        <PlusOutlined />
      </Button>
    </Tooltip>
  </InputGroup>
</template>
