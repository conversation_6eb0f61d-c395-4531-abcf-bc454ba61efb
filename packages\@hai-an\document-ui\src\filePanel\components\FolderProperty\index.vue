<script setup lang="ts">
import type { FolderListItem } from '@coder/document-api';

import { computed, ref } from 'vue';

import { Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{ folder: FolderListItem }>();

const visible = ref(false);
const folder = computed(() => props.folder);

defineExpose({
  show: () => {
    visible.value = true;
  },
});
</script>

<template>
  <Modal
    v-model:open="visible"
    cancel-text="取消  "
    ok-text="确定"
    title="文件详情"
  >
    <div class="file-details">
      <div class="detail-group">
        <div class="detail-label">文件名称</div>
        <div class="detail-value folder-name">{{ folder.name }}</div>
      </div>

      <div class="detail-group">
        <div class="detail-label">创建时间</div>
        <div class="detail-value">
          {{ dayjs(folder.createTime).format('YYYY-MM-DD hh:mm:ss') }}
        </div>
      </div>

      <div class="detail-group">
        <div class="detail-label">创建人</div>
        <div class="detail-value">{{ folder.createUser }}</div>
      </div>

      <div class="detail-group">
        <div class="detail-label">更新时间</div>
        <div class="detail-value">
          {{ dayjs(folder.createTime).format('YYYY-MM-DD hh:mm:ss') }}
        </div>
      </div>
    </div>
  </Modal>
</template>
<style scoped>
.file-details {
  max-width: 400px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
}

.detail-group {
  margin-bottom: 24px;
}

.detail-group:last-child {
  margin-bottom: 0;
}

.detail-label {
  margin-bottom: 4px;
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 16px;
}

.folder-name {
  font-size: 20px;
  font-weight: 500;
}
</style>
