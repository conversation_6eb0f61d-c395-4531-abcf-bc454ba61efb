<script setup lang="ts">
import { reactive } from 'vue';

import { login } from '@coder/system-api';
import {
  Button as AButton,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  message,
} from 'ant-design-vue';

import { createRequestClient } from './request';

const formInline = reactive({
  password: 'Pass!23',
  secretKey: '',
  userName: 'admin',
});

const onSubmit = () => {
  const data = {
    password: formInline.password,
    secretKey: '',
    username: formInline.userName,
  };

  const loginClient = createRequestClient('http://localhost:5006/auth/');

  login(loginClient, 'login', data as any).then((resp) => {
    message.info('登录成功');

    window.localStorage.setItem('test_token', `${resp.accessToken}`);
  });
};
</script>
<template>
  <AForm class="demo-form-inline" layout="inline">
    <AFormItem label="userName:">
      <AInput v-model:value="formInline.userName" placeholder="Approved by" />
    </AFormItem>
    <AFormItem label="密码:">
      <AInput v-model:value="formInline.password" placeholder="Approved by" />
    </AFormItem>
    <AFormItem>
      <AButton type="primary" @click="onSubmit">登录</AButton>
    </AFormItem>
  </AForm>
</template>
