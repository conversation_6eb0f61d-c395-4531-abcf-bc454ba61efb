<!-- 供应商管理 -->
<script setup lang="ts">
import { createVNode, onMounted, reactive, ref } from 'vue';

import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import {
  createInvoiceApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  <PERSON><PERSON> as <PERSON>utton,
  Card as ACard,
  Modal as AModal,
  message,
} from 'ant-design-vue';

import ContractEidt from './components/edit.vue';
import ContractForm from './components/form.vue';
import ContractInfo from './components/info.vue';
import ContractTable from './components/table.vue';

const api = createInvoiceApi(options.path, options.request);
const editTitle = ref('');
const infoTitle = ref('');
const isInfo = ref(false);
const isEdit = ref(false);
const loading = ref(false);
const editId = ref(0);
const infoId = ref(0);

const searchForm = reactive({
  page: 0,
  pageSize: 10,
});

const datas = reactive([]);
const pagination = reactive({
  pageSize: 10, // 每页中显示10条数据
  pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total) => `共有 ${total} 条数据`, // 分页中显示总的数据
  total: 0,
});
const filter = () => {
  loading.value = true;
  api.List(searchForm).then((res) => {
    datas.splice(0);
    datas.push(...res.data);
    loading.value = false;
  });
  api.Count(searchForm).then((res) => {
    pagination.total = res.data;
  });
};
const search = () => {
  searchForm.page = 1;
  filter();
};

const onAdd = () => {
  editTitle.value = '新增发票信息';
  isEdit.value = true;
  editId.value = 0;
};
const onEdit = (val) => {
  editTitle.value = '编辑发票信息';
  isEdit.value = true;
  editId.value = val.record.id;
};
const onInfo = (val) => {
  infoTitle.value = '发票详细信息';
  isInfo.value = true;
  infoId.value = val.record.id;
};
const canceled = () => {
  isEdit.value = false;
  isInfo.value = false;
};
const saved = () => {
  canceled();
  filter();
};

const onSearch = (val) => {
  Object.assign(searchForm, val); // 赋值到searchForm
  search();
};

// 分页按钮调用
const handleTableChange = (val) => {
  pagination.current = val.current;
  pagination.pageSize = val.pageSize;
  searchForm.page = val.current;
  searchForm.pageSize = val.pageSize;
  filter();
};

const onDel = (val) => {
  AModal.confirm({
    cancelText: '取消',
    content: () =>
      createVNode(
        'div',
        { style: 'color:red;' },
        '是否确定删除选定信息?删除后将无法恢复！',
      ),
    icon: () => createVNode(ExclamationCircleOutlined),
    okText: '确认删除',
    onCancel() {
      message.info('取消删除！');
    },
    onOk() {
      api.Delete(val.id).then((res) => {
        if (res.success) {
          message.success(res.message);
          filter();
        } else {
          message.error(res.message);
        }
      });
    },
    title: () => '删除确认',
  });
};

onMounted(() => {
  search();
});
</script>
<template>
  <ACard>
    <ContractForm @on-search="onSearch" />
  </ACard>
  <ACard>
    <div class="space-align-container">
      <AButton type="primary" shape="round" @click="onAdd" size="small">
        <PlusOutlined />新增
      </AButton>
    </div>
    <ContractTable
      :datas="datas"
      :pagination="pagination"
      :loading="loading"
      @to-edit="onEdit"
      @to-del="onDel"
      @to-info="onInfo"
      @handle-table-change="handleTableChange"
    />
  </ACard>
  <AModal
    :title="editTitle"
    :width="1000"
    :open="isEdit"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <ContractEidt :id="editId" @do-save="saved" @do-cancel="canceled" />
  </AModal>

  <AModal
    :title="infoTitle"
    :width="1200"
    :open="isInfo"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <ContractInfo :id="infoId" @do-cancel="canceled" />
  </AModal>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
