import type { RequestClient } from '@vben/request';

import type {
  AcceptResult,
  FileAttach,
  ListWorkActivityByProcessInstanceSearch,
  SimpleWordsProcess,
  TagSubmit,
  WorkActivityListItemViewModel,
  WorkActivitySearcher,
  WorkActivityViewModel,
} from '../types';
import type {
  AbandonResult,
  CreateProcessInstanceResult,
  CreateProcessInstanceSubmit,
  FlowViewModel,
  InstanceListItemViewModel,
  ProcessInstanceOperatorResult,
  ProcessInstanceSearcher,
  ProcessInstanceViewModel,
  ResolveResult,
  StartResult,
  SuspendCommentSubmit,
  SwfExecuteResult,
  WorkflowResolveSubmit,
} from '../types/processInstance';

/**
 * 创建工作流API服务
 * @param request HTTP请求客户端
 * @param host 服务器主机地址
 * @returns 工作流API方法集合
 */
export const createWorkflowApi = (request: RequestClient, host: string) => {
  const path = `${host}/workflow`;
  return {
    /**
     * 接受工作活动
     * @param activityId 活动ID
     * @returns 接受操作结果
     */
    acceptWorkActivity(activityId: number): Promise<AcceptResult> {
      return request.put<AcceptResult>(`${path}/accept/${activityId}`);
    },

    /**
     * 取消流程实例
     * @param id 流程实例ID
     * @returns 流程实例操作结果
     */
    cancel(id: number): Promise<ProcessInstanceOperatorResult> {
      return request.put(`${path}/cancel/${id}`);
    },

    /**
     * 统计流程实例数量
     * @param searcher 流程实例搜索条件
     * @returns 流程实例数量
     */
    countProcessInstance(searcher: ProcessInstanceSearcher): Promise<number> {
      return request.get(`${path}/count-process-instance`, {
        params: searcher,
        paramsSerializer: 'repeat',
      });
    },

    /**
     * 根据流程实例ID和搜索条件统计工作活动数量
     * @param processIntanceId 流程实例ID
     * @param searcher 搜索条件
     * @returns 工作活动数量
     */
    countWorkActivitesByInstance(
      processIntanceId: number,
      searcher: ListWorkActivityByProcessInstanceSearch,
    ): Promise<number> {
      return request.get<number>(
        `${path}/count-work-activity-by-process-instance-id/${processIntanceId}`,
        { params: searcher, paramsSerializer: 'repeat' },
      );
    },

    /**
     * 统计工作活动的数量
     * @param searcher 搜索器，用于过滤工作活动
     * @returns 工作活动的数量
     */
    countWorkActivity(searcher: WorkActivitySearcher): Promise<number> {
      return request.get(`${path}/count-work-activity`, {
        params: searcher,
        paramsSerializer: 'repeat',
      });
    },

    /**
     * 创建流程实例
     * @param createModel 创建流程实例的提交数据
     * @returns 创建结果
     */
    create(
      createModel: CreateProcessInstanceSubmit,
    ): Promise<CreateProcessInstanceResult> {
      return request.post(`${path}/create`, createModel);
    },

    /**
     * 删除文件
     * @param fileId 文件ID
     * @param processInstanceId 流程实例ID
     * @returns 删除操作结果
     */
    deleteFile(fileId: number, processInstanceId: number): Promise<any> {
      return request.delete(
        `${path}/deleteFile/${fileId}/${processInstanceId}`,
      );
    },

    /**
     * 删除流程实例
     * @param processInstanceId 流程实例ID
     * @returns 流程实例操作结果
     */
    deleteProcessInstance(
      processInstanceId: number,
    ): Promise<ProcessInstanceOperatorResult> {
      return request.delete(`${path}/delete/${processInstanceId}`);
    },

    /**
     * 获取附件下载链接
     * @param fileId 文件系统ID（非附件ID或正文ID）
     * @returns 下载链接
     */
    downloadAttachmentUrl(fileId: string): string {
      const result = `${path}/get-file-by-id/${fileId}`;
      if (result.startsWith('http')) {
        return result;
      }
      return `api/${result}`;
    },

    /**
     * 根据ID获取流程实例详情
     * @param id 流程实例ID
     * @returns 流程实例视图模型
     */
    get: (id: number | string): Promise<ProcessInstanceViewModel> => {
      return request.get<ProcessInstanceViewModel>(`${path}/${id}`);
    },

    /**
     * 根据流程实例ID获取文件列表
     * @param id 流程实例ID
     * @returns 文件附件数组
     */
    getFilesByProcessInstanceId(
      id: number | string,
    ): Promise<Array<FileAttach>> {
      return request.get(`${path}/get-files-by-processInstanceId/${id}`);
    },

    /**
     * 根据流程实例ID获取正文文件信息
     * @param processInstanceId 流程实例ID
     * @returns 包含文件文本信息的Promise对象
     */
    getFilesTextByProcessInstanceId(
      processInstanceId: number | string,
    ): Promise<FileAttach[]> {
      return request.get(
        `${path}/get-files-text-by-processInstanceId/${processInstanceId}`,
      );
    },

    /**
     * 获取流程实例的标签
     * @param processInstanceId 流程实例ID
     * @returns 标签数组
     */
    getTags(processInstanceId: number): Promise<Array<TagSubmit>> {
      return request.get(`${path}/${processInstanceId}/tags`);
    },

    /**
     * 获取预览票据
     * @param id 流程实例ID
     * @returns 票据对象
     */
    getTicket(id: number | string): Promise<{ ticket: string }> {
      return request.get(`${path}/get-preview-ticket/${id}`);
    },

    /**
     * 获取工作活动详情
     * @param workActivityId 工作活动ID
     * @returns 工作活动视图模型
     */
    getWorkActivity: (
      workActivityId: number | string,
    ): Promise<WorkActivityViewModel> => {
      return request.get<WorkActivityViewModel>(
        `${path}/work-activity/${workActivityId}`,
      );
    },

    /**
     * 根据流程实例ID获取工作流程
     * @param processInstanceId 流程实例ID
     * @returns 简单工作流程信息
     */
    getWorkProcessByProcessInstanceId(
      processInstanceId: number,
    ): Promise<SimpleWordsProcess> {
      return request.get<SimpleWordsProcess>(
        `${path}/work-process-flow/${processInstanceId}`,
      );
    },

    /**
     * 放弃工作活动
     * @param activityId 活动ID
     * @returns 放弃操作结果
     */
    giveUpWorkActivity(activityId: number): Promise<AbandonResult> {
      return request.put<AbandonResult>(`${path}/Abandon/${activityId}`);
    },

    /**
     * 解决/处理工作流程
     * @param id 流程实例ID
     * @param submit 工作流解决提交数据
     * @returns 解决操作结果
     */
    resolve(id: number, submit: WorkflowResolveSubmit): Promise<ResolveResult> {
      return request.post(`${path}/resolve/${id}`, submit);
    },

    /**
     * 恢复已暂停的流程实例
     * @param id 流程实例ID
     * @param submit 暂停评论提交数据
     * @returns 流程实例操作结果
     */
    resume(
      id: number,
      submit: SuspendCommentSubmit,
    ): Promise<ProcessInstanceOperatorResult> {
      return request.put(`${path}/resume/${id}`, submit);
    },

    /**
     * 保存表单数据
     * @param processInstanceId 流程实例ID
     * @param form 表单数据
     * @returns 工作流执行结果
     */
    saveForm(
      processInstanceId: number,
      form: Record<string, any>,
    ): Promise<SwfExecuteResult> {
      return request.put<SwfExecuteResult>(
        `${path}/save-form/${processInstanceId}`,
        {
          form: JSON.stringify(form),
        },
      );
    },

    /**
     * 保存工作活动表单数据
     * @param workActivityId 工作活动ID
     * @param form 表单对象
     * @param debuggerUser 调试用户（可选）
     * @returns 工作流执行结果
     */
    saveWorkActivityForm(
      workActivityId: number | string,
      form: object,
      debuggerUser?: string,
    ): Promise<SwfExecuteResult> {
      return request.put(
        `${path}/save-form-by-work-activity/${workActivityId}`,
        { form: JSON.stringify(form), submitFormUser: debuggerUser },
      );
    },

    /**
     * 启动流程实例
     * @param id 流程实例ID
     * @returns 启动操作结果
     */
    start(id: number): Promise<StartResult> {
      return request.put(`${path}/start/${id}`);
    },

    /**
     * 暂停流程实例
     * @param id 流程实例ID
     * @param submit 暂停评论提交数据
     * @returns 流程实例操作结果
     */
    suspend(
      id: number,
      submit: SuspendCommentSubmit,
    ): Promise<ProcessInstanceOperatorResult> {
      return request.put(`${path}/suspend/${id}`, submit);
    },

    /**
     * 获取上传附件的URL路径（非正文）
     * @param processInstanceId 流程实例ID
     * @returns 上传路径
     */
    uploadAttachmentUrl(processInstanceId: number): string {
      return `${path}/upload/${processInstanceId}`;
    },

    /**
     * 获取流程流转记录列表
     * @param processInstanceId 流程实例ID
     * @param showAllHisotry 是否显示所有历史记录
     * @returns 流程视图模型数组
     */
    listFlow(
      processInstanceId: number,
      showAllHisotry: boolean,
    ): Promise<Array<FlowViewModel>> {
      return request.get(
        `${path}/list-flow/${processInstanceId}?all=${showAllHisotry}`,
      );
    },

    /**
     * 获取流程实例列表
     * @param searcher 流程实例搜索条件
     * @returns 流程实例列表项视图模型数组
     */
    listProcessInstance(
      searcher: ProcessInstanceSearcher,
    ): Promise<Array<InstanceListItemViewModel>> {
      return request.get(`${path}/list-process-instance`, {
        params: searcher,
        paramsSerializer: 'repeat',
      });
    },

    /**
     * 根据流程实例ID获取工作活动列表
     * @param processInstanceId 流程实例ID
     * @param searcher 搜索条件
     * @returns 工作活动列表项视图模型数组
     */
    listWorkActivitesByInstance(
      processInstanceId: number,
      searcher: ListWorkActivityByProcessInstanceSearch,
    ): Promise<WorkActivityListItemViewModel[]> {
      return request.get<WorkActivityListItemViewModel[]>(
        `${path}/list-work-activity-by-process-instance-id/${processInstanceId}`,
        { params: searcher, paramsSerializer: 'repeat' },
      );
    },

    /**
     * 列出工作活动
     * @param searcher
     * @returns
     */
    listWorkActivity(
      searcher: WorkActivitySearcher,
    ): Promise<Array<WorkActivityViewModel>> {
      return request.get(`${path}/list-work-activity`, {
        params: searcher,
        paramsSerializer: 'repeat',
      });
    },
  };
};
