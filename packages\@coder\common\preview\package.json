{"name": "@coder/preview", "version": "0.1.0", "private": false, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "main": "./src/index.ts", "typings": "./src/index.ts", "publishConfig": {"main": "./dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs"}}, "typings": "./dist/index.d.ts"}, "dependencies": {"@coder/code-editor": "workspace:*", "@coder/common-api": "workspace:*", "@coder/monaco-editor-builder": "workspace:^", "@coder/object-editor": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vue-office/excel": "^1.7.14", "ant-design-vue": "catalog:", "axios": "catalog:", "docx-preview": "^0.3.5", "lodash-es": "catalog:coder", "monaco-editor": "catalog:", "pptx-preview": "^1.0.5", "vue": "catalog:"}, "devDependencies": {"@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:^", "@vben/vite-config": "workspace:*"}}