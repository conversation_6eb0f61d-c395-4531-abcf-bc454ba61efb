<!-- 一级仓table -->
<script setup lang="ts">
import type { CarSubmit } from '@hai-an/car-api';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import {
  <PERSON><PERSON> as <PERSON><PERSON>on,
  Divider as ADivider,
  Table as ATable,
  Tag as ATag,
} from 'ant-design-vue';

import { columns } from './column';

defineProps({
  data: { default: () => {}, type: Array },
  loading: { type: Boolean },
  pagination: { default: () => {}, type: Object },
});
const emit = defineEmits(['toEdit', 'toDel', 'handleTableChange']);
const toEdit = (val: CarSubmit) => {
  emit('toEdit', val);
};
const toDel = (val: CarSubmit) => {
  emit('toDel', val);
};
const handleTableChange = (pagination: any) => {
  emit('handleTableChange', pagination);
};
</script>
<template>
  <ATable
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :pagination="pagination"
    bordered
    class="ant-table-striped"
    row-key="id"
    size="middle"
    @change="handleTableChange"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'id'">
        <span v-if="!record.deleteFlag">
          <AButton type="link" @click="() => toEdit(record)">
            <EditOutlined />修改
          </AButton>
          <ADivider type="vertical" />
          <AButton type="link" @click="() => toDel(record)">
            <DeleteOutlined />删除
          </AButton>
        </span>
        <span v-else>
          <AButton type="link">无操作</AButton>
        </span>
      </template>
      <template v-else-if="column.key === 'deleteFlag'">
        <ATag :color="record.deleteFlag ? 'volcano' : 'green'">
          {{ record.deleteFlag ? '已删除' : '正常' }}
        </ATag>
      </template>
    </template>
  </ATable>
</template>

<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #f3f3f3;
}
</style>
