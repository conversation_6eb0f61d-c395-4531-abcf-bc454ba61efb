import type { RequestClient } from '@vben/request';

import type { ResponseResult } from './types/commont';
import type { WorkloadSearch, WorkloadViewModel } from './types/workload';

export interface WorkloadSubmit {
  amount: number;
  completeWorkload: number;
  contractCode: string;
  endDate?: string;
  id: number;
  orderNo: string;
  projectCode: string;
  startDate?: string;
  unitPrice: number;
  updateUser: string;
}
export const createWorkloadApi = (request: RequestClient, path: string) => {
  return {
    delete(id: number): Promise<ResponseResult> {
      return request.delete(`${path}/Workload/${id}`);
    },

    /**
     * Get workload by ID
     */
    getById(id: number): Promise<WorkloadViewModel> {
      return request.get(`${path}/Workload/${id}`);
    },
    save(form: WorkloadSubmit): Promise<ResponseResult> {
      return request.post(`${path}/Workload/save`, form);
    },
    /**
     * Get workload list
     */
    list(params: WorkloadSearch): Promise<WorkloadViewModel[]> {
      return request.get(`${path}/Workload/list`, { params });
    },
  };
};
