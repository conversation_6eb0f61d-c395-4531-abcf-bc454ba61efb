<script setup lang="ts">
import type { WidgetPropsType } from '@coder/vdesigner-core';

import { computed, onMounted, ref } from 'vue';

import {
  useMitter,
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { Col } from 'ant-design-vue';

import { ColOptions } from './ColOptions';

defineOptions({ name: 'CoderVDesignCol' });

const props = defineProps<WidgetPropsType & { showWidget: boolean }>();
const store = useRenderStore(props.renderId);
const { getComponent } = useWidgetRegistry();
const { childWidgets, widget, isDesign } = useWidget(
  props.widget,
  props.renderId,
);
const imple = computed(() => store.implement);
const options = widget.value.options as ColOptions;
const colDesignRef = ref();
const colRef = ref();
const mitter = useMitter(props.renderId);
if (store.isDesign) {
  // 在设计模式下，colRef 需要被添加到设计器中
  mitter.emitAddContainer(colDesignRef, childWidgets, props.parentWidget);
}

onMounted(() => {
  mitter.emitAddWidget(colRef.value.$el, props.widget, props.parentWidget);
});
</script>

<template>
  <Col
    ref="colRef"
    :class="{ 'col-in-designer': isDesign }"
    :flex="options.flex"
    :lg="options.lg"
    :md="options.md"
    :offset="options.offset"
    :pull="options.pull"
    :push="options.push"
    :sm="options.sm"
    :span="options.span"
    :xl="options.xl"
    :xs="options.xs"
    :xxl="options.xxl"
    v-widget-menu="{
      widgetProps: props,
      moveClass: 'col-handle',
    }"
  >
    <div ref="colDesignRef" style="min-height: 35px" v-if="isDesign">
      <h3 v-if="childWidgets.length === 0 && showWidget">请拖动到这里</h3>
      <span v-if="!showWidget">{{ widget.id }}</span>
      <template
        v-else
        v-for="childWidget in childWidgets.filter((_) => _ !== undefined)"
        :key="childWidget.id"
      >
        <component
          :is="getComponent(childWidget.type, imple)"
          :is-design="isDesign"
          :parent-widget="props.widget"
          :render-id="props.renderId"
          :widget="childWidget"
        />
      </template>
    </div>
    <template
      v-else
      v-for="childWidget in childWidgets.filter((_) => _ !== undefined)"
      :key="childWidget.id"
    >
      <component
        :is="getComponent(childWidget.type, imple)"
        :is-design="isDesign"
        :parent-widget="props.widget"
        :render-id="props.renderId"
        :widget="childWidget"
      />
    </template>
  </Col>
</template>
<style lang="scss" scoped>
.col-in-designer {
  background-color: #fff;
  border: 1px dashed #000000f1;

  .drop-placeholder: {
    background-color: aliceblue;
  }
}
</style>
