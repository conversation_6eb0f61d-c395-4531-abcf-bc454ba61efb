<script setup lang="tsx">
import type { DiaryItems } from '@hai-an/contract-api';

import { computed, ref, watch } from 'vue';

import { useUserStore } from '@vben/stores';

import {
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  PaperClipOutlined,
} from '@ant-design/icons-vue';
import { PreviewButton } from '@coder/preview';
// import { makePreviewUrl as makeUrl } from '../../../types/preview'
import { createDiaryApi, haianDiaryOption } from '@hai-an/diary-api';
import {
  Card as ACard,
  Descriptions as ADescriptions,
  DescriptionsItem as ADescriptionsItem,
  Empty as AEmpty,
  Modal as AModal,
  Popconfirm as APopconfirm,
  Result as AResult,
  Spin as ASpin,
  Table as ATable,
  Tag as ATag,
  Badge,
  Button,
  message,
  Space,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps<{
  oaDiaryId?: number | string;
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'edit', id: string): void;
  (e: 'delete', id: string): void;
}>();

const API = createDiaryApi(haianDiaryOption.request, haianDiaryOption.path);
const userStore = useUserStore();
const loading = ref(false);
const diaryData = ref<DiaryItems | null>(null);

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});
const userName = computed(() => userStore?.userInfo?.userName);

const fileColumns = [
  {
    title: '文件名',
    dataIndex: 'fileName',
    key: 'fileName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '文件ID',
    dataIndex: 'fileId',
    key: 'fileId',
    width: 150,
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
    customRender: ({ text }: { text: string }) => formatDateTime(text),
  },
  {
    title: '操作',
    key: 'action',
    width: 160,
    customRender: ({ record }: { record: any }) => {
      if (!record.fileId && !record.id) {
        message.error('找不到文件ID，无法预览');
      }
      // 预览文件
      const handlePreviewFile = (): string => {
        return API.getPreviewFileUrl(record.fileId) as string;
      };
      return (
        <Space>
          <PreviewButton
            headers={headers.value}
            type="Link"
            url={handlePreviewFile}
          >
            <EyeOutlined />
            预览
          </PreviewButton>
          <Button onClick={() => handleDownloadFile(record)} type="link">
            <DownloadOutlined />
            下载
          </Button>
        </Space>
      );
    },
  },
];

const formatDateTime = (dateTime: null | number | string) => {
  if (!dateTime) return '-';
  try {
    // 将时间戳或日期字符串转换为Date对象
    const d =
      typeof dateTime === 'number'
        ? new Date(dateTime * 1000)
        : new Date(dateTime);

    // 格式化年月日
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');

    // 格式化时分秒
    const hours = d.getHours().toString().padStart(2, '0');
    const minutes = d.getMinutes().toString().padStart(2, '0');
    const seconds = d.getSeconds().toString().padStart(2, '0');

    // 返回格式化后的日期时间字符串
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch {
    return dateTime?.toString() || '-';
  }
};

const formatDate = (date: null | string) => {
  if (!date) return '-';
  dayjs(date).format('yyyy年M月D日');
};

const handleCancel = () => {
  visible.value = false;
};

const handleEdit = () => {
  if (diaryData.value) {
    emit('edit', diaryData.value.id as string);
  }
};

const handleDelete = async () => {
  if (diaryData.value) {
    try {
      await API.del(diaryData.value.id as string);
      message.success('删除成功');
      visible.value = false;
      emit('delete', diaryData.value.id as string);
    } catch {
      message.error('删除失败');
    }
  }
};

// const handleCopy = async () => {
//   if (!diaryData.value?.content) {
//     message.warning('没有内容可复制');
//     return;
//   }

//   try {
//     await navigator.clipboard.writeText(diaryData.value.content);
//     message.success('内容已复制到剪贴板');
//   } catch {
//     // 降级处理
//     const textArea = document.createElement('textarea');
//     textArea.value = diaryData.value.content;
//     document.body.append(textArea);
//     textArea.select();
//     document.execCommand('copy');
//     textArea.remove();
//     message.success('内容已复制到剪贴板');
//   }
// };

const headers = computed(() => {
  // 使用 diary API 配置中的 getToken 方法
  const token = haianDiaryOption.getToken ? haianDiaryOption.getToken() : '';

  const result: Record<string, string> = {};
  if (token) {
    result.Authorization = token; // 已经包含 "Bearer " 前缀
  }
  return result;
});
// 预览URL和引用
const previewUrlRef = ref();
const previewUrl = ref('');

const handleDownloadFile = async (file: any) => {
  if (!file.fileId && !file.id) {
    message.error('找不到文件ID，无法下载');
    return;
  }

  try {
    const fileId = file.id;
    const response = await API.downloadFile(fileId);

    // 创建Blob对象
    const blob = new Blob([response.data]);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = file.fileName || '未知文件';

    // 模拟点击下载
    document.body.append(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(link.href);

    message.success(`文件 ${file.fileName} 下载成功`);
  } catch (error) {
    message.error(`下载文件失败: ${error}`);
  }
};

// 加载详情数据
const loadDetailData = async () => {
  const diaryId = props.oaDiaryId;
  if (!diaryId) {
    return;
  }
  loading.value = true;
  try {
    const response: any = await API.getBayId(diaryId);
    diaryData.value = response?.data as DiaryItems;
  } catch {
    diaryData.value = null;
  } finally {
    loading.value = false;
  }
};

// 监听日记ID变化，加载详情数据
// watch(
//   () => [props.oaDiaryId, props.visible],
//   async ([newDiaryId, isVisible]) => {
//     const diaryId = newDiaryId
//     if (!diaryId || !isVisible) {
//       return
//     }
//     await loadDetailData()
//   },
//   { immediate: true }
// )

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    const diaryId = props.oaDiaryId;
    if (newVisible && diaryId) {
      loadDetailData();
    } else if (!newVisible) {
      // 清空数据
      diaryData.value = null;
    }
  },
);
</script>

<template>
  <AModal
    v-model:visible="visible"
    title="OA日记详情"
    :width="1100"
    @cancel="handleCancel"
    :footer="null"
  >
    <div v-if="loading" style="padding: 50px; text-align: center">
      <ASpin size="large" />
      <div style="margin-top: 16px; color: #666">加载中...</div>
    </div>
    <div v-else-if="diaryData" class="diary-detail">
      <!-- 标题和操作区域 -->
      <div class="detail-header">
        <h3 class="detail-title">{{ diaryData.title || '无标题' }}</h3>
        <div class="detail-actions" v-if="userName === diaryData.createBy">
          <Space>
            <Button type="primary" @click="handleEdit" size="small">
              <template #icon><EditOutlined /></template>
              编辑
            </Button>
            <APopconfirm
              title="确认删除这条记录吗？"
              ok-text="确认"
              cancel-text="取消"
              @confirm="handleDelete"
            >
              <Button type="primary" danger size="small">
                <template #icon><DeleteOutlined /></template>
                删除
              </Button>
            </APopconfirm>
          </Space>
        </div>
      </div>
      <!-- 基本信息 -->
      <ACard class="info-card" size="small">
        <template #title>
          <span>基本信息</span>
        </template>
        <ADescriptions :column="4" bordered size="small">
          <ADescriptionsItem label="日记时间">
            <ATag color="orange">{{ formatDate(diaryData.diaryTime) }}</ATag>
          </ADescriptionsItem>
          <ADescriptionsItem label="创建人">
            {{ diaryData.createBy || '-' }}
          </ADescriptionsItem>
          <ADescriptionsItem label="创建时间">
            {{ formatDateTime(diaryData.createTime) }}
          </ADescriptionsItem>

          <ADescriptionsItem label="附件数量">
            <Badge
              :count="diaryData.fileList?.length || 0"
              :number-style="{ backgroundColor: '#52c41a' }"
            >
              <ATag>{{ diaryData.fileList?.length || 0 }} 个文件</ATag>
            </Badge>
          </ADescriptionsItem>
        </ADescriptions>
      </ACard>

      <!-- 日记内容 -->
      <ACard class="content-card" size="small">
        <template #title>
          <span><FileTextOutlined /> 日记内容</span>
        </template>
        <div class="content-container">
          <div v-if="diaryData.content" class="content-text">
            {{ diaryData.content }}
          </div>
          <div v-else class="empty-content">
            <AEmpty description="暂无内容" :image="false" />
          </div>
        </div>
      </ACard>
      <!-- 附件列表 -->
      <ACard class="file-card" size="small">
        <template #title>
          <span><PaperClipOutlined /> 附件列表</span>
        </template>
        <div v-if="diaryData.fileList && diaryData.fileList.length > 0">
          <ATable
            :data-source="diaryData.fileList"
            :columns="fileColumns"
            :pagination="false"
            size="small"
            bordered
            :scroll="{ x: 600 }"
          />
        </div>
        <div v-else class="empty-files">
          <AEmpty description="暂无附件" :image="false" style="padding: 20px" />
        </div>
      </ACard>
    </div>
    <div v-else class="error-state">
      <AResult
        status="warning"
        title="加载失败"
        sub-title="无法获取日记详情数据"
      >
        <template #extra>
          <Button type="primary" @click="loadDetailData"> 重新加载 </Button>
        </template>
      </AResult>
    </div>
    <!-- 隐藏的预览链接元素 -->
    <a
      :href="previewUrl"
      target="_blank"
      ref="previewUrlRef"
      style="display: none"
    ></a>
  </AModal>
  <!-- <AModal
    :open="previewVisible"
    title="预览222222222222222222222222222222"
    :width="1300"
    @cancel="() => (previewVisible = false)"
    footer=""
    cancel-text="取消"
  >
    {{ previewUrl }}
    <PreviewContent v-if="previewUrl" :url="previewUrl" :headers="headers" />
  </AModal> -->
</template>

<style scoped>
.diary-detail {
  padding: 0;
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 16px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
}

.detail-title {
  margin: 0;
  font-size: 20px; /* 增加标题字体大小 */
  font-weight: 600;
  color: white;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.info-card,
.content-card,
.file-card {
  margin-bottom: 16px;
}

.content-container {
  min-height: 120px;
}

.content-text {
  padding: 16px;
  font-size: 14px;
  line-height: 1.8;
  color: #333;
  word-break: break-all;
  white-space: pre-wrap;
  background: #fafafa;
  border-left: 4px solid #1890ff;
  border-radius: 6px;
}

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

.error-state {
  padding: 20px;
}

.empty-files {
  color: #999;
  text-align: center;
}

:deep(.ant-descriptions-item-label) {
  font-size: 14px; /* 增加标签字体大小 */
  font-weight: 600;
  background: #f8f9fa !important;
}

:deep(.ant-descriptions-item-content) {
  font-size: 14px; /* 增加内容字体大小 */
}

:deep(.ant-card-head) {
  background: #f8f9fa;
  border-bottom: 2px solid #e8e8e8;
}

:deep(.ant-card-head-title) {
  font-size: 16px; /* 增加卡片标题字体大小 */
  font-weight: 600;
  color: #1890ff;
}

:deep(.ant-table-thead > tr > th) {
  font-size: 14px; /* 增加表头字体大小 */
  font-weight: 600;
  background: #f8f9fa;
}

:deep(.ant-table) {
  font-size: 14px; /* 增加表格内容字体大小 */
}

:deep(.ant-table-tbody > tr > td) {
  padding: 10px 8px; /* 增加单元格内边距 */
}
</style>
