import type { ProjectViewModel } from '@hai-an/contract-api';
import type { ColumnsType } from 'ant-design-vue/es/table';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import { Button, Divider, Tag } from 'ant-design-vue';

import { dayF } from '../../util';
import { deleteProject, showDetail, showEditor } from './useModal';

export const makeColumns = (reload: { (): void }) => {
  return [
    {
      align: 'center',
      dataIndex: 'action',
      title: '操作',
      width: 320,
      customRender({ record }) {
        const proj = record as ProjectViewModel;
        if (proj.isDeleted === true) {
          return <span>--无操作--</span>;
        }

        return (
          <span>
            <Button onClick={() => showEditor(proj, reload)} type="link">
              <EditOutlined />
              修改
            </Button>
            <Divider type="vertical" />
            <Button onClick={() => deleteProject(proj, reload)} type="link">
              <DeleteOutlined />
              删除
            </Button>
          </span>
        );
      },
    },
    {
      dataIndex: 'code',
      title: '合同项目编号',
      width: 200,
      customRender({ record }) {
        const proj = record as ProjectViewModel;
        return <a onClick={() => showDetail(proj)}>{proj.code}</a>;
      },
    },
    {
      dataIndex: 'name',
      title: '合同项目名称',
      customRender({ record }) {
        const proj = record as ProjectViewModel;
        return <a onClick={() => showDetail(proj)}>{proj.name}</a>;
      },
      // slots: { customRender: "name" },
    },
    {
      dataIndex: 'manager',
      title: '负责人',
      // slots: { customRender: "manager" },
    },
    {
      dataIndex: 'phone',
      title: '负责人电话',
      // slots: { customRender: "phone" },
    },
    {
      dataIndex: 'isDeleted',
      title: '状态',
      customRender({ record }) {
        return (
          <Tag color={record.isDeleted ? 'volcano' : 'green'}>
            {record.isDeleted ? '已删除' : '正常'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      // slots: { customRender: "createBy" },
    },
    {
      dataIndex: 'createTime',
      title: '创建日期',
      customRender({ text }) {
        return <>{dayF(text)}</>;
      },
    },
  ] as ColumnsType;
};
