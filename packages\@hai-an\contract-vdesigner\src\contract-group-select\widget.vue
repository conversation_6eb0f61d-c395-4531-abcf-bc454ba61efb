<script lang="ts" setup>
import type { WidgetPropsType } from '@coder/vdesigner-core';

import type { ContractGroupViewModel } from '../../../contract-api/src';
import type { ContractGroupSelectOptions } from './_options';

import { computed } from 'vue';

import {
  useRenderStore,
  useWidget,
  useWidgetRegistry,
} from '@coder/vdesigner-core';
import { SelectContractGroup } from '@hai-an/contract-ui';

const props = defineProps<WidgetPropsType>();
const { getComponent } = useWidgetRegistry();
const { widget, value, isDesign, callJsCode } = useWidget(
  props.widget,
  props.renderId,
);
const renderStore = useRenderStore(props.renderId);
const options = widget.value.options as ContractGroupSelectOptions;
const CoderVDesignFormItem = getComponent(
  'CoderVDesignFormItem',
  renderStore.implement,
);
const style = computed(() => {
  return {
    display: options.hidden ? 'none' : 'block',
  };
});
const onChange = (val: ContractGroupViewModel) => {
  if (!isDesign.value) {
    callJsCode(options.changeEvent, {
      contractGroup: val,
    });
  }
};
</script>
<template>
  <CoderVDesignFormItem v-bind="props">
    <SelectContractGroup v-model="value" :style="style" @change="onChange" />
  </CoderVDesignFormItem>
</template>
