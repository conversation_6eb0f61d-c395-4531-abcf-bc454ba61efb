import type { ProjectManagementItem } from '@hai-an/contract-api';

import { h } from 'vue';

import { Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

// 扩展项目管理项接口以包含表格所需的字段
interface ExtendedProjectManagementItem extends ProjectManagementItem {
  collectedAmount: number;
  manager: string;
  paidAmount: number;
  paymentAmount: number;
  projectAddress: string;
  projectAmount: number;
  projectCreateDate: string;
  projectType: number | string;
  receivedAmount: number;
  totalProfit: number;
  unCollectedAmount: number;
  unPaymentAmount: number;
}

const getProjectType = (val: number | string) => {
  const projectTypes: any = {
    0: '警戒',
    1: '清道护航',
    2: '航标布设',
    3: '航标维护',
    4: '溢油应急服务',
    5: '海图制作',
  };
  return projectTypes[val] || '未知类型';
};

export const columns = [
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
    align: 'center',
  },
  {
    title: '项目编号',
    width: 200,
    dataIndex: 'code',
    key: 'code',
    // customRender: ({ text }: { text: string }) => {
    //   return h('a', {
    //     onClick: () => console.log('text:', text),
    //   }, text)
    // },
  },
  {
    title: '项目名称',
    width: 200,
    dataIndex: 'name',
  },
  {
    title: '项目创建人',
    width: 200,
    dataIndex: 'createBy',
  },
  {
    title: '项目类型',
    width: 200,
    dataIndex: 'projectType',
    customRender: ({ record }: { record: ExtendedProjectManagementItem }) => {
      return getProjectType(record.projectType);
    },
  },
  {
    title: '项目地址',
    width: 200,
    dataIndex: 'projectAddress',
  },
  {
    title: '项目创建时间',
    width: 120,
    dataIndex: 'projectCreateDate',
    customRender: ({ text }: { text: string }) => {
      return dayjs(text).format('YYYY-MM-DD');
    },
  },
  {
    title: '负责人',
    width: 100,
    dataIndex: 'manager',
  },
  {
    title: '负责人电话',
    width: 130,
    dataIndex: 'phone',
  },
  {
    title: '状态',
    dataIndex: 'isDeleted',
    width: 160,
    customRender: ({ text }: { text: string }) => {
      return h(
        Tag,
        {
          color: text ? 'volcano' : 'green',
        },
        () => (text ? '已删除' : '正常'),
      );
    },
  },
  {
    title: '总利润',
    dataIndex: 'totalProfit',
  },
  {
    title: '项目金额',
    dataIndex: 'projectAmount',
  },
  {
    title: '收款金额',
    dataIndex: 'collectedAmount',
  },
  {
    title: '已收款金额',
    dataIndex: 'receivedAmount',
  },
  {
    title: '未收款金额',
    dataIndex: 'unCollectedAmount',
  },
  {
    title: '合同付款金额',
    dataIndex: 'paymentAmount',
  },
  {
    title: '已付款金额',
    dataIndex: 'paidAmount',
  },
  {
    title: '未付款金额',
    dataIndex: 'unPaymentAmount',
  },
] as Array<any>;
