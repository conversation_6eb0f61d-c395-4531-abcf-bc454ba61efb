<script setup lang="ts">
import type { ContractSubmit } from '@hai-an/contract-api';
import type { RuleObject } from 'ant-design-vue/es/form';

import { computed, onMounted, reactive, ref } from 'vue';

import {
  BookType,
  createContractApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Row as ARow,
  Textarea as ATextarea,
  message,
} from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  id: { default: 0, type: Number },
});

const emit = defineEmits(['saved', 'doCancel']);

const dateFormat = 'YYYY-MM-DD';

const api = createContractApi(options.request, options.path);

const dayF = (val: string) => {
  return val ? dayjs(val).format('YYYY-MM-DD') : '';
};

const submitForm = reactive<ContractSubmit>({
  applyDate: '',
  bookDate: '',

  bookType: BookType.Expense,
  code: '',
  contractPrice: '',
  contractPriceInfo: '',
  contractTotal: 0,
  contractTotalInfo: '',
  contractType: 0,
  endDate: '',
  id: 0,
  lockInfo: '',
  name: '',
  oppositeCode: '',
  oppositeName: '',
  orderCloseDate: '',
  orderNo: '',
  orgPath: '',
  outline: '',
  payCount: 0,
  payDate: '',
  payTotal: 0,
  planBookDate: '',
  projectCode: '',
  projectName: '',
  promiseDateType: '',
  promiseServiceTerm: '',
  requestTotal: 0,
  serviceContent: '',
  serviceShip: '',
  serviceTerm: '',
  startDate: '',
  startPayDate: '',
  taxRate: 0,
  workloadDate: '',
  workloadLockInfo: '',
});

const submitFormRef = ref();
const rules = ref({
  name: [{ message: '请输入合同名称!', required: true, trigger: 'change' }],
} as Record<string, RuleObject[]>);

const reload = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res);
    });
  }
};

const bookDateForm = computed({
  get: () => (submitForm.bookDate ? dayjs(submitForm.bookDate) : undefined),
  set: (val) => {
    submitForm.bookDate = val ? val.format(dateFormat) : '';
  },
});

const startPayDateForm = computed({
  get: () =>
    submitForm.startPayDate ? dayjs(submitForm.startPayDate) : undefined,
  set: (val) => {
    submitForm.startPayDate = val ? val.format(dateFormat) : '';
  },
});

const startDateForm = computed({
  get: () => (submitForm.startDate ? dayjs(submitForm.startDate) : undefined),
  set: (val) => {
    submitForm.startDate = val ? val.format(dateFormat) : '';
  },
});

const endDateForm = computed({
  get: () => (submitForm.endDate ? dayjs(submitForm.endDate) : undefined),
  set: (val) => {
    submitForm.endDate = val ? val.format(dateFormat) : '';
  },
});

const reset = () => {
  if (submitForm.id === 0) {
    const tempForm = {
      applyDate: null,
      bookDate: undefined,
      code: '',
      contractPrice: '',
      contractPriceInfo: '',
      contractTotal: '',
      contractTotalInfo: '',
      contractType: 0,
      endDate: undefined,
      name: '',
      oppositeName: '',
      orderCloseDate: null,
      orgPath: '',
      outline: '',
      planBookDate: null,
      projectName: '',
      promiseDateType: '',
      promiseServiceTerm: '',
      serviceContent: '',
      serviceShip: '',
      serviceTerm: '',
      startDate: undefined,
      startPayDate: undefined,
    };
    Object.assign(submitForm, tempForm);
  } else {
    reload();
  }
};

onMounted(() => {
  reload();
});

defineExpose({
  save: () => {
    submitFormRef.value.validate().then(() => {
      api.save(submitForm).then((res) => {
        if (res.success) {
          message.success(res.message);
          emit('saved');
        } else {
          message.error(res.message);
        }
      });
    });
  },
  reset,
});

const labelCol = { style: { width: '150px' } };
</script>

<template>
  <AForm
    ref="submitFormRef"
    layout="horizontal"
    :model="submitForm"
    :rules="rules"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 14 }"
  >
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同名称"
          name="name"
        >
          <AInput v-model:value="submitForm.name" autocomplete="off" />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同编号"
          name="code"
        >
          {{ submitForm.code }}
        </AFormItem>
      </ACol>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="申请时间"
          name="applyDate"
        >
          {{ dayF(submitForm.applyDate) }}
        </AFormItem>
      </ACol>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="闭单时间"
          name="orderCloseDate"
        >
          {{ dayF(submitForm.orderCloseDate) }}
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="8">
        <AFormItem :label-col="labelCol" has-feedback label="合同类型">
          {{ submitForm.contractType === 0 ? '收款合同' : '付款合同' }}
        </AFormItem>
      </ACol>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="项目名称"
          name="projectName"
        >
          {{ submitForm.projectName }}
        </AFormItem>
      </ACol>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="对方名称"
          name="oppositeName"
        >
          {{ submitForm.oppositeName }}
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="所属组织机构"
          name="orgPath"
        >
          {{ submitForm.orgPath }}
        </AFormItem>
      </ACol>
      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="预计签订日期"
          name="planBookDate"
        >
          {{ dayF(submitForm.planBookDate) }}
        </AFormItem>
      </ACol>

      <ACol :span="8">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同总价(万元)"
          name="contractTotal"
        >
          {{ submitForm.contractTotal }}
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="签订日期"
          name="bookDate"
        >
          <ADatePicker
            v-if="submitForm.bookDate"
            style="width: 200px"
            v-model:value="bookDateForm"
            format="YYYY-MM-DD"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同起算日期"
          name="startPayDate"
        >
          <ADatePicker
            v-if="submitForm.startPayDate"
            style="width: 200px"
            v-model:value="startPayDateForm"
            format="YYYY-MM-DD"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同开始日期"
          name="startDate"
        >
          <ADatePicker
            v-if="submitForm.startDate"
            style="width: 200px"
            v-model:value="startDateForm"
            format="YYYY-MM-DD"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
      <ACol :span="12">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同截止日期"
          name="endDate"
        >
          <ADatePicker
            v-if="submitForm.endDate"
            style="width: 200px"
            v-model:value="endDateForm"
            format="YYYY-MM-DD"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同单价"
          name="contractPrice"
        >
          {{ submitForm.contractPrice }}
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同概况"
          name="outline"
        >
          <ATextarea
            style="width: 800px"
            v-model:value="submitForm.outline"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="约定付款时间及方式"
          name="promiseDateType"
        >
          {{ submitForm.promiseDateType }}
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="约定服务期限"
          name="promiseServiceTerm"
        >
          {{ submitForm.promiseServiceTerm }}
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同单价说明"
          name="contractPriceInfo"
        >
          <ATextarea
            style="width: 800px"
            v-model:value="submitForm.contractPriceInfo"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>

    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="合同总价说明"
          name="contractTotalInfo"
        >
          <ATextarea
            style="width: 800px"
            v-model:value="submitForm.contractTotalInfo"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="实际服务期限"
          name="serviceTerm"
        >
          <ATextarea
            style="width: 800px"
            v-model:value="submitForm.serviceTerm"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="服务内容"
          name="serviceContent"
        >
          <ATextarea
            style="width: 800px"
            v-model:value="submitForm.serviceContent"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
    <ARow>
      <ACol :span="24">
        <AFormItem
          :label-col="labelCol"
          has-feedback
          label="提供服务的船舶"
          name="serviceShip"
        >
          <ATextarea
            style="width: 800px"
            v-model:value="submitForm.serviceShip"
            autocomplete="off"
          />
        </AFormItem>
      </ACol>
    </ARow>
  </AForm>
</template>
