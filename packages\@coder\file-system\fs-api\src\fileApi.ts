import type {
  QuickUpload<PERSON><PERSON>ult,
  SFileSearcher,
  SFileSubmit,
  SFileSubmitResult,
  SFileSubmitWithFile,
  SFileViewModel,
} from './types';

import { RequestClient } from '@vben/request';

export const createFsApi = (request: RequestClient, path: string) => {
  const urlPath = `${path}/file`;
  // console.error('file api path', urlPath);
  return {
    buildPack(fileId: Array<string>, fileName = null) {
      return request.post(`${urlPath}/build-package`, {
        fileId,
        fileName,
      });
    },
    list: (params: SFileSearcher) => {
      return request.get<SFileViewModel[]>(`${urlPath}/list`, {
        params,
      });
    },
    count: (params: SFileSearcher) => {
      return request.get<number>(`${urlPath}/count`, {
        params,
      });
    },

    delFile(id: string) {
      return request.delete(`${urlPath}/${id}`);
    },
    getById(id: string): Promise<SFileViewModel> {
      return request.get(`${urlPath}/${id}`);
    },
    getDownloadFilePath(id: string) {
      return `${urlPath}/download/${id}`;
    },

    getQuickUploadPath: () => {
      return `${urlPath}/quick-upload`;
    },

    /**
     *
     * @param id 文件id
     * @returns 返回一次性访问文件的ticket
     */
    getTicket: (id: string) => {
      return request.get<{ ticket: string }>(
        `${urlPath}/build-access-file-ticket/${id}`,
      );
    },
    getUploadFilePath: () => {
      return `${urlPath}/upload`;
    },
    getViewFilePath: (id: string) => {
      return `${urlPath}/view/${id}`;
    },

    quickUpload: () => {
      return request.post<QuickUploadResult>(`${urlPath}/quick-upload`);
    },

    save(params: SFileSubmit) {
      return request.put(`${urlPath}/update`, params);
    },

    uploadSimpleFile: (submit: SFileSubmitWithFile) => {
      const forms = new FormData();
      const submitData = submit as Record<string, any>;
      for (const key in submitData) {
        forms.append(key, submitData[key]);
      }

      const options = {
        data: forms,
        headers: { 'content-type': 'multipart/form-data' },
        method: 'POST',
        url: `${urlPath}/upload-simple-file`,
      };
      return request.request<SFileSubmitResult>(
        `${urlPath}/upload-simple-file`,
        options,
      );
    },
  };
};
