import type { RequestClient } from '@vben/request';

export const createMaintenanceApi = (request: RequestClient, _path: string) => {
  // const path = 'workflow/WorkflowMaintenance'
  const path = `${_path}/WorkflowMaintenance`;
  return {
    /**
     * 工单已闭单状态改为未完成，工作处理人回退到最后一环节
     * @param searcher
     * @returns
     */
    GetProcessingBackLast(number: string) {
      return request.get(`${path}get-process-back-last?number=${number}`);
    },
    /**
     * 工单未完成状态改为已闭单，提单处理人直接完成（用于替换工单附件，在修复成已完成）
     * @param searcher
     * @returns
     */
    GetCompletedfirst(number: string) {
      return request.get(`${path}get-completed-first?number=${number}`);
    },
    /**
     * 工单已闭单状态改为未完成，工作处理人退回到提单环节
     * @param searcher
     * @returns
     */
    GetProcessingBackFirst(number: string) {
      return request.get(`${path}get-process-back-first?number=${number}`);
    },
    GetFormByNumber(number: string) {
      return request.put(`${path}get-save-form/${number}`);
    },
    FormSaveByNumber(number: string, form: object) {
      return request.put(`${path}save-form1/${number}`, {
        form: JSON.stringify(form),
      });
    },
  };
};
