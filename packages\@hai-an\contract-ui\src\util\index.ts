import dayjs from 'dayjs';

/**
 * 日期格式化函数
 * @param val 日期字符串
 * @param format 格式化字符串，默认为 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串，如果 val 为空则返回空字符串
 */
export const dayF = (
  val: string | undefined,
  format: string = 'YYYY-MM-DD',
) => {
  return val ? dayjs(val).format(format) : '';
};

/**
 * 数字格式化函数，将数字除以指定的值并保留5位小数
 * @param val 要格式化的数字
 * @param divisor 除数，默认为1
 * @returns 格式化后的数字
 */
export const numF = (val: number, divisor: number = 1) => {
  if (!val) return 0;
  const num = val / divisor;
  return Math.round(num * 100_000) / 100_000;
};

/**
 * 金额格式化函数，将数字转换为财务格式（千分位）
 * @param val 要格式化的金额
 * @param digits 小数位数，默认为2
 * @returns 格式化后的金额字符串
 */
export const moneyF = (val: number | string, digits: number = 2) => {
  if (!val) return '0.00';
  const num = typeof val === 'string' ? Number.parseFloat(val) : val;
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: digits,
    maximumFractionDigits: digits,
  });
};
