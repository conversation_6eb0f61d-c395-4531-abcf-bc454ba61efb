const toString = Object.prototype.toString;

export function is(val: unknown, type: string) {
  return toString.call(val) === `[object ${type}]`;
}

export function isNumber(val: unknown): val is number {
  return is(val, 'Number');
}

export function textToCircularSvg(
  text: string,
  backgroundColor = '#3498db',
  textColor = '#ffffff',
  size = 30,
  fontSize = 16,
) {
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', size);
  svg.setAttribute('height', size);
  svg.setAttribute('viewBox', `0 0 ${size} ${size}`);

  // Create circular background
  const circle = document.createElementNS(
    'http://www.w3.org/2000/svg',
    'circle',
  );
  circle.setAttribute('cx', size / 2);
  circle.setAttribute('cy', size / 2);
  circle.setAttribute('r', size / 2);
  circle.setAttribute('fill', backgroundColor);
  svg.append(circle);

  // Add text
  const textElement = document.createElementNS(
    'http://www.w3.org/2000/svg',
    'text',
  );
  textElement.setAttribute('x', '50%');
  textElement.setAttribute('y', '50%');
  textElement.setAttribute('text-anchor', 'middle');
  textElement.setAttribute('dominant-baseline', 'central');
  textElement.setAttribute('fill', textColor);
  textElement.setAttribute('font-family', 'Arial, sans-serif');
  textElement.setAttribute('font-size', `${fontSize}px`);
  textElement.textContent = text;
  svg.append(textElement);

  // Convert SVG to base64 string
  const svgString = new XMLSerializer().serializeToString(svg);
  // 现代的解决方案，使用 TextEncoder 和 Uint8Array
  const encoder = new TextEncoder();
  const data = encoder.encode(svgString);
  const base64 = btoa(
    [...data].map((byte) => String.fromCodePoint(byte)).join(''),
  );
  return `data:image/svg+xml;base64,${base64}`;
}
