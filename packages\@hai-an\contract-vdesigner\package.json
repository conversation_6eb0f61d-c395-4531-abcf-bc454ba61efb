{"name": "@hai-an/vdesigner-oa", "version": "1.0.0", "private": false, "type": "module", "scripts": {"build": " vite build", "dev": "vite", "preview": "vite preview"}, "files": ["dist"], "main": "./src/index.ts", "typings": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/vdesigner-core": "workspace:^", "@coder/vdesigner-form-render": "workspace:^", "@hai-an/car-ui": "workspace:*", "@hai-an/contract-ui": "workspace:*", "@vben/icons": "workspace:*", "ant-design-vue": "catalog:", "axios": "catalog:", "dayjs": "catalog:", "vue": "catalog:"}, "devDependencies": {"@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}