{"name": "@coder/vdesigner-form-render", "version": "0.1.0", "private": false, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview"}, "main": "lib/index.ts", "typings": "lib/index.ts", "publishConfig": {"main": "dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs"}}, "typings": "dist/index.d.ts"}, "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/code-editor": "workspace:*", "@coder/http-request-setting": "workspace:*", "@coder/rich-editor": "workspace:*", "@coder/string-format": "workspace:*", "@coder/toolbar": "workspace:*", "@coder/vdesigner-core": "workspace:^", "@vben/icons": "workspace:*", "@vben/request": "workspace:^", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "axios": "catalog:", "dayjs": "catalog:", "grid-layout-plus": "catalog:coder", "lodash-es": "catalog:coder", "lru-cache": "catalog:coder", "mitt": "catalog:", "pinia": "catalog:", "spark-md5": "catalog:coder", "vue": "catalog:", "vue-draggable-plus": "catalog:coder"}, "devDependencies": {"@types/lodash-es": "catalog:coder", "@types/spark-md5": "catalog:coder", "@vben/tailwind-config": "workspace:*", "@vben/tsconfig": "workspace:^", "@vben/vite-config": "workspace:*"}}