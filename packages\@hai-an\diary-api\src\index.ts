import type { RequestClient } from '@vben/request';

export * from './services/diaryApi';
export interface IHaianDiaryOption {
  getToken: { (): string };
  path: string;
  previewHost?: string;
  request: RequestClient;
}

export const haianDiaryOption = {} as IHaianDiaryOption;

export const api = {
  install: (_app: any, options: IHaianDiaryOption) => {
    if (options.path.endsWith('/')) {
      options.path = options.path.slice(
        0,
        Math.max(0, options.path.length - 1),
      );
    }

    Object.assign(haianDiaryOption, options);
  },
};

export default api;
