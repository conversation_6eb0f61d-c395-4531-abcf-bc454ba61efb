import type { RequestClient } from '@vben/request';

import type {
  ProjectManagementItem,
  ProjectManagementSearch,
} from './types/projectManagement';

export const createProjectManagementApi = (
  request: RequestClient,
  _path: string,
) => {
  // const path = `${_path}/Supplier`;
  const path = `${_path}/ProjectArchive`;
  return {
    list(params: ProjectManagementSearch): Promise<ProjectManagementItem[]> {
      return request.get(`${path}/list`, { params });
    },
    count(params: ProjectManagementSearch): Promise<number> {
      return request.get(`${path}/count`, { params });
    },
    getData(id: number | string) {
      return request.get(`${path}/${id}`);
    },
    delData(id: number | string) {
      return request.delete(`${path}/${id}`);
    },
    exist(params: ProjectManagementSearch) {
      return request.get(`${path}/exist`, { params });
    },
    save(formData: ProjectManagementItem) {
      return request.post(`${path}/save`, formData);
    },
  };
};
