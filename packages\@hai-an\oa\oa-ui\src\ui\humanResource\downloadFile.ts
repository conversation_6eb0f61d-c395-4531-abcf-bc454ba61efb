import { message } from 'ant-design-vue';

export const downloadFile = (file: any, name: string = 'file-name') => {
  try {
    const blob = new Blob([file], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const link = document.createElement('a');
    link.href = window.URL.createObjectURL(blob);
    link.download = `${name}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(link.href);
  } catch (error: any) {
    message.error(`下载模板失败：${error.message || JSON.stringify(error)}`);
  }
};

export default downloadFile;
