import type { WidgetOptionEditorSetting } from '@coder/vdesigner-core';

import {
  booleanEditor,
  formItemName,
  javascriptEditor,
} from '@coder/vdesigner-core';

const intellisense = `
interface ContractGroupViewModel {
  code: string;
  createBy: string;
  createTime: Date;
  description?: string;
  id: number;
  isDeleted?: boolean;
  name: string;
}
declare const data:{contractGroup:ContractGroupViewModel}
`;
export const ContractSelectEditor = () => {
  return {
    name: formItemName('contractGroupSelect'),
    hidden: booleanEditor('是否隐藏'),
    changeEvent: javascriptEditor('change事件', intellisense),
  } as Record<string, WidgetOptionEditorSetting>;
};
