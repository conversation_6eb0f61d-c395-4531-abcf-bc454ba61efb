import type { Ref } from 'vue';

import { Button, Input, Space, type TableColumnType } from 'ant-design-vue';

const defineColumns = (data: Ref<any[]>) => {
  const columns = [
    {
      customRender({ index }) {
        const onDelete = () => {
          data.value.splice(index, 1);
        };

        return (
          <Space>
            <Button danger onClick={onDelete} size="small">
              删除
            </Button>
          </Space>
        );
      },
      key: 'action',
      title: '操作',
    },
    {
      customRender({ record }) {
        return <Input v-model:value={record.title}></Input>;
      },
      dataIndex: 'title',
      key: 'title',
      title: '名称',
    },
    {
      customRender({ record }) {
        return <Input v-model:value={record.dataIndex}></Input>;
      },
      dataIndex: 'dataIndex',
      key: 'dataIndex',
      title: '属性名称',
    },
    {
      customRender({ record }) {
        return <Input v-model:value={record.key}></Input>;
      },
      dataIndex: 'key',
      key: 'key',
      title: 'key',
    },
  ] as TableColumnType[];
  return columns;
};

export default defineColumns;
