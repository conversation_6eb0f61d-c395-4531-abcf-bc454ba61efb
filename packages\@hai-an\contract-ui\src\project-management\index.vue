<script setup lang="ts">
import type {
  ProjectManagementItem,
  ProjectManagementSearch,
} from '@hai-an/document-api';

import { onMounted, reactive, ref, watch } from 'vue';

import { useLists } from '@vben/hooks';

import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import {
  haianContractOption as apiOptions,
  createProjectManagementApi,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Select as ASelect,
  SelectOption as ASelectOption,
  Space as ASpace,
  Table as ATable,
  message,
  Modal,
} from 'ant-design-vue';

import { columns } from './columns';
import PreviewModal from './components/preview-modal/index.vue';
import UpdateModal from './components/update-modal.vue';

const options = [
  { value: 'false', label: '正常' },
  { value: 'true', label: '已删除' },
];

const projectTypeOptions = [
  { name: '警戒', value: '0' },
  { name: '清道护航', value: '1' },
  { name: '航标布设', value: '2' },
  { name: '航标维护', value: '3' },
  { name: '溢油应急服务', value: '4' },
  { name: '海图制作', value: '5' },
];
const searchForm = reactive({
  code: '',
  name: '',
  manager: '',
  phone: '',
  isDeleted: 'false',
} as ProjectManagementSearch);

const onChange = (evt: any) => {
  searchForm.isDeleted = evt ? evt.value : 'false';
};

const api = createProjectManagementApi(apiOptions.request, apiOptions.path);

const { getList, reload, tableData, loading, pagination, changeTable } =
  useLists({
    searchForm,
    listApi: api.list,
    countApi: api.count,
    deleteApi: api.delData,
  });
const visibleUpdate = ref(false);
const visiblePreview = ref(false);
const itemId = ref();
const itemCode = ref('');

const handlePreview = (item: ProjectManagementItem) => {
  itemId.value = item.id;
  itemCode.value = item.code;
  visiblePreview.value = true;
};
const onAdd = () => {
  itemId.value = 0;
  visibleUpdate.value = true;
};
const handleUpdate = (item: ProjectManagementItem) => {
  itemId.value = item.id;
  visibleUpdate.value = true;
};
const onDelete = (item: ProjectManagementItem) => {
  Modal.confirm({
    cancelText: '取消',
    content: () => '流程,一旦删除，将不能够恢复。',
    okText: '删除',
    title: `警告-` + `你正在删除项目档案管理`,
    onOk: async () => {
      const res = await api.delData(item.id);
      message.success(res.message);
    },
  });
};

watch(visibleUpdate, (val, oldVal) => {
  if (oldVal && !val) {
    // 弹窗由开到关，说明新增/编辑完成，自动刷新列表
    reload();
  }
});

onMounted(() => {
  getList();
});
</script>

<template>
  <ACard>
    <AForm :model="searchForm" layout="inline">
      <AFormItem name="code" label="项目编号">
        <AInput v-model:value="searchForm.code" allow-clear />
      </AFormItem>
      <AFormItem name="name" label="项目名称">
        <AInput v-model:value="searchForm.name" allow-clear />
      </AFormItem>
      <AFormItem name="manager" label="负责人">
        <AInput v-model:value="searchForm.manager" allow-clear />
      </AFormItem>
      <AFormItem name="phone" label="负责人电话">
        <AInput v-model:value="searchForm.phone" allow-clear />
      </AFormItem>
      <AFormItem name="createBy" label="项目创建人">
        <AInput v-model:value="searchForm.createBy" allow-clear />
      </AFormItem>
      <AFormItem name="projectArchiveType" label="项目类型">
        <ASelect
          v-model:value="searchForm.projectArchiveType"
          placeholder="请选择项目类型"
          style="width: 100%"
          allow-clear
        >
          <ASelectOption
            v-for="item in projectTypeOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.name }}
          </ASelectOption>
        </ASelect>
      </AFormItem>
      <AFormItem name="isDeleted" label="删除标识">
        <ASelect
          v-model:value="searchForm.isDeleted"
          @change="onChange"
          label-in-value
          style="width: 180px"
          :options="options"
          :allow-clear="true"
        />
      </AFormItem>
      <AFormItem>
        <AButton type="primary" @click="reload">
          <SearchOutlined />查询
        </AButton>
      </AFormItem>
    </AForm>
  </ACard>
  <AButton class="mt-3" type="primary" @click="onAdd">
    <PlusOutlined />新增
  </AButton>
  <ACard class="mt-3">
    <ATable
      bordered
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="changeTable"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <ASpace v-if="!record.isDeleted">
            <AButton
              type="link"
              @click="handleUpdate(record as ProjectManagementItem)"
            >
              <EditOutlined /> 修改
            </AButton>
            <AButton
              type="link"
              @click="onDelete(record as ProjectManagementItem)"
            >
              <DeleteOutlined /> 删除
            </AButton>
          </ASpace>
          <span v-else>--无操作--</span>
        </template>
        <template v-if="column.key === 'code'">
          <a
            @click="handlePreview(record as ProjectManagementItem)"
            style="color: #006be6"
            >{{ record.code }}
          </a>
        </template>
      </template>
    </ATable>
  </ACard>
  <UpdateModal v-model:visible="visibleUpdate" :item-id="itemId" />
  <PreviewModal
    v-model:visible="visiblePreview"
    :item-id="itemId"
    :code="itemCode"
  />
</template>

<style scoped></style>
