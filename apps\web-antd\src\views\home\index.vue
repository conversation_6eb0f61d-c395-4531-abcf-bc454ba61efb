<script setup lang="ts">
import type { WorkActivityListItemViewModel } from '@coder/swf-api';

import { onActivated, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { WorkActivityList } from '@coder/swf-ui';
import { OrgFilter, WorkflowStartupPlatform } from '@hai-an/oa-ui';
import {
  Card as ACard,
  Col as ACol,
  FormItem as AFormItem,
} from 'ant-design-vue';

const router = useRouter();
const swfListRef = ref();

const onDispose = (workActivityId: number) => {
  router.push({
    name: 'SwfWorkActivityDispose',
    params: { id: workActivityId },
  });
};

const onToDispose = (wa: WorkActivityListItemViewModel) => {
  router.push({ name: 'SwfWorkActivityDispose', params: { id: wa.id } });
};
const onDetail = (item: WorkActivityListItemViewModel) => {
  router.push({
    name: 'SwfProcessInstanceDetail',
    params: { id: item.processInstanceId },
  });
};

onActivated(() => {
  if (swfListRef.value) swfListRef.value.reload();
});
</script>

<template>
  <Page>
    <ACard title="可提工单">
      <WorkflowStartupPlatform @dispose="onDispose" />
    </ACard>
    <ACard title="待处理工单" style="margin-top: 10px">
      <WorkActivityList
        ref="swfListRef"
        @dispose="onToDispose"
        @on-detail="onDetail"
      >
        <template #filter="searchForm">
          <ACol :span="6">
            <AFormItem
              label="部门/分公司"
              :label-col="{ span: 8 }"
              :wrapper-col="{ span: 20 }"
            >
              <OrgFilter
                :tags="searchForm.searchModel.tags"
                :tag-index="0"
                placeholder="请选择组织架构"
              />
            </AFormItem>
          </ACol>
        </template>
      </WorkActivityList>
    </ACard>
  </Page>
</template>

<style scoped lang="scss">
// ::v-deep(.ant-card) {
//   border-radius: 2px 2px 0 0 !important;
// }
</style>
