<script setup lang="ts">
import type { ContractGroupSubmit } from '@hai-an/contract-api/src/types/group';

import { onMounted, reactive, ref, toRaw } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import {
  createGroupApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button,
  Card,
  Col,
  Form,
  FormItem,
  Input,
  message,
  Row,
} from 'ant-design-vue';

const props = defineProps({
  id: { default: 0, type: Number },
});
const emit = defineEmits(['doSave', 'doCancel']);

const api = createGroupApi(options.request, options.path);

const submitForm = reactive<ContractGroupSubmit>({
  code: '',
  id: 0,
  name: '',
});

const submitFormRef = ref();
const rules = ref({
  name: [
    { max: 200, message: '字符串长度最大为200', trigger: 'blur' },
    { message: '请输入合同组名称!', required: true, trigger: 'change' },
  ],
});

const toSave = () => {
  submitFormRef.value.validate().then(() => {
    api.save(toRaw(submitForm)).then((res) => {
      if (res.success) {
        message.success(res.message);
        emit('doSave');
      } else {
        message.error(res.message);
      }
    });
  });
};
const reload = () => {
  if (props.id) {
    api.getById(props.id).then((res) => {
      Object.assign(submitForm, res.data);
    });
  }
};
const doReset = () => {
  if (submitForm.id === 0) {
    const tempForm = {
      name: '',
    };
    Object.assign(submitForm, tempForm);
  } else {
    reload();
  }
};
const doCancel = () => {
  emit('doCancel');
};

onMounted(() => {
  reload();
});

const labelCol = { style: { width: '150px' } };
</script>
<template>
  <Card>
    <Form
      ref="submitFormRef"
      layout="horizontal"
      :model="submitForm"
      :rules="rules as any"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 14 }"
    >
      <Row>
        <Col :span="12">
          <FormItem
            :label-col="labelCol"
            has-feedback
            label="合同组名称"
            name="name"
          >
            <Input v-model:value="submitForm.name" autocomplete="off" />
          </FormItem>
        </Col>
      </Row>
    </Form>
    <Row type="flex" justify="center">
      <Col :span="4">
        <Button @click="doCancel"> <CloseOutlined />取消 </Button>
      </Col>
      <Col :span="4">
        <Button @click="doReset"> <RedoOutlined />重置 </Button>
      </Col>
      <Col :span="4">
        <Button type="primary" @click="toSave"> <CheckOutlined />保存 </Button>
      </Col>
    </Row>
  </Card>
</template>
