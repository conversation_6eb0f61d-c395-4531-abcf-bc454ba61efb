import type { WidgetOptionEditorSetting } from '@coder/vdesigner-core';

import {
  booleanEditor,
  formItemName,
  javascriptEditor,
} from '@coder/vdesigner-core';

const intellisense = `
interface ProjectViewModel {
  code: string;
  createdTime?: string;
  description?: string;
  id: number;
  isDeleted?: boolean;
  name: string;
  updatedTime?: string;
  // 添加其他项目相关的属性
}

declare const data:{project:ProjectViewModel};
`;
export const ProjectSelectorEditor = () => {
  return {
    name: formItemName('projectSelector'),
    hidden: booleanEditor('是否隐藏'),
    changeEvent: javascriptEditor('change事件', intellisense),
  } as Record<string, WidgetOptionEditorSetting>;
};
