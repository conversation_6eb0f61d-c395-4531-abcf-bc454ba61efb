export interface SupplierViewModel {
  address: string;
  bankName: string;
  code: string;
  createBy: string;
  createTime: string;
  id: number;
  isDeleted: boolean;
  name: string;
  num: string;
  orgPath: string;
  phone: string;
  updateBy: string;
  updateTime: string;
  userName: string;
}

export interface SupplierSearch {
  code?: string;
  name?: string;
  page?: number;
  pageSize?: number;
}
