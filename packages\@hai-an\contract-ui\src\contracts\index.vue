<!-- 一级仓 -->
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

import { Card as ACard, Table } from 'ant-design-vue';

import ContractForm from './components/form.vue';
import { makeColumns } from './tableColumn';
import { useList } from './useList';

interface Props {
  contractType?: number; // 0: 收款合同, 1: 付款合同
  isEdit?: boolean;
  projectArchiveCode?: string;
}
const props = withDefaults(defineProps<Props>(), {
  isEdit: true,
  projectArchiveCode: '',
  contractType: null,
});

const { pagination, search, searchForm, dataSource, pageChange } =
  useList(props);
const columns = ref(makeColumns(props.isEdit));

const loading = ref(false);
const contractFormRef = ref();

const onSearch = () => {
  // 将 ContractForm 的 searchForm 内容赋值给 useList 的 searchForm
  if (contractFormRef.value?.searchForm) {
    Object.assign(searchForm, contractFormRef.value.searchForm);
  }
  search();
};

onMounted(() => {
  search();
});

watch(
  () => props.projectArchiveCode,
  (newVal) => {
    if (newVal !== undefined) {
      searchForm.projectArchiveCode = newVal;
      search();
    }
  },
);
watch(
  () => props.isEdit,
  (val) => {
    columns.value = makeColumns(val);
  },
);
</script>
<template>
  <ACard v-if="props.isEdit">
    <ContractForm ref="contractFormRef" @on-search="onSearch" />
  </ACard>
  <ACard class="mt-3">
    <Table
      size="middle"
      :data-source="dataSource"
      :pagination="pagination"
      @change="pageChange"
      :loading="loading"
      :is-edit="props.isEdit"
      :columns="columns"
    />
  </ACard>
</template>
<style scoped>
.space-align-container {
  margin-top: 10px;
  margin-bottom: 5px;
}
</style>
