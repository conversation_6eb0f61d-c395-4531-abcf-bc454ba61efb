<script lang="ts" setup>
import type { Performer } from '@coder/system-ui';
import type {
  FolderListItem,
  FolderPermissionSubmit,
  PermissionViewModel,
} from '@hai-an/document-api';

import type { PropType } from 'vue';

import { ref, watch } from 'vue';

import { EditOutlined } from '@ant-design/icons-vue';
import { PerformSelector } from '@coder/system-ui';
import { createFolderPermissionApi } from '@hai-an/document-api';
import {
  Button as AButton,
  Modal as AModal,
  TabPane as ATabPane,
  Tabs as ATabs,
  message,
} from 'ant-design-vue';

import { documentOptions } from '../../..';

const props = defineProps({
  folder: {
    type: Object as PropType<FolderListItem>,
    default: () => ({}),
  },
});

const emits = defineEmits(['saveSuccess']);

const visible = ref(false);

const managerPerformers = ref<Array<Performer>>([]);
const readonlyPerformers = ref<Array<Performer>>([]);

const activeKey = ref('1');
const show = () => {
  setData();
  visible.value = true;
};

watch(
  () => props.folder,
  () => {
    setData();
  },
  { deep: true },
);

/**
 * 渲染所需数据
 */
const setData = () => {
  managerPerformers.value.splice(0);
  readonlyPerformers.value.splice(0);
  managerPerformers.value = [...trnafer(props.folder?.managePerformers || [])];
  readonlyPerformers.value = [...trnafer(props.folder?.performers || [])];
};

const trnafer = (view: PermissionViewModel[]): Performer[] => {
  // console.log('trnafer view', view);
  return view.map((el) => {
    return {
      name: el.name,
      key: el.name,
      type: el.performerType,
      selected: true,
    } as Performer;
  });
};

const reTrasfer = (
  performers: Array<Performer>,
  isManager: boolean,
): Array<PermissionViewModel> => {
  const result = new Array<PermissionViewModel>();

  for (const performer of performers) {
    const item = {} as PermissionViewModel;
    // console.log('reTrasfer performers[i]', performer, item);
    const el = { ...performer };

    item.name = el.key;
    item.performerType = el.type;
    item.permissionType = isManager ? 0 : 1; // 1:管理权限，0:查看权限
    // ? PermissionType.管理权限
    //  : PermissionType.使用权限;
    // : 0; // 1:管理权限，0:查看权限

    result.push(item);
  }
  return result;
};
// console.log('documentOptions', documentOptions);
const onSave = () => {
  const manager = [...reTrasfer(managerPerformers.value, true)];
  const performers = [...reTrasfer(readonlyPerformers.value, false)];

  // documentOptions.request is not a function,重新加载该请求
  // console.log(documentOptions.request, 'documentOptions.request');
  if (
    !documentOptions.request ||
    Object.keys(documentOptions.request).length === 0
  ) {
    message.error('documentOptions.request is not a function,重新加载该请求');
  }

  const folderPermissionApi = createFolderPermissionApi(
    documentOptions.request!,
    documentOptions.path,
  );

  folderPermissionApi
    .save({
      id: props.folder?.id,
      managePerformers: manager,
      performers,
    } as FolderPermissionSubmit)
    .then((resp) => {
      emits('saveSuccess', {
        manager: [...manager],
        performers: [...performers],
      });
      message.success(resp.message);
      if (resp.success) {
        visible.value = false;
        setData();
      }
    });
};

defineExpose({
  show,
});
</script>

<template>
  <AModal v-model:visible="visible" title="权限设置">
    <ATabs v-model:active-key="activeKey">
      <ATabPane key="1" tab="查看权限">
        <PerformSelector v-model="readonlyPerformers" :is-manager="false" />
      </ATabPane>
      <ATabPane
        key="2"
        v-if="props.folder ? props.folder.isAdmin : false"
        tab="管理权限"
      >
        <PerformSelector v-model="managerPerformers" :is-manager="true" />
      </ATabPane>
    </ATabs>

    <template #footer>
      <AButton type="primary" @click="onSave">
        <template #icon>
          <EditOutlined />
        </template>
        保存
      </AButton>
    </template>
  </AModal>
</template>
