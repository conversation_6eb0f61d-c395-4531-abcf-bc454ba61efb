import dayjs from 'dayjs';

export const columns = [
  {
    title: '序号',
    width: 80,
    align: 'center',
    // dataIndex: 'id',
    // key: 'id',
    dataIndex: 'index',
    key: 'index',
    scopedSlots: { customRender: 'index' },
  },
  {
    title: '文件名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '文件夹',
    dataIndex: 'folderPath',
    key: 'folderPath',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    customRender: ({ text }: { text: string }) => {
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    key: 'action',
  },
] as Array<any>;
