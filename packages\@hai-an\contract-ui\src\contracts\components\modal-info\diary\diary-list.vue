<script setup lang="ts">
import type { DiaryItems, DiarySearch } from '@hai-an/contract-api';

import type { PropType } from 'vue';

import { computed, onMounted, reactive, ref } from 'vue';

import { useUserStore } from '@vben/stores';

import {
  LeftOutlined,
  PlusOutlined,
  RightOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { createDiaryApi, haianDiaryOption } from '@hai-an/diary-api';
import {
  Button as AButton,
  Calendar as ACalendar,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Space as ASpace,
  message,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';

import DetailModal from './detail-modal.vue';
import UpdateModal from './update-modal.vue';

import 'dayjs/locale/zh-cn';

const props = defineProps({
  businessId: {
    type: [String, Number] as PropType<any>,
    default: null,
  },
  diaryType: {
    type: String as PropType<string>,
    default: '',
  },
  businessName: {
    type: String as PropType<string>,
    default: '',
  },
});
const API = createDiaryApi(haianDiaryOption.request, haianDiaryOption.path);
const userStore = useUserStore();
const userName = computed(() => userStore?.userInfo?.userName);
const searcherForm = reactive({
  title: '',
  content: '',
  page: 0,
  pageSize: 999,
} as DiarySearch);
const loading = ref(false);
const dataSource = ref<DiaryItems[]>([]);
const selectedDate = ref(dayjs());
const currentId = ref<number | string>('');
const editId = ref('');
const defaultDiaryTime = ref('');
const updateVisible = ref(false);
const detailVisible = ref(false);
// 设置 dayjs 语言为中文
dayjs.locale('zh-cn');
const calendarLocale = locale;

// 当前月份标签
const currentMonthLabel = computed(() => {
  return selectedDate.value.format('YYYY年MM月');
});
// 切换月份
const changeMonth = (delta: number) => {
  selectedDate.value = selectedDate.value.add(delta, 'month');
  loadList();
};
// 获取指定日期的日记
const getLogsForDate = (date: dayjs.Dayjs) => {
  const dateStr = date.format('YYYY-MM-DD');
  return dataSource.value.filter((log) => {
    if (!log.diaryTime) return false;
    const logDate = dayjs(log.diaryTime).format('YYYY-MM-DD');
    return logDate === dateStr;
  });
};

// 获取日记内容摘要
const getLogSummary = (content: string) => {
  if (!content) return '';
  return content.length > 15 ? `${content.slice(0, 15)}...` : content;
};

// 获取指定日期的显示日记（最多3条，超出显示"更多"）
const getDisplayLogsForDate = (date: dayjs.Dayjs): DiaryItems[] => {
  const logs = getLogsForDate(date);
  const maxDisplay = 3;

  if (logs.length <= maxDisplay) {
    return logs.map((log) => ({
      ...log,
      content: getLogSummary(log.content || ''),
    }));
  }

  const displayLogs = logs.slice(0, maxDisplay).map((log) => ({
    ...log,
    content: getLogSummary(log.content || ''),
  }));

  // 添加"更多"项
  const moreItem = {
    id: '',
    title: `+${logs.length - maxDisplay}更多`, // 使用title而非moreText
    content: '',
    diaryType: '',
    businessId: '',
    businessName: null,
    diaryTime: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
    fileList: [],
  } as DiaryItems;

  displayLogs.push(moreItem);

  return displayLogs;
};
// 日期选择
const onDateSelect = (date: dayjs.Dayjs | string) => {
  selectedDate.value = typeof date === 'string' ? dayjs(date) : date;
};
// 面板变化
const onPanelChange = (date: dayjs.Dayjs | string, _: string) => {
  selectedDate.value = typeof date === 'string' ? dayjs(date) : date;
  loadList();
};

const showLogDetail = (log: DiaryItems) => {
  currentId.value = log.id;
  detailVisible.value = true;
};
// 显示当天所有日记（可以后续扩展为弹窗列表）
const showAllLogsForDate = (date: dayjs.Dayjs) => {
  const logs = getLogsForDate(date);
  const dateStr = date.format('YYYY年MM月DD日');
  message.info(`${dateStr}共有${logs.length}条日记`);
  // 这里可以扩展为显示列表弹窗
};

// 日志点击处理（区分普通记录和"更多"按钮）
const handleLogClick = (log: DiaryItems, date: dayjs.Dayjs): void => {
  if (log.id) {
    showLogDetail(log);
  } else {
    showAllLogsForDate(date);
  }
};

// 日志双击处理（仅处理有id的记录且满足权限）
const handleLogDoubleClick = (log: DiaryItems): void => {
  if (log.id && log.createBy && userName.value === log.createBy) {
    handleEditLog(log);
  }
};

// 编辑日记
const handleEditLog = (log: DiaryItems) => {
  // 只有当前登录用户和创建人相同时才能编辑
  if (userName.value === log.createBy) {
    editId.value = log.id as string;
    defaultDiaryTime.value = log.diaryTime || '';
    updateVisible.value = true;
  }
};
// 编辑日记
const handleEditFromDetail = (id: string) => {
  // Get the current record to verify createBy
  // const record = dataSource.value.find((item) => item.id === id)
  // if (record && userName.value === record.createBy) {
  //   editId.value = id
  //   detailVisible.value = false
  //   updateVisible.value = true
  // }
  editId.value = id;
  detailVisible.value = false;
  updateVisible.value = true;
};

const onAdd = () => {
  editId.value = '';
  defaultDiaryTime.value = selectedDate.value.format('YYYY-MM-DD'); // 默认设置为当前选择的日期
  updateVisible.value = true;
};

const loadList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searcherForm,
      diaryMonth: selectedDate.value.format('YYYY-MM'),
      ...props,
    };
    const responseData: any = await API.list(params);
    if (responseData.code === 200) {
      dataSource.value = responseData.data.rows;
    }
  } catch {
    message.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadList();
});
</script>

<template>
  <ACard>
    <AForm layout="inline" class="search-form">
      <AFormItem label="标题">
        <AInput
          v-model:value="searcherForm.title"
          placeholder="请输入标题"
          allow-clear
        />
      </AFormItem>
      <AFormItem label="内容">
        <AInput
          v-model:value="searcherForm.content"
          placeholder="请输入内容"
          allow-clear
        />
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton type="primary" @click="loadList">
            <template #icon><SearchOutlined /></template> 查询
          </AButton>
          <AButton type="primary" @click="onAdd">
            <template #icon><PlusOutlined /></template> 新增
          </AButton>
        </ASpace>
      </AFormItem>
    </AForm>
    <!-- 日历视图 -->
    <div class="calendar-container">
      <div class="calendar-header">
        <AButton @click="changeMonth(-1)">
          <template #icon><LeftOutlined /></template>
        </AButton>
        <span class="current-month">{{ currentMonthLabel }}</span>
        <AButton @click="changeMonth(1)">
          <template #icon><RightOutlined /></template>
        </AButton>
      </div>
      <ACalendar
        v-model:value="selectedDate"
        mode="month"
        :locale="calendarLocale"
        @select="onDateSelect"
        @panel-change="onPanelChange"
      >
        <template #dateCellRender="{ current }">
          <div class="date-cell">
            <div class="date-number">{{ current.date() }}</div>
            <div class="log-content" v-if="getLogsForDate(current).length > 0">
              <div
                v-for="(log, index) in getDisplayLogsForDate(current)"
                :key="log.id || `more-${index}`"
                class="log-item"
                @click="handleLogClick(log, current)"
                @dblclick="handleLogDoubleClick(log)"
                :title="log.id ? log.title || '' : ''"
              >
                <div class="log-summary">{{ log.title }}</div>
              </div>
            </div>
          </div>
        </template>
      </ACalendar>
    </div>
  </ACard>
  <UpdateModal
    v-model:visible="updateVisible"
    :edit-id="editId"
    :business-id="props.businessId"
    :business-name="props.businessName"
    :diary-type="props.diaryType"
    :default-diary-time="defaultDiaryTime"
    @success="loadList"
  />
  <DetailModal
    v-model:visible="detailVisible"
    :oa-diary-id="currentId"
    @edit="handleEditFromDetail"
    @delete="loadList"
  />
</template>

<style scoped>
.oa-diary-calendar {
  min-height: 100vh;
  padding: 20px;
  background: #f0f2f5;
}

.search-form {
  margin-bottom: 20px;
  font-size: 14px; /* 增加搜索表单的字体大小 */
}

:deep(.ant-form-item-label > label) {
  font-size: 14px; /* 标签字体大小 */
}

:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  font-size: 14px; /* 输入框字体大小 */
}

:deep(.ant-select-selection-item),
:deep(.ant-picker-input > input) {
  line-height: 36px;
}

:deep(.ant-btn) {
  height: 36px;
  font-size: 14px;
}

.calendar-container {
  margin-top: 20px;
}

.calendar-header {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.current-month {
  padding: 0 15px;
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

:deep(.ant-picker-calendar) {
  background: white;
  border-radius: 6px;
}

:deep(.ant-picker-cell) {
  height: 130px !important;
}

:deep(.ant-picker-calendar-date) {
  padding: 6px 8px !important;
}

.date-cell {
  position: relative;
  height: 100%;
  padding: 2px;
}

.date-number {
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.log-content {
  max-height: 90px;
  overflow: hidden;
}

.log-item {
  padding: 2px 4px;
  margin-bottom: 2px;
  font-size: 14px;
  line-height: 1.4;
  cursor: pointer;
  background: #f0f9ff;
  border: 1px solid #e6f7ff;
  border-radius: 3px;
  transition: all 0.2s;
}

.log-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.log-summary {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: #333;
  word-break: break-all;
  white-space: nowrap;
}

.log-type {
  text-align: center;
}

:deep(.ant-card-body) {
  padding: 24px;
}
</style>
