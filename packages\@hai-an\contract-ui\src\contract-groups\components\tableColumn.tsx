import type { ContractGroupViewModel } from '@hai-an/contract-api';
import type { ColumnsType } from 'ant-design-vue/es/table';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue';
import { UserLabel } from '@coder/system-ui';
import { Button, Divider, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

export const makeColumns = (
  onDelete: { (val: ContractGroupViewModel): void },
  onEdit: { (val: ContractGroupViewModel): void },
  onDetail: { (val: ContractGroupViewModel): void },
) => {
  return [
    {
      align: 'center',
      dataIndex: 'action',
      title: '操作',
      width: 320,
      customRender(opt) {
        const record = opt.record as ContractGroupViewModel;
        if (record.isDeleted !== true) {
          return (
            <span>
              <Button onClick={() => onEdit(record)} type="link">
                <EditOutlined />
                修改
              </Button>
              <Divider type="vertical" />
              <Button onClick={() => onDelete(record)} type="link">
                <DeleteOutlined />
                删除
              </Button>
            </span>
          );
        }
        return <span>--无操作--</span>;
      },
    },
    {
      dataIndex: 'name',
      title: '合同组名称',
      width: 200,
      customRender(opt) {
        const record = opt.record as ContractGroupViewModel;
        return <a onClick={() => onDetail(record)}>{record.name}</a>;
      },
    },
    {
      dataIndex: 'isDeleted',
      title: '状态',
      customRender(opt) {
        const record = opt.record as ContractGroupViewModel;
        return (
          <Tag
            color={record.isDeleted === true ? 'volcano' : 'green'}
            onClick={() => onDetail(record)}
          >
            {record.isDeleted === true ? '已删除' : '正常'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'contractTotalOut',
      title: '付款合同总计',
    },
    {
      dataIndex: 'payTotalOut',
      title: '已付款总计',
    },
    {
      dataIndex: 'contractTotalIn',
      title: '收款合同总计',
    },
    {
      dataIndex: 'payTotalIn',
      title: '已经收款总计',
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      customRender(opt) {
        const record = opt.record as ContractGroupViewModel;
        return <UserLabel userName={record.createBy} />;
      },
    },
    {
      dataIndex: 'createTime',
      title: '创建日期',
      customRender: ({ text }) => {
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ] as ColumnsType;
};
