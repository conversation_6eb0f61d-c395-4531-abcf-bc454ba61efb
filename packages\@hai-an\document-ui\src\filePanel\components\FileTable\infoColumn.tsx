import type { FileListItem } from '@hai-an/document-api';
import type { TableColumnType } from 'ant-design-vue';

import type { FileItem } from './useFileTable';

import { ref } from 'vue';

import { createIconifyIcon } from '@vben/icons';

import { DownloadButton } from '@coder/file-download';
import { createFileApi } from '@hai-an/document-api';
import { Button, message, Modal, Progress, Space } from 'ant-design-vue';

import { documentOptions } from '../../..';

const EyeIcon = createIconifyIcon('ant-design:eye-outlined');
const DeleteIcon = createIconifyIcon('ant-design:delete-outlined');
const deleteFile = (record: FileItem, onSuccess?: () => void) => {
  Modal.confirm({
    cancelText: '取消',
    okText: '确定',
    onOk: () => {
      const fileApi = createFileApi(
        documentOptions.request,
        documentOptions.path,
      );

      fileApi.deleteFile(record.id).then((msg) => {
        if (msg.success) {
          message.success({ content: msg.message });
          // 删除成功后调用刷新回调
          onSuccess?.();
        } else {
          message.error({ content: msg.message });
        }
      });
    },
    title: `是否删除文件${record.name}?`,
  });
};

export const createColumn = (
  previewFile: (url: string) => void,
  onRefresh?: () => void,
) => {
  const columns: TableColumnType<FileListItem>[] = [
    {
      dataIndex: 'name',
      key: 'name',
      title: '文件名',
      width: '30%',
    },
    {
      customRender: ({ record }) => {
        return record.fileLength;
      },
      dataIndex: 'size',
      key: 'size',
      title: '大小',
    },
    {
      dataIndex: 'createUser',
      key: 'createUser',
      title: '创建者',
    },
    {
      customRender: ({ text }) => new Date(text).toLocaleString(),
      dataIndex: 'createTime',
      key: 'createTime',
      title: '创建时间',
    },
    {
      customRender: ({ text }) => new Date(text).toLocaleString(),
      dataIndex: 'updateTime',
      key: 'updateTime',
      title: '更新时间',
    },
    {
      customRender: ({ record }) => {
        const fileItem = record as FileItem;
        if (fileItem.uploadStatus) {
          const precent =
            fileItem.uploadStatus.current / fileItem.uploadStatus.total;
          return (
            <span>
              {precent * 100}%
              <Progress percent={precent * 100} />
            </span>
          );
        } else {
          const fileApi = createFileApi(
            documentOptions.request,
            documentOptions.path,
          );
          const url = ref(fileApi.getDownloadUrl(record.id));
          const headers = {
            Authorization: () =>
              documentOptions.getToken ? documentOptions.getToken() : '',
          };
          const getFilePath = async (): Promise<string> => {
            const api = createFileApi(
              documentOptions.request,
              documentOptions.path,
            );

            const fileTicketResult = await api.getPreviewTicket(record.id);
            const ticket = (fileTicketResult as any).data.ticket;
            const result = `${url.value}?_ctikt_=${ticket}&fullfilename=${record.name}`;

            return result;
          };

          const handlePreview = async () => {
            try {
              const authenticatedUrl = await getFilePath();
              previewFile(authenticatedUrl);
            } catch (error) {
              message.error('获取预览链接失败');
              console.error('Preview error:', error);
            }
          };

          return (
            <Space>
              <DownloadButton headers={headers} url={url.value}>
                下载
              </DownloadButton>
              <Button onClick={handlePreview} type="link">
                <span
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    whiteSpace: 'nowrap',
                    color: '#1890ff',
                  }}
                >
                  <EyeIcon />
                  预览
                </span>
              </Button>
              <Button
                onClick={() => deleteFile(fileItem, onRefresh)}
                type="link"
              >
                <span
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    whiteSpace: 'nowrap',
                    color: '#1890ff',
                  }}
                >
                  <DeleteIcon />
                  删除
                </span>
              </Button>
            </Space>
          );
        }
      },
      key: 'action',
      title: '操作',
      width: '30%',
    },
  ];

  return columns;
};
