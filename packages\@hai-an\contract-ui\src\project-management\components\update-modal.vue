<script setup lang="ts">
import type { PropType } from 'vue';

import { computed, reactive, ref, watch } from 'vue';

import {
  CheckOutlined,
  CloseOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue';
import {
  haianContractOption as apiOptions,
  createProjectManagementApi,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  Card as ACard,
  Col as ACol,
  DatePicker as ADatePicker,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Modal as AModal,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
} from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import dayjs from 'dayjs';

const props = defineProps({
  visible: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
  itemId: {
    type: Number as PropType<number>,
    default: 0,
  },
});
const emit = defineEmits(['update:visible', 'reload']);
const API = createProjectManagementApi(apiOptions.request, apiOptions.path);
const submitFormRef = ref();
const labelCol = { style: { width: '150px' } };
const title = computed(() => (props.itemId ? '修改项目' : '新增项目'));
const isVisible = computed(() => props.visible);
const rules: any = {
  code: [
    { max: 100, message: '字符串长度最大为100', trigger: 'blur' },
    {
      asyncValidator(_rule: any, value: any, callback: Function) {
        const existForm = {
          id: formData.id,
          code: value,
        };
        API.exist(existForm).then((res) => {
          console.error('res:', res);
          if (res.success) {
            callback();
          } else {
            callback(new Error(res.message));
          }
        });
      },
      trigger: 'blur',
    },
  ],
  name: [
    { max: 100, message: '字符串长度最大为200', trigger: 'blur' },
    { required: true, message: '请输入项目名称!', trigger: 'blur' },
  ],
  manager: [{ max: 100, message: '字符串长度最大为50', trigger: 'blur' }],
  phone: [{ max: 100, message: '字符串长度最大为50', trigger: 'blur' }],
};

const formData = reactive({
  id: 0,
  code: '',
  name: '',
  manager: '',
  phone: '',
  projectType: '0',
  projectCreateDate: '',
  address: '',
});
const optionList = [
  { name: '警戒', value: '0' },
  { name: '清道护航', value: '1' },
  { name: '航标布设', value: '2' },
  { name: '航标维护', value: '3' },
  { name: '溢油应急服务', value: '4' },
  { name: '海图制作', value: '5' },
];

watch(
  () => props.visible,
  (newVal) => {
    newVal && props.itemId && getData();
    !props.itemId && onReset();
  },
);

const canceled = () => {
  emit('update:visible', false);
};
const onReset = () => {
  if (props.itemId === 0) {
    getData();
  } else {
    const tempForm = {
      code: '',
      name: '',
      manager: '',
      phone: '',
      projectType: '0',
      address: '',
      projectCreateDate: '',
    };
    Object.assign(formData, tempForm);
  }
};
const getData = async () => {
  if (props.itemId === 0) {
    const tempForm = {
      id: 0,
      code: '',
      name: '',
      manager: '',
      phone: '',
      projectType: '0',
      address: '',
      projectCreateDate: '',
    };
    Object.assign(formData, tempForm);
  } else {
    const res = await API.getData(props.itemId);

    const form = {
      id: res.id,
      code: res.code,
      name: res.name,
      manager: res.manager,
      phone: res.phone,
      address: res.projectAddress,
      projectType: res.projectType.toString(),
      projectCreateDate: dayjs(res.projectCreateDate).format('YYYY-MM-DD'),
    };
    Object.assign(formData, form);
  }
};

const onSave = () => {
  submitFormRef.value.validate().then(async () => {
    const params = {
      ...formData,
      projectType: Number(formData.projectType),
    };
    await API.save(params as any);
    canceled();
  });
};
</script>

<template>
  <AModal
    :title="title"
    :width="1000"
    :visible="isVisible"
    :mask-closable="false"
    :destroy-on-close="true"
    @cancel="canceled"
    footer=""
  >
    <ACard>
      <AForm
        ref="submitFormRef"
        layout="horizontal"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
      >
        <ARow>
          <ACol :span="12">
            <AFormItem
              label="项目编号"
              name="code"
              :label-col="labelCol"
              has-feedback
            >
              <AInput
                :value="formData.code"
                autocomplete="off"
                @update:value="(v) => (formData.code = v)"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="项目名称"
              name="name"
              :label-col="labelCol"
              has-feedback
            >
              <AInput
                :value="formData.name"
                autocomplete="off"
                @update:value="(v) => (formData.name = v)"
              />
            </AFormItem>
          </ACol>
        </ARow>
        <ARow>
          <ACol :span="12">
            <AFormItem
              label="负责人"
              name="manager"
              :label-col="labelCol"
              has-feedback
            >
              <AInput
                :value="formData.manager"
                autocomplete="off"
                @update:value="(v) => (formData.manager = v)"
              />
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="负责人电话"
              name="phone"
              :label-col="labelCol"
              has-feedback
            >
              <AInput
                :value="formData.phone"
                autocomplete="off"
                @update:value="(v) => (formData.phone = v)"
              />
            </AFormItem>
          </ACol>
        </ARow>
        <ARow>
          <ACol :span="12">
            <AFormItem
              label="项目类型"
              name="projectType"
              :label-col="labelCol"
              has-feedback
            >
              <ASelect
                :value="formData.projectType"
                placeholder="请选择项目类型"
                style="width: 100%"
                @update:value="(v) => (formData.projectType = String(v || '0'))"
              >
                <ASelectOption
                  v-for="item in optionList"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.name }}
                </ASelectOption>
              </ASelect>
            </AFormItem>
          </ACol>
          <ACol :span="12">
            <AFormItem
              label="项目创建时间"
              name="projectCreateDate"
              :label-col="labelCol"
              has-feedback
            >
              <ADatePicker
                :value="formData.projectCreateDate"
                :locale="locale"
                autocomplete="off"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @update:value="
                  (v) => (formData.projectCreateDate = String(v || ''))
                "
              />
            </AFormItem>
          </ACol>
        </ARow>
        <ARow>
          <ACol :span="12">
            <AFormItem
              label="项目地址"
              name="address"
              :label-col="labelCol"
              has-feedback
            >
              <AInput
                :value="formData.address"
                autocomplete="off"
                @update:value="(v) => (formData.address = v)"
              />
            </AFormItem>
          </ACol>
        </ARow>
      </AForm>
      <ARow type="flex" justify="center">
        <ACol :span="4">
          <AButton @click="canceled"><CloseOutlined />取消</AButton>
        </ACol>
        <ACol :span="4">
          <AButton @click="onReset"><RedoOutlined />重置</AButton>
        </ACol>
        <ACol :span="4">
          <AButton @click="onSave" type="primary">
            <CheckOutlined />保存
          </AButton>
        </ACol>
      </ARow>
    </ACard>
  </AModal>
</template>

<style scoped></style>
