import type { IHaianDiaryOption } from '@hai-an/diary-api';

import { useAccessStore } from '@vben/stores';

import DiaryApi from '@hai-an/diary-api';

import { requestClient } from '#/api/request';

const install = (app: any) => {
  const option = {
    path: '/diary',
    // path: 'http://localhost:8080',
    request: requestClient,
    getToken() {
      const store = useAccessStore();
      return `Bearer ${store.accessToken}`;
    },
  } as IHaianDiaryOption;
  app.use(DiaryApi, option);
};

export default install;
