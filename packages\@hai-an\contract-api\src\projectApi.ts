import type { RequestClient } from '@vben/request';

import type { ContractResponse } from './types';
import type { ResponseResult } from './types/commont';
import type { ProjectSearch, ProjectViewModel } from './types/project';

export interface ProjectExistSearcher {
  code: string;
  id: number;
}
export const createProjectApi = (request: RequestClient, path: string) => {
  return {
    /**
     * Get project count
     */
    count(params: { code?: string; name?: string }): Promise<number> {
      return request.get(`${path}/Project/count`, { params });
    },

    delete(id: number): Promise<ResponseResult> {
      return request.delete(`${path}/Project/${id}`);
    },

    exist(params: ProjectExistSearcher): Promise<ResponseResult> {
      return request.get(`${path}/Project/exist`, { params });
    },
    getById(id: number): Promise<ProjectViewModel> {
      return request.get(`${path}/Project/${id}`);
    },
    /**
     * Save project
     */
    save(project: ProjectViewModel): Promise<ContractResponse> {
      return request.post(`${path}/Project/save`, project);
    },
    /**
     * Get project list
     */
    list(params: ProjectSearch): Promise<ProjectViewModel[]> {
      return request.get(`${path}/Project/list`, { params });
    },
  };
};
