import type { RequestClient } from '@vben/request';

import type {
  ContractListItem,
  ContractResponse,
  ContractSearch,
  ContractSubmit,
  ContractViewModel,
} from './types';

export interface GetContractCodeSubmit {
  code: string;
  contractType: string;
  orderNo: string;
  org: string;
  year: string;
}
export const createContractApi = (request: RequestClient, path: string) => {
  return {
    /**
     * 重新创建合同编号
     */
    buildContractCode(body: GetContractCodeSubmit): Promise<string> {
      return request.put(`${path}/Contract/build-contract-code`, body);
    },

    /**
     * Get the total count of contracts matching the search criteria
     */
    count(searcher: ContractSearch): Promise<number> {
      return request.get(`${path}/Contract/count`, { params: searcher });
    },

    /**
     * Delete a contract
     */
    delete(id: number): Promise<ContractResponse> {
      return request.delete(`${path}/Contract/${id}`);
    },

    /**
     * Check if a contract exists
     */
    exists(params: { code?: string; id?: number }): Promise<ContractResponse> {
      return request.get(`${path}/Contract/exist`, { params });
    },

    /**
     * Get contracts by code
     */
    getByCode(code: string): Promise<ContractListItem[]> {
      return request.get(`${path}/Contract/get-by-code/${code}`);
    },

    /**
     * Get contract by ID
     */
    getById(id: number): Promise<ContractViewModel> {
      return request.get(`${path}/Contract/${id}`);
    },
    /**
     * 重新创建合同编号
     */
    getContractCode(params: GetContractCodeSubmit): Promise<string> {
      return request.get(`${path}/Contract/build-contract-code`, { params });
    },
    /**
     * Lock or unlock a contract
     */
    lockContract(data: {
      code: string;
      isLock: boolean;
    }): Promise<ContractResponse> {
      return request.post(`${path}/Contract/contract-lock`, data);
    },

    /**
     * Get master contracts count
     */
    masterCount(searcher: ContractSearch): Promise<number> {
      return request.get(`${path}/Contract/master-count`, { params: searcher });
    },

    /**
     * Get master contracts list
     */
    masterList(searcher: ContractSearch): Promise<ContractListItem[]> {
      return request.get<ContractListItem[]>(`${path}/Contract/master-list`, {
        params: searcher,
      });
    },

    /**
     * Save a contract
     */
    save(contract: ContractSubmit): Promise<ContractResponse> {
      return request.post(`${path}/Contract/save`, contract);
    },

    /**
     * Save contract from order
     */
    saveFromOrder(data: any): Promise<ContractResponse> {
      return request.post(`${path}/Contract/save-from-order`, data);
    },

    /**
     * Get a list of contracts based on search criteria
     */
    list(searcher: ContractSearch): Promise<ContractListItem[]> {
      return request.get(`${path}/Contract/list`, { params: searcher });
    },
    linkList(searcher: ContractSearch): Promise<ContractListItem[]> {
      return request.get(`${path}/Contract/link-list`, { params: searcher });
    },
    linkCount(searcher: ContractSearch): Promise<number> {
      return request.get(`${path}/Contract/link-list`, { params: searcher });
    },

    // 根据项目ID查询关联的合同列表-项目管理使用
    listByProjectId(params: ContractSearch) {
      return request.get(`${path}/Contract/list-by-project`, { params });
    },
    // 根据项目ID查询关联的合同数量-项目管理使用
    countByProjectId(params: ContractSearch) {
      return request.get(`${path}/Contract/list-by-project`, { params });
    },
  };
};
