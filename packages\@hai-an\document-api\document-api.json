{"openapi": "3.0.1", "info": {"title": "文档管理模块 API", "version": "v1"}, "paths": {"/File/change_isCommunal/{id}/{isCommunal}": {"put": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "isCommunal", "in": "path", "required": true, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success"}}}}, "/File/count": {"get": {"tags": ["File"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CreateUser", "in": "query", "schema": {"type": "string"}}, {"name": "FolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsManager", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/File/deleteFile/{id}": {"delete": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/File/get-file-by-id/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/File/get-file-by-sfile-id/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}}}}, "/File/get-preview-ticket/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/File/list": {"get": {"tags": ["File"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "CreateUser", "in": "query", "schema": {"type": "string"}}, {"name": "FolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsManager", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileListItem"}}}}}}}}, "/File/pack-files": {"post": {"tags": ["File"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PackageMultiFiles"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PackageMultiFiles"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PackageMultiFiles"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PackageMultiFiles"}}}}, "responses": {"200": {"description": "Success"}}}}, "/File/quick-upload/{folderId}": {"post": {"tags": ["File"], "parameters": [{"name": "folderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/QuickUploadSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QuickUploadSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QuickUploadSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/QuickUploadSubmit"}}}}, "responses": {"200": {"description": "Success"}}}}, "/File/upload/{folderId}": {"post": {"tags": ["File"], "parameters": [{"name": "folderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"ContentType": {"type": "string"}, "ContentDisposition": {"type": "string"}, "Headers": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "Length": {"type": "integer", "format": "int64"}, "Name": {"type": "string"}, "FileName": {"type": "string"}}}, "encoding": {"ContentType": {"style": "form"}, "ContentDisposition": {"style": "form"}, "Headers": {"style": "form"}, "Length": {"style": "form"}, "Name": {"style": "form"}, "FileName": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}}}}, "/Folder/{id}": {"get": {"tags": ["Folder"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FolderViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FolderViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FolderViewModel"}}}}}}, "delete": {"tags": ["Folder"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/Folder/build-pack/{folderId}": {"get": {"tags": ["Folder"], "parameters": [{"name": "folderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/Folder/count": {"get": {"tags": ["Folder"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "ParentFolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/Folder/count-by-parent": {"get": {"tags": ["Folder"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "ParentFolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/Folder/exchange": {"get": {"tags": ["Folder"], "parameters": [{"name": "AId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "BId", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/Folder/exist": {"get": {"tags": ["Folder"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "ParentFolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResponseMessage"}}}}}}}, "/Folder/list": {"get": {"tags": ["Folder"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "ParentFolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/Folder/list-by-parent": {"get": {"tags": ["Folder"], "parameters": [{"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "ParentFolderId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FolderListItem"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FolderListItem"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FolderListItem"}}}}}}}}, "/Folder/save": {"post": {"tags": ["Folder"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/FolderSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FolderSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FolderSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FolderSubmit"}}}}, "responses": {"default": {"description": "Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Int32UpdateResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Int32UpdateResult"}}}}}}}, "/FolderPermission/{folderId}": {"get": {"tags": ["FolderPermission"], "parameters": [{"name": "folderId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/FolderPermission/save": {"post": {"tags": ["FolderPermission"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/FolderPermissionSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FolderPermissionSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FolderPermissionSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FolderPermissionSubmit"}}}}, "responses": {"200": {"description": "Success"}}}}, "/FolderPermission/search-performers": {"get": {"tags": ["FolderPermission"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"$ref": "#/components/schemas/PerformerType"}}], "responses": {"200": {"description": "Success"}}}}, "/FolderPermission/search-performers-count": {"get": {"tags": ["FolderPermission"], "parameters": [{"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"$ref": "#/components/schemas/PerformerType"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"FileListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileClientId": {"type": "string", "nullable": true}, "folderId": {"type": "integer", "format": "int32"}, "folderName": {"type": "string", "nullable": true}, "folderPath": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "fileLength": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "suffixName": {"type": "string", "nullable": true}, "isCommunal": {"type": "boolean"}, "deleteFlag": {"type": "boolean"}, "deletePermissions": {"type": "boolean"}, "createUser": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateUser": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "FolderListItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "deleteFlag": {"type": "boolean"}, "leafFlag": {"type": "boolean"}, "isAdmin": {"type": "boolean"}, "namePath": {"type": "string", "nullable": true}, "idPath": {"type": "string", "nullable": true}, "parentFolderId": {"type": "integer", "format": "int32"}, "createUser": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateUser": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}, "isManager": {"type": "boolean"}, "isMyFolder": {"type": "boolean"}, "isFolderManager": {"type": "boolean"}, "managePerformers": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionViewModel"}, "nullable": true}, "performers": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionViewModel"}, "nullable": true}}, "additionalProperties": false}, "FolderPermissionSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "managePerformers": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionViewModel"}, "nullable": true}, "performers": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionViewModel"}, "nullable": true}}, "additionalProperties": false}, "FolderSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "parentFolderId": {"type": "integer", "format": "int32"}, "sequence": {"type": "integer", "format": "int32"}, "inheritPermissionM": {"type": "boolean"}, "inheritPermissionQ": {"type": "boolean"}}, "additionalProperties": false}, "FolderViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "sequence": {"type": "integer", "format": "int32"}, "deleteFlag": {"type": "boolean"}, "leafFlag": {"type": "boolean"}, "namePath": {"type": "string", "nullable": true}, "idPath": {"type": "string", "nullable": true}, "parentFolderId": {"type": "integer", "format": "int32"}, "createUser": {"type": "string", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}, "updateUser": {"type": "string", "nullable": true}, "updateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Int32UpdateResult": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "id": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "PackageMultiFiles": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "fileId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "PerformerType": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "PermissionType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "PermissionViewModel": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "permissionType": {"$ref": "#/components/schemas/PermissionType"}, "performerType": {"$ref": "#/components/schemas/PerformerType"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "QuickUploadSubmit": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "md5": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int32"}, "isCommunal": {"type": "boolean"}}, "additionalProperties": false}, "ResponseMessage": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}