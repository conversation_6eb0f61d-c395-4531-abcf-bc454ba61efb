import type { WidgetOptionEditorSetting } from '@coder/vdesigner-core';

import {
  booleanEditor,
  formItemName,
  javascriptEditor,
} from '@coder/vdesigner-core';

const intellisense = `
interface SupplierViewModel {
  address: string;
  bankName: string;
  code: string;
  createBy: string;
  createTime: string;
  id: number;
  isDeleted: boolean;
  name: string;
  num: string;
  orgPath: string;
  phone: string;
  updateBy: string;
  updateTime: string;
  userName: string;
}

declare const data: {supplier:SupplierViewModel };`;
export const ContractSelectEditor = () => {
  return {
    name: formItemName('supplierSelector'),
    hidden: booleanEditor('是否隐藏'),
    changeEvent: javascriptEditor('change事件', intellisense),
  } as Record<string, WidgetOptionEditorSetting>;
};
