{"name": "@hai-an/contract-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "main": "./src/index.ts", "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/file-download": "workspace:*", "@coder/fs-api": "workspace:^", "@coder/kkfile-preview": "workspace:*", "@coder/preview": "workspace:*", "@coder/swf-render": "workspace:*", "@coder/system-api": "workspace:*", "@coder/system-ui": "workspace:*", "@coder/vdesigner-core": "workspace:*", "@hai-an/contract-api": "workspace:*", "@hai-an/diary-api": "workspace:*", "@morev/vue-transitions": "catalog:coder", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/utils": "workspace:^", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "echarts": "catalog:", "lodash-es": "catalog:coder", "lru-cache": "catalog:coder", "vue": "catalog:", "vue-clipboard3": "catalog:coder", "vue-draggable-plus": "catalog:coder"}, "devDependencies": {"@types/lodash-es": "catalog:coder", "@vben/tsconfig": "workspace:*", "@vben/vite-config": "workspace:*"}}