<script setup lang="ts">
// 基础设置模块的主页面
import { useRouter } from 'vue-router';

const router = useRouter();

// 重定向到默认的子页面
const redirectToDefault = () => {
  router.push('/basic-setting/org');
};

// 页面加载时自动重定向
redirectToDefault();
</script>

<template>
  <div class="basic-setting-main">
    <div class="redirect-info">
      <p>正在跳转到基础设置页面...</p>
    </div>
  </div>
</template>

<style scoped>
.basic-setting-main {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
}

.redirect-info {
  color: #666;
  text-align: center;
}
</style>
