import type { IPlugin, RenderOptions } from '@coder/vdesigner-core';

import { useRouter } from 'vue-router';

import { usePlugins } from '@coder/vdesigner-core';
import antdv from '@coder/vdesigner-form-antdv';
import designer from '@coder/vdesigner-form-designer';
import render from '@coder/vdesigner-form-render';
import commonUI from '@coder/vdesigner-performer';
import AddAxiosPlugin from '@coder/vdesigner-plugins-axios';
import AddRouterPlugin from '@coder/vdesigner-plugins-router';
import HttpFile from '@coder/vdesigner-widget-http-file';
import printIdfWidget from '@coder/vdesigner-widget-print-pdf';

import { requestClient } from '#/api/request';

const install = (app: any) => {
  const option = {
    axios: () => {
      return requestClient.instance;
    },
    defaultImple: 'antd',
    request: requestClient,
  } as RenderOptions;

  const { register } = usePlugins();

  register({
    instance: requestClient.instance,
    name: 'axios',
    className: 'AxiosStatic|AxiosInstance',
  } as IPlugin);

  register({
    instance: () => useRouter(),
    name: 'router',
    className: 'Router',
  } as IPlugin);

  app
    .use(render, option)
    .use(antdv)
    .use(designer)
    .use(commonUI)
    .use(printIdfWidget)
    .use(HttpFile)
    .use(AddAxiosPlugin, () => requestClient.instance)
    .use(AddRouterPlugin);
};

export default install;
