<script setup lang="ts">
import type { FileListItem, FileSearcher } from '@hai-an/document-api';

import { onMounted, reactive, ref } from 'vue';

import { useLists } from '@vben/hooks';

import { SearchOutlined } from '@ant-design/icons-vue';
import { DownloadButton } from '@coder/file-download';
import { createFileApi } from '@hai-an/document-api';
import {
  Button as AButton,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  RangePicker as ARangePicker,
  Space as ASpace,
  Table as ATable,
} from 'ant-design-vue';

import { documentOptions } from '../';
import { columns } from './columns';
import PackageModal from './package-modal.vue';

const emit = defineEmits(['previewFile']);

const fileApi = createFileApi(documentOptions.request!, documentOptions.path);

const { listSearch, liseCount, getTicket } = fileApi;
const visible = ref(false);
const searchForm = reactive({
  name: '',
  deleteFlag: false,
  isManager: false,
  folderId: 0,
  createTimeStart: '',
  createTimeEnd: '',
} as FileSearcher); // as FileSearcher

const { getList, tableData, loading, pagination, changeTable, listIndex } =
  useLists({
    searchForm,
    listApi: listSearch,
    countApi: liseCount,
  });

const changeTime = (val: any) => {
  searchForm.createTimeStart = '';
  searchForm.createTimeEnd = '';
  if (val) {
    searchForm.createTimeStart = val[0];
    searchForm.createTimeEnd = val[1];
  }
};

const onBatchDownload = () => {
  visible.value = true;
};

const multipleSelection = ref<FileListItem[]>([]);
const multipleIds = ref<(number | string)[]>([]);

const onChange = (
  selectedRowKeys: (number | string)[],
  selectedRows: FileListItem[],
) => {
  multipleSelection.value = selectedRows;
  multipleIds.value = selectedRowKeys;
};
const headers = {
  Authorization: () =>
    documentOptions.getToken ? documentOptions.getToken() : '',
};
const getFileUrl = (item: FileListItem) => {
  return fileApi.getDownloadUrl(item.id);
};
const handlePreivew = async (item: FileListItem) => {
  // const fileApi = createFileApi(documentOptions.request, documentOptions.path);
  const url = ref(fileApi.getDownloadUrl(item.id));
  const fileTicketResult = await getTicket(item.id);
  const ticket = (fileTicketResult as any).data.ticket;
  const result = `${url.value}?_ctikt_=${ticket}&fullfilename=${item.name}`;

  emit('previewFile', result);
};

onMounted(() => {
  getList();
});
</script>

<template>
  <ACard>
    <AForm layout="inline" style="margin-bottom: 10px">
      <AFormItem label="文件名称">
        <AInput
          v-model:value="searchForm.name"
          placeholder="文件名称"
          allow-clear
        />
      </AFormItem>
      <AFormItem label="创建时间">
        <ARangePicker
          style="width: 100%"
          @change="changeTime"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </AFormItem>
      <AFormItem>
        <ASpace>
          <AButton @click="getList" type="primary">
            <template #icon> <SearchOutlined /> </template>
            查找
          </AButton>
        </ASpace>
      </AFormItem>
    </AForm>
  </ACard>
  <AButton
    class="mt-3"
    type="primary"
    @click="onBatchDownload"
    :disabled="multipleSelection.length === 0"
  >
    批量下载
  </AButton>
  <ACard class="mt-3">
    <ATable
      bordered
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      @change="changeTable"
      :row-selection="{ onChange }"
      row-key="id"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'index'">
          {{ listIndex(index) }}
          <!-- 注意这里要加1来从1开始计数 -->
        </template>
        <template v-if="column.key === 'action'">
          <ASpace>
            <DownloadButton
              :file-name="record.name"
              :url="getFileUrl(record as FileListItem)"
              :headers="headers"
            >
              下载
            </DownloadButton>
            <AButton @click="handlePreivew(record as FileListItem)">
              预 览
            </AButton>
          </ASpace>
        </template>
      </template>
    </ATable>
  </ACard>
  <PackageModal
    v-model:visible="visible"
    :files="multipleSelection"
    :file-ids="multipleIds"
  />
</template>

<style scoped></style>
