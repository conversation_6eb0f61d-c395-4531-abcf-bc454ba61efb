<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { useConfigStore } from '@vben/stores';

import { Button, Checkbox, Form, Input } from 'ant-design-vue';

import { getLoginConfig } from '#/api';
import { useAuthStore } from '#/store';

import VerifyCode from '../verify-code.vue';

const authStore = useAuthStore();

const FormItem = Form.Item;
const InputPassword = Input.Password;
const loginButton = '登陆';
const formRef = ref();
const verifyCodeKey = ref(1);

const REMEMBER_ME_KEY = `REMEMBER_ME_USERNAME_${location.hostname}`;
const localUsername = localStorage.getItem(REMEMBER_ME_KEY) || '';
const rememberMe = ref(!!localUsername);
const formData = reactive({
  username: '',
  password: '',
  verifyCode: '',
  verifyCodeId: '',
});

async function handleLogin() {
  formRef.value
    .validate()
    .then(async () => {
      if (rememberMe.value) {
        localStorage.setItem(
          REMEMBER_ME_KEY,
          rememberMe.value ? formData.username : '',
        );
      }
      try {
        await authStore.authLogin(formData);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('login-err:', error);
        verifyCodeKey.value++;
      }
    })
    .catch(() => {});
}

onMounted(() => {
  if (localUsername) {
    formData.username = localUsername;
  }

  authStore.loginLoading = true;
  getLoginConfig()
    .then((resp) => {
      if (resp.requireVerifyCode) {
        // verifyCodeUrl.value = '/api/auth/verify-code';

        useConfigStore().$patch({
          auth: {
            encryptPassword: resp.encryptPassword,
            // 是否启用密码加密
            requireVerifyCode: resp.requireVerifyCode,
          },
        } as any);
      }
    })
    .finally(() => {
      authStore.loginLoading = false;
    });
});
</script>

<template>
  <div>
    <h2
      class="enter-x mb-3 ml-4 text-center text-2xl font-bold xl:text-left xl:text-3xl"
    >
      登陆
    </h2>
    <Form
      class="enter-x p-4"
      :model="formData"
      ref="formRef"
      @keypress.enter="handleLogin"
    >
      <FormItem
        name="username"
        class="enter-x"
        :rules="[{ required: true, message: '请输入账号' }]"
      >
        <Input
          size="large"
          v-model:value="formData.username"
          placeholder="账号"
          class="fix-auto-fill"
        />
      </FormItem>
      <FormItem
        name="password"
        class="enter-x"
        :rules="[{ required: true, message: '请输入密码' }]"
      >
        <InputPassword
          size="large"
          visibility-toggle
          v-model:value="formData.password"
          placeholder="密码"
        />
      </FormItem>
      <FormItem
        name="verifyCode"
        class="enter-x"
        :rules="[{ required: true, message: '请输入验证码' }]"
      >
        <Input
          size="large"
          visibility-toggle
          v-model:value="formData.verifyCode"
          placeholder="请输入验证码"
        />
        <VerifyCode
          v-model="formData.verifyCodeId"
          verify-code-url="/api/auth/verify-code"
          :key="verifyCodeKey"
        />
      </FormItem>
      <FormItem>
        <Checkbox v-model:checked="rememberMe" size="small"> 记住我 </Checkbox>
      </FormItem>
      <FormItem class="enter-x">
        <Button
          type="primary"
          size="large"
          block
          @click="handleLogin"
          :loading="authStore.loginLoading"
          :disabled="authStore.loginLoading"
        >
          {{ loginButton }}
        </Button>
      </FormItem>
    </Form>
  </div>
</template>

<style scoped></style>
