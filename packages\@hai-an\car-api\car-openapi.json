{"openapi": "3.0.1", "info": {"title": "coder.car.service API", "version": "v1"}, "paths": {"/Car/{id}": {"get": {"tags": ["Car"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Car"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/Car/save": {"post": {"tags": ["Car"], "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CarSubmit"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CarSubmit"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CarSubmit"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CarSubmit"}}}}, "responses": {"200": {"description": "Success"}}}}, "/Car/list": {"get": {"tags": ["Car"], "parameters": [{"name": "CarNo", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "DriverPhone", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/Car/count": {"get": {"tags": ["Car"], "parameters": [{"name": "CarNo", "in": "query", "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "DriverPhone", "in": "query", "schema": {"type": "string"}}, {"name": "DeleteFlag", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"CarSubmit": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "carNo": {"type": "string", "nullable": true}, "driverName": {"type": "string", "nullable": true}, "driverPhone": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}