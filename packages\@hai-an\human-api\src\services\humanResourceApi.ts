import type { RequestClient } from '@vben/request';

import type {
  HumanResourceListItem,
  HumanResourceLogItem,
  HumanResourceSearcher,
  ResponseMessage,
} from '../types';

export const createHumanResourceApi = (
  request: RequestClient,
  _path: string | undefined,
) => {
  // const path = `/humanResource` + `/human/resource`;
  const path = `${_path}` + `/human/resource`;
  return {
    /**
     * 获取人力资源列表数据
     * @param searcher 搜索条件
     * @returns 人力资源列表数据
     */
    list(
      searcher: HumanResourceSearcher,
    ): Promise<ResponseMessage<HumanResourceListItem>> {
      return request.get(`${path}/list`, { params: searcher });
    },

    /**
     * 获取人力资源详情数据
     * @param humanResourceId 人力资源ID
     * @returns 人力资源详情数据
     */
    listDetail(
      humanResourceId: string,
    ): Promise<ResponseMessage<HumanResourceLogItem>> {
      return request.get(`${path}/log/listDetail`, {
        params: { humanResourceId },
      });
    },
    /**
     * 导出人力资源数据
     * @param searcher 导出参数
     * @returns Excel文件二进制数据
     */
    export(searcher: HumanResourceSearcher): Promise<Blob> {
      return request.get(`${path}/export`, {
        params: searcher,
        responseType: 'blob',
      });
    },
    /**
     * 导出人力资源数据
     * @param searcher 导出参数
     * @returns Excel文件二进制数据
     */
    exportDetail(searcher: HumanResourceSearcher): Promise<Blob> {
      return request.get(`${path}/export/detail`, {
        params: searcher,
        responseType: 'blob',
      });
    },
    /**
     * 导入年假数据
     * @param formData 包含Excel文件的表单数据
     * @returns 导入结果
     */
    importAnnualLeave(formData: FormData): Promise<any> {
      return request.post(`${path}/annual/leave/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },
    /**
     * 导出年假导入模板
     * @returns Excel模板文件二进制数据
     */
    exportTemplate(): Promise<Blob> {
      return request.get(`${path}/annual/leave/export`, {
        responseType: 'blob',
        headers: {
          Accept:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      });
    },
  };
};
