<script setup lang="ts">
import { computed } from 'vue';
import { preferences } from '@vben/preferences';
const props = defineProps({
  showTitle: { type: Boolean, default: true },
  alwaysShowTitle: { type: Boolean },
});
const logo = computed(() => preferences.logo.source);

const title = '海安OA系统';
const prefixCls = 'app-logo';

const getAppLogoClass = computed(() => [
  prefixCls,
  { 'collapsed-show-title': true },
]);

const getTitleClass = computed(() => [ 
  `${prefixCls}__title`,
  {
    'xs:opacity-0': !props.alwaysShowTitle,
  },
]);
const goHome = () => {};
</script>

<template>
  <div class="anticon" :class="getAppLogoClass" @click="goHome">
    <img
      :src="logo"
      :class="!props.alwaysShowTitle ? 'large' : 'small'"
    />
    <div
      class="ml-2 truncate md:opacity-100"
      :class="getTitleClass"
      v-show="showTitle"
      :style="{ fontSize: !props.alwaysShowTitle ? '24px' : '16px' }"
    >
      {{ title }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.large {
  width: 48px;
  height: 48px;
}

.small {
  width: 32px;
  height: 32px;
}

.app-logo {
  display: flex;
  align-items: center;
  padding-left: 7px;
  cursor: pointer;
  transition: all 0.2s ease;

  &.collapsed-show-title {
    padding-left: 20px;
    margin-top: 20px;
  }

  &__title {
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    transition: all 0.5s;
  }
}
</style>
