import type { FolderListItem, FolderSubmit } from '@hai-an/document-api';
import type { DropdownOption, TreeOption } from 'naive-ui';

import { ref } from 'vue';

import { createIconifyIcon } from '@vben/icons';

import { createFolderApi } from '@hai-an/document-api';
import { Form, FormItem, Input, Modal } from 'ant-design-vue';

import { documentOptions } from '../../..';

const CreateIcon = createIconifyIcon('ant-design:plus-outlined');
const DeleteIcon = createIconifyIcon('ant-design:delete-outlined');
const RefreshIcon = createIconifyIcon('ant-design:redo-outlined');
const CarcodeIcon = createIconifyIcon('ant-design:select-outlined');
const EditIcon = createIconifyIcon('ant-design:edit-outlined');
const CopyIcon = createIconifyIcon('ant-design:copy-outlined');
const DownloadIcon = createIconifyIcon('ant-design:download-outlined');
const SetPermissionIcon = createIconifyIcon('ant-design:lock-outlined');

const showCreateModal = (
  folder: FolderListItem,
  onSuccess?: (id: number) => void,
) => {
  const newFolderName = ref('');
  const showDialog = ref(true);
  const save = () => {
    try {
      const folderApi = createFolderApi(
        documentOptions.request as any,
        documentOptions.path,
      );
      folderApi
        .save({
          name: newFolderName.value,
          parentFolderId: folder.id as number,
        } as FolderSubmit)
        .then((resp) => {
          onSuccess && onSuccess(resp.id);
          newFolderName.value = '';
          showDialog.value = false;
        });
    } catch (error) {
      console.error('Failed to create folder:', error);
    }
  };

  Modal.confirm({
    content: () => (
      <Form>
        <FormItem
          name="newFolderName"
          rules={[{ message: '请输入目录名称', required: true }]}
        >
          <Input placeholder="目录名称" v-model:value={newFolderName.value} />
        </FormItem>
      </Form>
    ),
    onOk: save,
    title: '创建目录',
  });
};

const showRenameModal = (
  folder: FolderListItem,
  onSuccess?: (id: number) => void,
) => {
  const newName = ref(folder.name);

  Modal.confirm({
    content: () => (
      <Form>
        <FormItem
          name="newName"
          rules={[{ message: '请输入新名称', required: true }]}
        >
          <Input placeholder="新名称" v-model:value={newName.value} />
        </FormItem>
      </Form>
    ),
    onOk: async () => {
      try {
        const folderApi = createFolderApi(
          documentOptions.request as any,
          documentOptions.path,
        );
        const resp = await folderApi.save({
          id: folder.id,
          name: newName.value,
          parentFolderId: folder.parentId as number,
        } as FolderSubmit);
        onSuccess && onSuccess(resp.id);
      } catch (error) {
        console.error('Failed to rename folder:', error);
      }
    },
    title: '重命名',
  });
};
const showCopyModal = (
  folder: FolderListItem,
  onSuccess?: (id: number) => void,
) => {
  const copyFolderName = ref('');
  const showDialog = ref(true);
  const copy = () => {
    try {
      const folderApi = createFolderApi(
        documentOptions.request as any,
        documentOptions.path,
      );
      folderApi
        .save({
          name: copyFolderName.value,
          parentFolderId: folder.parentFolderId as number,
          copyFolderId: folder.id as number,
        } as FolderSubmit)
        .then((resp) => {
          onSuccess && onSuccess(resp.id);
          copyFolderName.value = '';
          showDialog.value = false;
        });
    } catch (error) {
      console.error('Failed to copy folder:', error);
    }
  };

  Modal.confirm({
    content: () => (
      <Form>
        <FormItem
          name="copyFolderName"
          rules={[{ message: '请输入目录名称', required: true }]}
        >
          <Input placeholder="目录名称" v-model:value={copyFolderName.value} />
        </FormItem>
      </Form>
    ),
    onOk: copy,
    title: '复制目录',
  });
};
export const useContextMenu = () => {
  const selectedNode = ref<null | TreeOption>(null);
  const menuX = ref(0);
  const menuY = ref(0);
  const showDropdown = ref(false);

  const dropdownOptions = [
    {
      icon() {
        return <DeleteIcon />;
      },
      key: 'delete',
      label: '删除目录',
    },
    {
      icon() {
        return <CreateIcon />;
      },
      key: 'create',
      label: '创建目录',
    },
    {
      icon() {
        return <CopyIcon />;
      },
      key: 'copy',
      label: '复制目录',
    },
    {
      icon() {
        return <EditIcon />;
      },
      key: 'rename',
      label: '重命名',
    },
    {
      key: 'dividier1',
      type: 'divider',
    },
    {
      icon() {
        return <RefreshIcon />;
      },
      key: 'refresh',
      label: '刷新',
    },
    {
      icon() {
        return <CarcodeIcon />;
      },
      key: 'properties',
      label: '详细信息',
    },
    {
      icon() {
        return <SetPermissionIcon />;
      },
      key: 'setpermission',
      label: '权限分配',
    },
    {
      icon() {
        return <DownloadIcon />;
      },
      key: 'download',
      label: '下载',
    },
  ] as DropdownOption[];

  const handleClickoutside = () => {
    showDropdown.value = false;
  };

  const handleContextMenu = (e: MouseEvent, node: TreeOption) => {
    selectedNode.value = node;
    showDropdown.value = true;
    menuX.value = e.clientX;
    menuY.value = e.clientY;
    e.preventDefault();
  };

  return {
    dropdownOptions,
    handleClickoutside,
    handleContextMenu,
    menuX,
    menuY,
    selectedNode,
    showCreateModal,
    showDropdown,
    showRenameModal,
    showCopyModal,
  };
};
