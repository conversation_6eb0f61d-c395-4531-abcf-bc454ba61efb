import type { RequestClient } from '@vben/request';

import type {
  Exchange,
  FolderListItem,
  FolderSearcher,
  FolderSubmit,
  FolderViewModel,
  ResponseMessage,
  UpdateMessage,
} from '../types';

export const createFolderApi = (request: RequestClient, basePath: string) => {
  const path = `${basePath}/Folder`;

  const count = async (searcher: FolderSearcher): Promise<number> => {
    const data = await request.get(`${path}/count-by-parent`, {
      params: searcher,
    });
    return data;
  };

  const deleteFolder = async (id: number): Promise<ResponseMessage> => {
    const data = await request.delete(`${path}/${id}`);
    return data;
  };

  const exchange = async (param: Exchange): Promise<void> => {
    const data = await request.get(`${path}/exchange`, { params: param });
    return data;
  };

  const exist = async (param: {
    name: string;
    parentId: number;
  }): Promise<boolean> => {
    const data = await request.get(`${path}/exist`, { params: param });
    return data;
  };

  const getById = async (id: number): Promise<FolderViewModel> => {
    const data = await request.get(`${path}/${id}`);
    return data;
  };

  const list = async (searcher: FolderSearcher): Promise<FolderListItem[]> => {
    const data = await request.get(`${path}/list-by-parent`, {
      params: searcher,
    });
    return data;
  };

  const buildPack = async (folderId: number): Promise<ResponseMessage> => {
    const data = await request.get(`${path}/build-pack/${folderId}`);
    return data;
  };

  const save = async (param: FolderSubmit): Promise<UpdateMessage> => {
    const data = await request.post(`${path}/save`, param);
    return data;
  };

  return {
    buildPack,
    count,
    deleteFolder,
    exchange,
    exist,
    getById,
    save,
    list,
  };
};
