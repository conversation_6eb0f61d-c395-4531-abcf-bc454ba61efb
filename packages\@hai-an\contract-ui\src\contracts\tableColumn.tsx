import type { ContractListItem } from '@hai-an/contract-api';
import type { ColumnType } from 'ant-design-vue/es/table';

import { EditOutlined, LinkOutlined } from '@ant-design/icons-vue';
import { UserLabel } from '@coder/system-ui';
import { Button, Tag } from 'ant-design-vue';

import { dayF } from '../util';
import {
  showDetail,
  showEditorPanel,
  showLinkContractPanel,
} from './useAction';

export const makeColumns = (isEdit = true) => {
  return [
    {
      align: 'center',
      dataIndex: 'action',
      title: isEdit ? '操作' : '序号',
      width: 150,
      customRender: ({ record, index }) => {
        if (!isEdit) return <span>{index + 1}</span>;
        const contract = record as ContractListItem;
        return (
          <div>
            <Button
              onClick={() => {
                showEditorPanel(contract);
              }}
              type="link"
            >
              <EditOutlined />
              修改
            </Button>
            <Button
              onClick={() => {
                showLinkContractPanel(contract);
              }}
              type="link"
            >
              <LinkOutlined />
              关联合同
            </Button>
          </div>
        );
      },
    },
    {
      dataIndex: 'code',
      title: '合同编号',
      width: 150,
      customRender: ({ record }) => {
        const contract = record as ContractListItem;
        return (
          <a onClick={() => showDetail(contract)} style="color:#006be6;">
            {contract.code}
          </a>
        );
      },
    },
    {
      dataIndex: 'name',
      title: '合同名称',
    },
    {
      dataIndex: 'projectArchiveCode',
      title: '项目编码',
    },
    {
      dataIndex: 'projectArchiveName',
      title: '项目名称',
    },
    {
      dataIndex: 'contractType',
      title: '合同类型',
      customRender: ({ record }) => {
        return (
          <Tag color={record.contractType ? 'green' : 'geekblue'}>
            {record.contractType ? '付款合同' : '收款合同'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'projectName',
      title: '合同项目名称',
    },
    {
      dataIndex: 'orderNo',
      title: '工单编号',
      width: 150,
    },
    {
      dataIndex: 'oppositeName',
      title: '对方名称',
    },
    {
      dataIndex: 'orgPath',
      title: '所属组织机构',
    },
    {
      dataIndex: 'isDeleted',
      title: '状态',
      customRender({ record }) {
        return (
          <Tag color={record.isDeleted ? 'volcano' : 'green'}>
            {record.isDeleted ? '已删除' : '正常'}
          </Tag>
        );
      },
    },
    {
      customRender({ text }) {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'planBookDate',
      title: '预计签订日期',
    },
    {
      customRender({ text }) {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'bookDate',
      title: '签订日期',
    },
    {
      dataIndex: 'serviceShip',
      title: '提供服务的船舶',
    },
    {
      dataIndex: 'contractPrice',
      title: '合同单价',
    },
    {
      dataIndex: 'contractTotal',
      title: '合同总价',
    },
    {
      customRender({ text }) {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'applyDate',
      title: '申请时间',
    },
    {
      customRender({ text }) {
        return <span>{dayF(text)}</span>;
      },
      dataIndex: 'orderCloseDate',
      title: '闭单时间',
    },
    {
      dataIndex: 'createBy',
      title: '创建人',
      customRender: ({ text }) => {
        <UserLabel userName={text}></UserLabel>;
      },
    },
    {
      dataIndex: 'payCount',
      title: '合同进度',
      customRender({ text }) {
        return <span>已完成{text}期</span>;
      },
    },
  ] as ColumnType[];
};
