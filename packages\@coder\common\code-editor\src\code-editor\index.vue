<script setup lang="ts">
import type { CodeProvider } from '@coder/monaco-editor-builder';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

import { BarsOutlined } from '@ant-design/icons-vue';
import { MonacoEditor, useBuilder } from '@coder/monaco-editor-builder';
import { Toolbar, ToolbarButton } from '@coder/toolbar';

const props = defineProps<{
  code?: string | undefined;
  readonly?: boolean;
  style?: any | string | undefined;
  switchKey?: string;
  type?: 'css' | 'html' | 'javascript' | 'json' | 'yaml' | undefined;
}>();

const emits = defineEmits<{
  (e: 'focus'): void;
  (e: 'update:code', code: string | undefined): void;
}>();

const generateId = () => Math.random().toString(36).slice(-6);

const monacoBuilder = useBuilder();
let editor: MonacoEditor | null = null;
let codeModel: any;
const editr2Ref = ref();
let _code: string | undefined;
const codeProvider = {
  get: () => _code ?? '',
  set: (v) => {
    _code = v;
  },
} as CodeProvider;

const langType = computed(() => {
  return props.type ?? 'json';
});

onMounted(() => {
  _code = props.code ?? '';
  editor = monacoBuilder.build(editr2Ref.value);
  codeModel = editor.setCode(
    props.switchKey ?? generateId(),
    codeProvider,
    true,
    langType.value,
  );
  editor.editor.onDidFocusEditorText(() => {
    editor?.readonly(props.readonly === true);
    emits('focus');
  });
  editor.editor.onDidBlurEditorText(() => {
    emits('update:code', _code);
  });
});

watch(
  () => props.code,
  () => {
    editor?.updateCode(props.code ?? '');
  },
);

onUnmounted(() => {
  const ed = editor as MonacoEditor;
  ed?.editor?.dispose();
  codeModel?.dispose();
});

defineExpose({
  format: () => {
    editor?.formatCode();
  },
  hightLight: (len: number) => {
    editor?.hightLine(len, 'hight-line-Code');
  },
  updateCode: (code: string) => {
    editor?.updateCode(code);
  },
});
</script>
<template>
  <div :style="props.style" class="editor-container">
    <Toolbar>
      <ToolbarButton @click="() => editor?.formatCode()" title="格式化">
        <BarsOutlined />
      </ToolbarButton>
    </Toolbar>
    <div ref="editr2Ref" class="editor"></div>
  </div>
</template>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.editor {
  flex-grow: 1;
  min-height: 450px;
}

.toolbar {
  margin-bottom: 2px;
  font-size: 12px;
  text-align: left;
  background-color: #f5f5f5;
  background-image: linear-gradient(to top, #f5f5f5, #eee);
  border-top: 1px solid #cbcccc;
  box-shadow: inset 0 1px 0 0 #fff;
}
</style>
<style>
.hight-line-Code {
  color: red !important;
  background-color: rgb(101 0 93);
}
</style>
