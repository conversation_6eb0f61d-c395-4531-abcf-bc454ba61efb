{"name": "@vben/web-antd", "version": "5.5.7", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-antd"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@ant-design/icons-vue": "catalog:coder", "@coder/common-api": "workspace:*", "@coder/fs-ui": "workspace:^", "@coder/notify-api": "workspace:^", "@coder/notify-ui": "workspace:^", "@coder/page-api": "workspace:*", "@coder/page-designer": "workspace:*", "@coder/page-render": "workspace:*", "@coder/preview": "workspace:*", "@coder/print-pdf": "workspace:^", "@coder/rich-editor": "workspace:^", "@coder/swf-api": "workspace:^", "@coder/swf-designer": "workspace:^", "@coder/swf-render": "workspace:^", "@coder/swf-ui": "workspace:*", "@coder/system-api": "workspace:*", "@coder/system-ui": "workspace:*", "@coder/vdesigner-core": "workspace:^", "@coder/vdesigner-form-antdv": "workspace:*", "@coder/vdesigner-form-designer": "workspace:^", "@coder/vdesigner-form-render": "workspace:^", "@coder/vdesigner-performer": "workspace:*", "@coder/vdesigner-plugins-axios": "workspace:*", "@coder/vdesigner-plugins-router": "workspace:*", "@coder/vdesigner-widget-http-file": "workspace:*", "@coder/vdesigner-widget-print-pdf": "workspace:^", "@hai-an/car-api": "workspace:*", "@hai-an/car-ui": "workspace:*", "@hai-an/contract-api": "workspace:^", "@hai-an/contract-ui": "workspace:^", "@hai-an/diary-api": "workspace:*", "@hai-an/human-api": "workspace:*", "@hai-an/document-ui": "workspace:^", "@hai-an/oa-api": "workspace:*", "@hai-an/oa-ui": "workspace:*", "@hai-an/vdesigner-oa": "workspace:*", "@iconify/vue": "catalog:", "@vben-core/shadcn-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash-es": "catalog:coder", "monaco-editor": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-devui": "catalog:coder", "vue-router": "catalog:"}, "devDependencies": {"@types/lodash-es": "catalog:coder"}}