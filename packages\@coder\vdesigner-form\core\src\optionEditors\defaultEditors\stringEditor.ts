import type { ValueFrom, WidgetOptionEditorSetting } from '../../widget-define';

/**
 * 字符串默认编辑器
 * @param label 标签
 * @param order 排序
 * @param valueFrom 值来源 "cache" | "code" | "dataSource" | "formData" | "value"
 * @returns WidgetOptionEditor
 */
export const stringEditor = (
  label: string,
  order?: number,
  valueFrom?: ValueFrom[],
) => {
  return {
    editorOption: {
      type: 'CoderEditorString',
    },
    label,
    order: order ?? 0,
    valueFrom,
  } as WidgetOptionEditorSetting;
};
