export interface DiaryItems {
  businessId?: string;
  businessName?: null | string;
  content: string;
  createBy?: null | string;
  createTime?: null | string;
  delFlag?: null | string;
  diaryTime?: null | string;
  diaryType?: null | string;
  fileList?: Array<any>;
  id: number | string;
  title?: string;
  updateBy?: null | string;
  updateTime?: null | string;
}

export interface DiarySearch {
  businessId?: string;
  businessName?: string;
  content?: string;
  diaryMonth?: string;
  diaryType?: string;
  page?: number;
  pageSize?: number;
  title?: string;
}

export interface DiaryFormData {
  businessId?: string;
  businessName?: number | string;
  content?: string;
  diaryTime?: string;
  diaryType?: string;
  fileList?: any;
  id?: string;
  title?: string;
}
