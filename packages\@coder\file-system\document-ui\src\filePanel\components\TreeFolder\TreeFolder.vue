<script lang="ts" setup>
import type { FolderSearcher } from '@coder/document-api';
import type { TreeDropInfo, TreeOption } from 'naive-ui';

import { onMounted, ref } from 'vue';

import { createFolderApi } from '@coder/document-api';
import { Button } from 'ant-design-vue';
import { NDropdown, NTree } from 'naive-ui';

import { documentOptions } from '../../..';
import FolderProperty from '../FolderProperty/index.vue';
import { useContextMenu } from './useContextMenu';

const emit = defineEmits<{
  (e: 'select', id: number): void;
}>();

const treeData = ref<TreeOption[]>([]);
const defaultExpandedKeys = ref<string[]>([]);
const folderPropertyRef = ref();
const selectedKeys = ref<any[]>([]);
const {
  dropdownOptions,
  handleClickoutside,
  handleContextMenu,
  menuX,
  menuY,
  selectedNode,
  showCreateModal,
  showDropdown,
  showRenameModal,
} = useContextMenu();

// Handle lazy loading of child nodes
const loadChild = async (parenNode: TreeOption) => {
  try {
    const folderApi = createFolderApi(
      documentOptions.request!,
      documentOptions.path,
    );
    const children = await folderApi.list({
      parentFolderId: parenNode.id,
    } as FolderSearcher);

    parenNode.children = children.map((item) => ({
      ...item,
      isLeaf: false,
    }));
    parenNode.children = [...parenNode.children];
  } catch (error) {
    console.error('Failed to load child nodes:', error);
  }
};

// Load folder data
const loadRoot = async () => {
  treeData.value = [];
  try {
    const folderApi = createFolderApi(
      documentOptions.request!,
      documentOptions.path,
    );
    const data = await folderApi.list({ parentId: 0 });
    treeData.value = data.map((item) => ({
      ...item,
      isLeaf: false,
    }));
  } catch (error) {
    console.error('Failed to load folder data:', error);
  }
};

const handleDropdownSelect = async (key: string) => {
  showDropdown.value = false;
  if (key === 'properties' && selectedNode.value) {
    folderPropertyRef.value?.show();
  } else if (key === 'create') {
    showCreateModal(selectedNode.value, async (id: number) => {
      await loadChild(selectedNode.value as any);
      const createdNode =
        selectedNode.value &&
        selectedNode.value.children &&
        (selectedNode.value.children.find(
          (item: any) => item.id === id,
        ) as any);

      if (createdNode && createdNode.id) {
        selectedKeys.value = [createdNode.id];
        emit('select', createdNode.id);
      }
    });
  } else if (key === 'rename' && selectedNode.value) {
    showRenameModal(selectedNode.value, async () => {
      // 如果重命名的是根节点，刷新整个树
      if (selectedNode.value?.parentFolderId === 0) {
        await loadRoot();
      } else {
        // 否则刷新父节点
        const parentNode = treeData.value.find(
          (node) => node.id === selectedNode.value?.parentFolderId,
        );
        if (parentNode) {
          await loadChild(parentNode);
        }
      }
    });
  } else if (key === 'refresh' && selectedNode.value) {
    // 如果是根节点，刷新整个树
    // eslint-disable-next-line unicorn/prefer-ternary
    if (selectedNode.value.parentFolderId === 0) {
      await loadRoot();
    } else {
      // 否则刷新当前节点
      await loadChild(selectedNode.value);
    }
  }
};

// Handle node selection
const handleSelect = (keys: number[]) => {
  const id = keys[0];
  if (id === undefined) {
    return;
  }
  emit('select', id);
};

const nodesProps = ({ option }: { option: TreeOption }) => {
  return {
    onContextmenu(e: MouseEvent): void {
      handleContextMenu(e, option);
    },
  };
};

onMounted(() => {
  loadRoot();
});

const handleDrop = ({ dragNode, dropPosition, node }: TreeDropInfo) => {
  const folderApi = createFolderApi(
    documentOptions.request!,
    documentOptions.path,
  );

  const reOrder = () => {
    folderApi
      .exchange({
        aId: dragNode.id as any,
        bId: node.id as any,
      })
      .then(() => {
        if (dragNode.parentFolderId === 0) {
          loadRoot();
        } else {
          const aryPathes = (dragNode.namePath as string).split('/');
          aryPathes.pop();
          let parent: any;
          do {
            const path = aryPathes.shift();
            parent = treeData.value.find((item) => item.name === path);
          } while (aryPathes.length > 0);

          if (parent) loadChild(parent);
        }
      });
  };

  switch (dropPosition) {
    case 'after': {
      reOrder();
      break;
    }
    case 'before': {
      reOrder();
      break;
    }
    case 'inside': {
      if (dragNode.parentFolderId === node.parentFolderId) {
        reOrder();
      }
      break;
    }
    // No default
  }
};
</script>

<template>
  <NDropdown
    :options="dropdownOptions"
    :show="showDropdown"
    :x="menuX"
    :y="menuY"
    placement="bottom-start"
    trigger="manual"
    @clickoutside="handleClickoutside"
    @select="handleDropdownSelect"
  />
  <Button @click="loadRoot">刷新</Button>
  <NTree
    v-model:selected-keys="selectedKeys"
    :data="treeData"
    :default-expanded-keys="defaultExpandedKeys"
    :node-props="nodesProps"
    :on-load="loadChild"
    block-line
    draggable
    key-field="id"
    label-field="name"
    remote
    @drop="handleDrop"
    @update:selected-keys="handleSelect"
  />
  <FolderProperty ref="folderPropertyRef" :folder="selectedNode" />
</template>
