<script setup lang="ts">
import { computed, ref, watch } from 'vue';

import {
  FileZipOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue';
import { Toolbar, ToolbarButton, ToolbarGroup } from '@coder/toolbar';
import {
  Breadcrumb as ABreadcrumb,
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Table,
} from 'ant-design-vue';

import PackageModal from '../../../fileSearch/package-modal.vue';
import UploadButton from './_upload-buttom.vue';
import { createColumn } from './infoColumn';
import { useFileTable } from './useFileTable';

const props = defineProps<{
  folderId: number;
}>();

const emit = defineEmits(['previewFile', 'selectNode']);

const {
  handleTableChange,
  loadTableData,
  loadFolderViewModel,
  tableData,
  tableLoading,
  tablePagination,
  uploadFile,
  folderViewModel,
  fileSearcher,
} = useFileTable(props);
// 临时解决方案：使用 any 类型来绕过类型检查
const columns = (createColumn as any)(
  (url: string) => {
    // 预览文件的逻辑，可以在这里打开新窗口或处理文件预览
    emit('previewFile', url);
  },
  () => {
    loadTableData();
  },
);

watch(
  () => props.folderId,
  () => {
    fileSearcher.folderId = props.folderId;
    loadTableData();
    loadFolderViewModel();
  },
);

const onSelectFile = (file: File) => {
  uploadFile(file);
};
const multipleSelection = ref<FileListItem[]>([]);
const multipleIds = ref<(number | string)[]>([]);

const onChange = (
  selectedRowKeys: (number | string)[],
  selectedRows: FileListItem[],
) => {
  multipleSelection.value = selectedRows;
  multipleIds.value = selectedRowKeys;
};
const onRefresh = () => {
  loadTableData();
};

const visible = ref(false);
const onBatchDownload = () => {
  visible.value = true;
};

const folderPaths = computed(() => {
  if (!folderViewModel || !folderViewModel.namePath || !folderViewModel.idPath)
    return [];
  const names = folderViewModel.namePath.split('/');
  const ids = folderViewModel.idPath.split('/');
  return names.map((name, idx) => ({
    path: ids[idx],
    breadcrumbName: name,
  }));
});

const onBreadcrumbClick = (route: any) => {
  emit('selectNode', route.path);
};
</script>

<template>
  <div class="table-container">
    <div style="margin-bottom: 10px">
      <ABreadcrumb v-if="folderPaths.length > 0" :routes="folderPaths">
        <template #itemRender="{ route }">
          <span style="cursor: pointer" @click="onBreadcrumbClick(route)">
            {{ route.breadcrumbName }}
          </span>
        </template>
      </ABreadcrumb>
      <div class="form-toolbar-row">
        <Card>
          <Form :model="fileSearcher" layout="inline">
            <FormItem label="文件名称">
              <Input
                v-model:value="fileSearcher.name"
                placeholder="请输入文件名"
              />
            </FormItem>
            <FormItem>
              <Button type="primary" @click="loadTableData">
                <SearchOutlined /> 查询
              </Button>
            </FormItem>
          </Form>
        </Card>
        <Toolbar>
          <ToolbarGroup>
            <UploadButton @select="onSelectFile" />
          </ToolbarGroup>
          <ToolbarGroup>
            <ToolbarButton @click="onRefresh">
              <ReloadOutlined />
            </ToolbarButton>
            <ToolbarButton>
              <FileZipOutlined />
            </ToolbarButton>
          </ToolbarGroup>
        </Toolbar>
      </div>
    </div>
    <Button
      class="mt-3"
      type="primary"
      @click="onBatchDownload"
      :disabled="multipleSelection.length === 0"
    >
      批量下载
    </Button>
    <Card class="mt-3">
      <Table
        bordered
        :columns="columns"
        :data-source="tableData"
        :loading="tableLoading"
        :pagination="tablePagination"
        @change="handleTableChange"
        :row-selection="{ onChange }"
        row-key="id"
      />
    </Card>
    <PackageModal
      v-model:visible="visible"
      :files="multipleSelection"
      :file-ids="multipleIds"
    />
  </div>
</template>

<style scoped>
.table-container {
  height: 100%;
  padding: 12px;
  overflow: auto;
}

.folder-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.form-toolbar-row {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  width: 60%;
}

.form-toolbar-row .ant-card {
  flex: 1 1 auto;
  margin-bottom: 0;
}

.form-toolbar-row .ant-toolbar {
  flex-shrink: 0;
}
</style>
