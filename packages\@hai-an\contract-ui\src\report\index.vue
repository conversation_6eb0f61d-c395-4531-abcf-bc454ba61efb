<script setup lang="ts">
import type { ReportContractSearcher } from '@hai-an/contract-api/src/types/report';

import { ref } from 'vue';

import {
  createReportApi,
  haianContractOption as options,
} from '@hai-an/contract-api';
import {
  Button as AButton,
  Card as ACard,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Modal as AModal,
  RangePicker as ARangePicker,
  Select as ASelect,
  Space as ASpace,
  message,
} from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import filelist from './filelist.vue';

const api = createReportApi(options.request, options.path);

type RangeValue = [Dayjs, Dayjs];
const reportDatetemp = ref<RangeValue>();
const dayF = (val: any) => {
  return dayjs(val).format('YYYY-MM-DD');
};
const formSubmitReport = ref<ReportContractSearcher & { type: string }>({
  code: '',
  contractType: null,
  createBy: '',
  departmentType: '',
  isAllData: false,
  isInRole: false,
  isOrgData: false,
  oppositeName: '',
  orgName: '',
  orgPaths: [],
  projectName: '',
  reportDateEnd: '',
  reportDateStar: '',
  type: '',
});
const reportDateChange = (val: any) => {
  formSubmitReport.value.reportDateStar = '';
  formSubmitReport.value.reportDateEnd = '';
  if (val) {
    formSubmitReport.value.reportDateStar = dayF(val[0]);
    formSubmitReport.value.reportDateEnd = dayF(val[1]);
  }
};

const dialogVisible = ref(false);

const showDialog = (type: string) => {
  dialogVisible.value = true;
  formSubmitReport.value.type = type;
  formSubmitReport.value.orgName = '';
  formSubmitReport.value.reportDateStar = '';
  formSubmitReport.value.reportDateEnd = '';
  formSubmitReport.value.departmentType = '';
  formSubmitReport.value.oppositeName = '';
  formSubmitReport.value.projectName = '';
  reportDatetemp.value = undefined;
};
const buildReport = (type: string) => {
  let promiseType: any = null;
  switch (type) {
    case '付款合同报表': {
      promiseType = api.getPayReport(formSubmitReport.value);
      break;
    }
    case '应收账款明细表': {
      promiseType = api.getReportReceiveInfo();
      break;
    }
    case '收款合同报表': {
      promiseType = api.getContractReceive(formSubmitReport.value);
      break;
    }
    case '经营收付款合同对应表': {
      promiseType = api.getReportReceivePay();
      break;
    }
  }
  if (promiseType) {
    promiseType.then((resp: any) => {
      message.info(resp.message);
      dialogVisible.value = false;
    });
  }
};
const departmentTypes = ref([
  {
    label: '请选择',
    value: '',
  },
  {
    label: '经营',
    value: '经营',
  },
  {
    label: '综合',
    value: '综合',
  },
  {
    label: '办公室',
    value: '办公室',
  },
  {
    label: '财审',
    value: '财审',
  },
  {
    label: '商务',
    value: '商务',
  },
  {
    label: '科技',
    value: '科技',
  },
  {
    label: '水工',
    value: '水工',
  },
  {
    label: '船务',
    value: '船务',
  },
]);
</script>

<template>
  <ACard>
    <!-- OAContract 是由服务端的 tokenService 配置段的client决定的 -->

    <AForm layout="inline">
      <AFormItem>
        <ASpace>
          <AButton @click="showDialog('付款合同报表')"> 付款合同报表 </AButton>
          <AButton @click="showDialog('收款合同报表')"> 收款合同报表 </AButton>
          <AButton @click="buildReport('应收账款明细表')">
            应收账款明细表
          </AButton>
          <AButton @click="buildReport('经营收付款合同对应表')">
            经营收付款合同对应表
          </AButton>
        </ASpace>
      </AFormItem>
    </AForm>

    <filelist />

    <AModal v-model:open="dialogVisible">
      <ACard>
        <AForm :label-col="{ span: 6 }">
          <AFormItem label="签订合同日期">
            <ARangePicker
              v-model:value="reportDatetemp"
              style="width: 100%"
              @change="reportDateChange"
            />
          </AFormItem>
          <AFormItem label="公司/部门">
            <AInput v-model:value="formSubmitReport.orgName" />
          </AFormItem>
          <AFormItem label="合同机构类型">
            <ASelect
              v-model:value="formSubmitReport.departmentType"
              style="width: 120px"
              :options="departmentTypes"
            />
          </AFormItem>
          <AFormItem label="第三方名称">
            <AInput v-model:value="formSubmitReport.oppositeName" />
          </AFormItem>
          <AFormItem label="项目名称">
            <AInput v-model:value="formSubmitReport.projectName" />
          </AFormItem>
        </AForm>
      </ACard>
      <template #footer>
        <AButton @click="buildReport(formSubmitReport.type)">创建</AButton>
        <AButton @click="dialogVisible = false">关闭</AButton>
      </template>
    </AModal>
  </ACard>
</template>
